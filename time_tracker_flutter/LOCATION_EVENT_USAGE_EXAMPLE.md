# Location Event System Usage Examples

The location tracking system has been completely rewritten to use event-based communication instead of polling. This provides real-time updates and better performance.

## Key Components

### 1. LocationService - Event Streams

The `LocationService` now provides two main streams:

```dart
final LocationService locationService = LocationService();

// Listen to specific location events
locationService.eventStream.listen((event) {
  switch (event.type) {
    case LocationEventType.areaEntered:
      print('Entered ${event.areaName} for project ${event.projectName}');
      break;
    case LocationEventType.areaExited:
      print('Exited ${event.areaName}');
      break;
    case LocationEventType.trackingStarted:
      print('Location tracking started');
      break;
    case LocationEventType.trackingStopped:
      print('Location tracking stopped');
      break;
  }
});

// Listen to tracking status changes
locationService.statusStream.listen((status) {
  print('Tracking active: ${status.isActive}');
  print('Active areas: ${status.activeTimeEntries.length}');
  print('Current project: ${status.currentProjectName}');
});
```

### 2. LocationTrackingWidget - Wrap Any Widget

Wrap any widget to add location tracking capabilities:

```dart
// Method 1: Using extension
MyProjectScreen().withLocationTracking(projectId: project.id)

// Method 2: Direct widget
LocationTrackingWidget(
  projectId: project.id,
  child: MyProjectScreen(),
)
```

This provides:
- Real-time snack bar notifications for area enter/exit
- Visual tracking indicator in top-right corner
- Automatic duration updates

### 3. ProjectTrackingStatus - Project-Specific Status

Show tracking status for a specific project:

```dart
ProjectTrackingStatus(
  projectId: project.id,
  projectName: project.name,
)
```

This shows:
- Green indicator when project is being tracked
- Location name where tracking is happening  
- Real-time duration counter

### 4. TrackingBadge - Now Event-Based

The `TrackingBadge` (used in project cards) now uses events instead of polling:

```dart
TrackingBadge(
  projectId: project.id,
  projectColor: projectColor,
  isDark: isDark,
)
```

**Before (Polling):**
- Checked status every 5 seconds with timers
- Could miss events or show stale data
- Used throttling to avoid excessive calls

**After (Event-based):**
- Instantly responds to location events
- No polling or timers needed
- Always shows current state

## Migration from Old System

### Before (Polling Pattern):
```dart
Timer.periodic(Duration(seconds: 5), (_) async {
  final isTracking = await locationService.isProjectBeingTracked(projectId);
  if (isTracking != _wasTracking) {
    setState(() => _wasTracking = isTracking);
  }
});
```

### After (Event Pattern):
```dart
locationService.eventStream
  .where((event) => event.projectId == projectId)
  .listen((event) {
    if (event.type == LocationEventType.areaEntered) {
      setState(() => _isTracking = true);
    }
  });
```

## Event Types

```dart
enum LocationEventType {
  trackingStarted,    // Location service started
  trackingStopped,    // Location service stopped  
  areaEntered,       // User entered a tracking area
  areaExited,        // User exited a tracking area
  timeEntryCreated,  // New time entry created
  timeEntryUpdated,  // Time entry updated
  statusChanged,     // General status change
}
```

## Real-World Usage Examples

### 1. Main App Screen with Global Tracking

```dart
class MainScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // App content
          Expanded(child: ProjectsList()),
        ],
      ),
    ).withLocationTracking(); // Add global tracking overlay
  }
}
```

### 2. Project Detail Screen

```dart
class ProjectDetailScreen extends StatelessWidget {
  final Project project;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(project.name),
        actions: [
          // Show if this project is being tracked
          ProjectTrackingStatus(
            projectId: project.id,
            projectName: project.name,
          ),
        ],
      ),
      body: ProjectDetails(project: project),
    ).withLocationTracking(projectId: project.id);
  }
}
```

### 3. Custom Event Handling

```dart
class CustomLocationWidget extends StatefulWidget {
  @override
  State<CustomLocationWidget> createState() => _CustomLocationWidgetState();
}

class _CustomLocationWidgetState extends State<CustomLocationWidget> {
  final LocationService _locationService = LocationService();
  StreamSubscription<LocationEvent>? _eventSubscription;
  List<String> _recentEvents = [];

  @override
  void initState() {
    super.initState();
    
    _eventSubscription = _locationService.eventStream.listen((event) {
      setState(() {
        _recentEvents.add('${event.type}: ${event.projectName ?? 'Unknown'}');
        if (_recentEvents.length > 10) {
          _recentEvents.removeAt(0); // Keep only recent 10 events
        }
      });
    });
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _recentEvents.length,
      itemBuilder: (context, index) {
        return ListTile(
          title: Text(_recentEvents[index]),
          leading: Icon(Icons.location_on),
        );
      },
    );
  }
}
```

## Benefits of Event-Based System

1. **Real-time Updates**: No delays from polling intervals
2. **Better Performance**: No unnecessary timer-based checks
3. **Accurate State**: Always reflects current tracking status
4. **Lower Battery Usage**: No background polling
5. **Simplified Code**: No need for throttling or manual status checks
6. **Reactive UI**: UI automatically updates when location events occur

## Architecture Overview

```
Background Isolate          Main Isolate               UI Widgets
┌─────────────────┐        ┌────────────────┐        ┌──────────────┐
│ LocationTask    │  data  │ LocationService │ stream │ TrackingBadge│
│ Handler         │───────▶│                │───────▶│              │
│                 │        │ - eventStream  │        │              │
│ - GPS tracking  │        │ - statusStream │        │              │
│ - Area detection│        │                │        └──────────────┘
│ - Time entries  │        │                │ stream │ Project      │
│                 │        │                │───────▶│ TrackingStatus│
└─────────────────┘        └────────────────┘        └──────────────┘
```

The new system eliminates polling completely and provides a clean, reactive interface for location-based time tracking. 
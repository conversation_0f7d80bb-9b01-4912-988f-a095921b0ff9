# Implementation Plan

- [ ] 1. Create core invoice data models and Hive adapters
  - Implement Invoice, Client, InvoiceLineItem, and Address models with Hive annotations
  - Add servicePeriodStart and servicePeriodEnd fields to Invoice model
  - Create corresponding Hive type adapters using build_runner code generation
  - Add comprehensive validation methods and JSON serialization support including service period validation
  - Write unit tests for all model classes including edge cases and validation
  - _Requirements: 1.1, 2.1, 3.1, 6.1, 7.1_

- [x] 2. Extend database service for invoice and client management
  - Add new Hive boxes for invoices, clients, and invoice settings to DatabaseService
  - Implement CRUD operations for Invoice and Client models
  - Add invoice-specific query methods (by date range, client, status)
  - Update database initialization to register new Hive adapters
  - Write unit tests for all new database operations
  - _Requirements: 1.1, 5.1, 6.1_

- [x] 3. Implement currency service for locale-aware formatting
  - Create CurrencyService class with support for major international currencies
  - Implement currency formatting methods that respect locale-specific rules
  - Add currency validation and supported currency list functionality
  - Integrate with existing LocaleDateUtils for consistent locale handling
  - Write unit tests for currency formatting across different locales
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 4. Create invoice calculation and business logic service
  - Implement InvoiceService class with invoice creation from time entries
  - Add methods for calculating subtotals, taxes, and totals with proper rounding
  - Implement invoice number generation with customizable formats
  - Add invoice status management and update functionality
  - Write comprehensive unit tests for all calculation scenarios
  - _Requirements: 1.1, 1.2, 4.1, 4.2, 5.1_

- [x] 5. Build client management functionality
  - Create ClientService class for client CRUD operations
  - Implement client search and filtering capabilities
  - Add client validation including email format and tax ID validation
  - Create client selection and management helper methods
  - Write unit tests for client operations and validation
  - _Requirements: 3.1, 3.2_

- [x] 6. Integrate invoice data with backup system
  - Extend BackupData model to include invoice and client data
  - Update DatabaseService backup/restore methods to handle invoice data
  - Ensure invoice data is included in cloud backup encryption
  - Add migration handling for existing backups without invoice data
  - Write integration tests for backup/restore with invoice data
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 7. Create invoice list screen and navigation
  - Implement InvoiceListScreen with Material 3 design following existing patterns
  - Add invoice filtering by status, date range, and client
  - Create InvoiceCard widget for displaying invoice summaries
  - Implement navigation integration with existing app structure
  - Add floating action button for creating new invoices
  - Write widget tests for invoice list functionality
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 8. Build time entry selection interface
  - Create TimeEntrySelector widget for multi-selecting time entries
  - Implement filtering by project, date range, and unbilled status
  - Add time entry grouping by project with expandable sections
  - Create summary calculations showing selected hours and estimated totals
  - Write widget tests for time entry selection functionality
  - _Requirements: 1.1, 1.2_

- [x] 9. Implement client management UI components
  - Create ClientSelector widget with search and quick-add functionality
  - Build AddClientDialog for creating new clients with validation
  - Implement ClientManagementScreen for viewing and editing clients
  - Add client address input with proper formatting
  - Write widget tests for client management components
  - _Requirements: 3.1, 3.2_

- [x] 10. Create invoice creation and editing screen
  - Implement InvoiceCreateScreen with step-by-step invoice creation flow
  - Add real-time invoice preview with currency and locale formatting
  - Create rate configuration interface for different projects/time entries
  - Implement additional line items functionality for expenses and adjustments
  - Add invoice settings configuration (currency, locale, due date)
  - Write widget tests for invoice creation workflow
  - _Requirements: 1.1, 1.2, 2.1, 4.1, 4.2_

- [x] 11. Build currency and locale selection components
  - Create CurrencyPicker widget with search and popular currencies section
  - Implement locale-aware currency formatting preview
  - Add currency validation with user feedback for unsupported currencies
  - Create currency settings persistence and default handling
  - Write widget tests for currency selection and formatting
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 12. Implement PDF generation service
  - Create PDFService class using pdf package for Flutter
  - Implement professional invoice template with proper layout
  - Add support for business logo and customizable styling
  - Implement locale-aware text formatting and number display
  - Add error handling for PDF generation failures
  - Write unit tests for PDF generation with various invoice configurations
  - _Requirements: 1.3, 1.4_

- [x] 13. Create invoice detail and management screen
  - Implement InvoiceDetailScreen for viewing complete invoice information
  - Add invoice status management with update functionality
  - Create invoice editing capabilities with validation
  - Implement PDF preview and sharing functionality
  - Add invoice deletion with confirmation dialog
  - Write widget tests for invoice detail operations
  - _Requirements: 1.4, 5.1, 5.2, 5.3_

- [x] 14. Add PDF export and sharing functionality
  - Integrate PDF generation with file sharing using existing share_plus
  - Implement local PDF storage with user-selectable location
  - Add email sharing integration with PDF attachment
  - Create PDF generation progress indicators for large invoices
  - Add error handling and user feedback for export operations
  - Write integration tests for PDF export and sharing workflows
  - _Requirements: 1.3, 1.4_

- [x] 15. Implement invoice settings and templates
  - Create InvoiceSettingsScreen for configuring default invoice settings
  - Add business information configuration (name, address, logo)
  - Implement invoice template selection with preview
  - Add default currency and locale settings
  - Create invoice numbering format configuration
  - Write widget tests for settings configuration
  - _Requirements: 3.2, 3.3_

- [x] 16. Implement service period (Leistungszeitraum) functionality
- [x] 16.1 Update Invoice model with service period fields
  - Add servicePeriodStart and servicePeriodEnd DateTime fields to Invoice model with @HiveField annotations
  - Update Invoice constructor, copyWith, fromJson, and toJson methods to handle service period fields
  - Add isServicePeriodValid() validation method to Invoice model
  - Regenerate Hive type adapters using build_runner
  - _Requirements: 6.1, 6.2_

- [x] 16.2 Create ServicePeriodSelector widget
  - Implement ServicePeriodSelector widget with Material 3 design
  - Add checkbox to enable/disable service period with proper state management
  - Create two date picker fields for start and end dates with validation
  - Implement real-time validation with error messages for invalid date ranges
  - Add proper accessibility support and keyboard navigation
  - _Requirements: 6.1, 6.2, 6.5_

- [x] 16.3 Add German and English localization
  - Add German translations: "Leistungszeitraum", "Von", "Bis", "Leistungszeitraum aktivieren"
  - Add English translations: "Service Period", "From", "To", "Enable Service Period"
  - Create validation error messages in both languages
  - Update InvoiceLocalizationService with service period translations
  - Test localization switching between German and English
  - _Requirements: 6.3_

- [x] 16.4 Integrate service period into invoice screens
  - Update InvoiceCreateScreen to include ServicePeriodSelector widget
  - Add service period section to invoice editing interface
  - Update invoice preview to display service period when enabled
  - Ensure service period data is properly saved and loaded
  - Add service period information to invoice detail screen
  - _Requirements: 6.1, 6.2_

- [x] 16.5 Update PDF generation with service period
  - Modify PDFService to include service period information in PDF data
  - Update professional template to show service period in dedicated section
  - Update minimal template to show service period in compact format
  - Update detailed template to show service period in highlighted box
  - Ensure proper date formatting for service period in PDFs based on locale
  - _Requirements: 6.4_

- [ ] 17. Add comprehensive error handling and validation
  - Implement form validation for all invoice and client input fields
  - Add currency and locale validation with user-friendly error messages
  - Create error handling for PDF generation and file operations
  - Implement data consistency validation for time entry references
  - Add graceful fallback handling for unsupported currencies/locales
  - _Requirements: 2.4, 3.1, 4.1, 4.2_

- [ ] 18. Create invoice analytics and reporting
  - Add invoice summary statistics (total invoiced, pending, overdue)
  - Implement invoice filtering and search functionality
  - Create date range reporting for invoice analysis
  - Add client-based invoice reporting and summaries
  - Write widget tests for analytics and reporting features
  - _Requirements: 5.1, 5.2_

- [ ] 19. Integrate with existing app navigation and theme
  - Add invoice module to main navigation structure
  - Ensure consistent Material 3 theming across all invoice screens
  - Implement proper navigation flow between invoice screens
  - Add invoice-related notifications and status updates
  - Create consistent loading states and progress indicators
  - Write integration tests for navigation and theming
  - _Requirements: 1.1, 3.3_

- [ ] 20. Add comprehensive testing and locale validation
  - Write integration tests for complete invoice creation workflow
  - Add locale-specific testing for major currencies and date formats
  - Test backup/restore functionality with invoice data
  - Create performance tests for large invoice lists and PDF generation
  - Add accessibility testing for all invoice-related screens
  - Test error scenarios and recovery mechanisms
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 6.1, 6.2, 6.3, 6.4_

- [ ] 21. Final integration and polish
  - Perform end-to-end testing of complete invoice workflow
  - Optimize performance for large datasets and PDF generation
  - Add final UI polish and animations consistent with app design
  - Create user documentation and help text for invoice features
  - Implement any remaining edge case handling
  - Conduct final testing across different device sizes and orientations
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 5.1, 5.2, 5.3_
# Design Document

## Overview

The Invoice Module will be integrated into the existing Flutter time tracking application as a new feature that transforms tracked time entries into professional invoices. The module will leverage the existing architecture patterns, including Hive for local storage, the established service layer pattern, and the current locale-aware formatting system. The design emphasizes seamless integration with existing backup systems, consistent UI/UX with the current Material 3 theme, and robust locale support for international usage.

## Architecture

### High-Level Architecture

The Invoice Module follows the existing application architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Layer      │    │  Service Layer  │    │  Data Layer     │
│                 │    │                 │    │                 │
│ - Invoice       │◄──►│ - Invoice       │◄──►│ - Invoice       │
│   Screens       │    │   Service       │    │   Models        │
│ - Invoice       │    │ - PDF           │    │ - Client        │
│   Widgets       │    │   Service       │    │   Models        │
│ - Dialogs       │    │ - Currency      │    │ - Hive          │
│                 │    │   Service       │    │   Storage       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Integration Points

- **Database Service**: Extends existing Hive-based storage for invoice and client data
- **Backup Service**: Integrates with existing backup/restore functionality
- **Locale System**: Utilizes existing `LocaleDateUtils` for currency and date formatting
- **Theme System**: Follows existing Material 3 theme patterns
- **Navigation**: Integrates with existing screen navigation structure

## Components and Interfaces

### Core Models

#### Invoice Model
```dart
@HiveType(typeId: 4)
class Invoice extends HiveObject {
  @HiveField(0) final String id;
  @HiveField(1) final String invoiceNumber;
  @HiveField(2) final String clientId;
  @HiveField(3) final List<String> timeEntryIds;
  @HiveField(4) final List<InvoiceLineItem> additionalItems;
  @HiveField(5) final String currency;
  @HiveField(6) final String locale;
  @HiveField(7) final DateTime issueDate;
  @HiveField(8) final DateTime? dueDate;
  @HiveField(9) final InvoiceStatus status;
  @HiveField(10) final double subtotal;
  @HiveField(11) final double taxRate;
  @HiveField(12) final double taxAmount;
  @HiveField(13) final double total;
  @HiveField(14) final String? notes;
  @HiveField(15) final String createdAt;
  @HiveField(16) final String? updatedAt;
  @HiveField(17) final DateTime? servicePeriodStart;
  @HiveField(18) final DateTime? servicePeriodEnd;
}
```

#### Client Model
```dart
@HiveType(typeId: 5)
class Client extends HiveObject {
  @HiveField(0) final String id;
  @HiveField(1) final String name;
  @HiveField(2) final String? email;
  @HiveField(3) final String? phone;
  @HiveField(4) final Address? address;
  @HiveField(5) final String? taxId;
  @HiveField(6) final String? notes;
  @HiveField(7) final String createdAt;
}
```

#### InvoiceLineItem Model
```dart
@HiveType(typeId: 6)
class InvoiceLineItem extends HiveObject {
  @HiveField(0) final String id;
  @HiveField(1) final String description;
  @HiveField(2) final double quantity;
  @HiveField(3) final double rate;
  @HiveField(4) final double amount;
  @HiveField(5) final InvoiceLineItemType type;
}
```

### Service Layer

#### InvoiceService
Primary service for invoice management:
- `createInvoiceFromTimeEntries(List<TimeEntry> entries, Client client, InvoiceSettings settings)`
- `calculateInvoiceTotals(Invoice invoice)`
- `updateInvoiceStatus(String invoiceId, InvoiceStatus status)`
- `getInvoicesByDateRange(DateTime start, DateTime end)`
- `getInvoicesByClient(String clientId)`

#### ClientService
Manages client information:
- `saveClient(Client client)`
- `getClients()`
- `searchClients(String query)`
- `deleteClient(String clientId)`

#### PDFService
Handles PDF generation and export:
- `generateInvoicePDF(Invoice invoice, InvoiceTemplate template)`
- `shareInvoicePDF(String filePath)`
- `saveInvoicePDF(String filePath, String fileName)`

#### CurrencyService
Manages currency formatting and conversion:
- `formatCurrency(double amount, String currency, String locale)`
- `getSupportedCurrencies()`
- `getCurrencySymbol(String currency)`
- `validateCurrencyFormat(String currency, String locale)`

### UI Components

#### Screens
- **InvoiceListScreen**: Main invoice management interface
- **InvoiceCreateScreen**: Invoice creation and editing
- **InvoiceDetailScreen**: View and manage individual invoices
- **ClientManagementScreen**: Client information management
- **InvoiceSettingsScreen**: Template and default settings

#### Key Widgets
- **InvoiceCard**: Display invoice summary in lists
- **TimeEntrySelector**: Multi-select widget for time entries
- **CurrencyPicker**: Locale-aware currency selection
- **InvoicePreview**: Real-time invoice preview during creation
- **ClientSelector**: Client selection with search and quick-add
- **InvoiceTemplateSelector**: Template selection widget
- **ServicePeriodSelector**: Optional date range selector for service period with German/English localization

## Data Models

### Database Schema Extensions

The existing Hive database will be extended with new type IDs:
- Invoice: typeId 4
- Client: typeId 5  
- InvoiceLineItem: typeId 6
- Address: typeId 7
- InvoiceSettings: typeId 8

### Relationships

```mermaid
erDiagram
    Invoice ||--|| Client : "belongs to"
    Invoice ||--o{ TimeEntry : "includes"
    Invoice ||--o{ InvoiceLineItem : "contains"
    Client ||--o{ Invoice : "has many"
    Client ||--o| Address : "has"
```

### Backup Integration

Invoice data will be included in the existing `BackupData` model:
```dart
class BackupData {
  // ... existing fields
  final List<Invoice> invoices;
  final List<Client> clients;
  final List<InvoiceSettings> invoiceSettings;
}
```

## Error Handling

### Validation Errors
- **Client Validation**: Required fields, email format, tax ID format
- **Invoice Validation**: Date ranges, currency amounts, time entry consistency
- **Currency Validation**: Supported currency codes, locale compatibility
- **PDF Generation**: Template errors, file system permissions

### Error Recovery
- **Graceful Degradation**: Fall back to default templates/currencies if custom ones fail
- **Data Consistency**: Validate time entry references before invoice creation
- **Backup Integration**: Ensure invoice data integrity during backup/restore operations

### User Feedback
- **Validation Messages**: Clear, actionable error messages with locale support
- **Progress Indicators**: PDF generation and data processing feedback
- **Confirmation Dialogs**: Critical actions like invoice deletion

## Testing Strategy

### Unit Tests
- **Model Validation**: Test all model constructors, validation rules, and serialization
- **Service Logic**: Test invoice calculations, currency formatting, and business rules
- **Currency Handling**: Test locale-specific formatting and edge cases
- **Date Formatting**: Test invoice date formatting across different locales

### Integration Tests
- **Database Operations**: Test CRUD operations for invoices and clients
- **Backup/Restore**: Test invoice data preservation during backup operations
- **PDF Generation**: Test PDF creation with various templates and data combinations
- **Time Entry Integration**: Test invoice creation from different time entry scenarios

### Widget Tests
- **Invoice Forms**: Test form validation and user input handling
- **Currency Pickers**: Test currency selection and formatting display
- **Date Pickers**: Test date selection with locale-aware formatting
- **Preview Components**: Test real-time invoice preview updates

### Locale Testing
- **Currency Formatting**: Test major currencies (USD, EUR, GBP, JPY, etc.)
- **Date Formatting**: Test various date formats (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD)
- **Number Formatting**: Test decimal separators and thousand separators
- **RTL Support**: Test right-to-left language support for Arabic locales

## Service Period Feature Design

### Service Period Data Model
The service period feature will be implemented as optional fields in the Invoice model:

```dart
class Invoice extends HiveObject {
  // ... existing fields
  @HiveField(17) final DateTime? servicePeriodStart;
  @HiveField(18) final DateTime? servicePeriodEnd;
  
  // Validation method
  bool isServicePeriodValid() {
    if (servicePeriodStart == null && servicePeriodEnd == null) return true;
    if (servicePeriodStart == null || servicePeriodEnd == null) return false;
    return servicePeriodEnd!.isAfter(servicePeriodStart!) || 
           servicePeriodEnd!.isAtSameMomentAs(servicePeriodStart!);
  }
}
```

### Service Period UI Components

**ServicePeriodSelector Widget:**
- Checkbox to enable/disable service period
- Two date picker fields for start and end dates
- Localized labels (German: "Leistungszeitraum", English: "Service Period")
- Real-time validation with error messages
- Integration with existing Material 3 theme

**Localization Support:**
- German translations: "Leistungszeitraum", "Von", "Bis", "Leistungszeitraum aktivieren"
- English translations: "Service Period", "From", "To", "Enable Service Period"
- Validation messages in both languages

### PDF Template Integration
Service period information will be displayed in all PDF templates:
- Professional template: Dedicated service period section
- Minimal template: Compact service period line
- Detailed template: Highlighted service period box

## Locale and Currency Considerations

### Currency Support
The module will support major international currencies with proper locale-aware formatting:

- **Supported Currencies**: USD, EUR, GBP, JPY, CAD, AUD, CHF, CNY, INR, BRL
- **Formatting Rules**: Respect locale-specific decimal separators, thousand separators, and currency symbol placement
- **Fallback Handling**: Default to USD with warning if unsupported currency is selected

### Date Formatting Integration
Leverage existing `LocaleDateUtils` for consistent date formatting:
- **Invoice Dates**: Issue date, due date formatting
- **Service Period Dates**: Start and end date formatting with locale support
- **Date Ranges**: Time entry date ranges in invoices
- **Locale Override**: Support for custom date formats from user settings

### Number Formatting
- **Decimal Precision**: Consistent 2-decimal precision for monetary amounts
- **Thousand Separators**: Locale-appropriate separators (comma vs period)
- **Negative Numbers**: Proper formatting for credits/adjustments

## PDF Generation and Templates

### Template System
- **Built-in Templates**: Professional, Minimal, Detailed templates
- **Customization**: Business logo, colors, fonts within templates
- **Responsive Design**: Templates adapt to different content lengths

### PDF Library Integration
Utilize `pdf` package for Flutter:
- **Document Structure**: Header, client info, line items, totals, footer
- **Styling**: Consistent with app theme, professional appearance
- **Localization**: Support for different text directions and character sets

### Export Options
- **File Sharing**: Integration with existing `share_plus` functionality
- **Local Storage**: Save to device with user-chosen location
- **Email Integration**: Direct email sharing with PDF attachment

## Performance Considerations

### Data Loading
- **Lazy Loading**: Load invoice details only when needed
- **Pagination**: Implement pagination for large invoice lists
- **Caching**: Cache frequently accessed client and currency data

### PDF Generation
- **Background Processing**: Generate PDFs on background isolate
- **Progress Feedback**: Show generation progress for large invoices
- **Memory Management**: Efficient handling of PDF generation memory usage

### Database Optimization
- **Indexing**: Efficient queries for invoice searches and filtering
- **Batch Operations**: Optimize bulk operations for time entry selection
- **Storage Efficiency**: Minimize storage footprint for invoice data

## Security Considerations

### Data Protection
- **Encryption**: Invoice data included in existing encryption scheme
- **Access Control**: No additional authentication required (single-user app)
- **Data Validation**: Sanitize all user inputs, especially in PDF generation

### PDF Security
- **Content Validation**: Prevent injection attacks in PDF content
- **File Permissions**: Appropriate file system permissions for generated PDFs
- **Temporary Files**: Secure cleanup of temporary PDF files

## Migration and Compatibility

### Database Migration
- **Schema Updates**: Add new Hive type adapters for invoice models
- **Backward Compatibility**: Ensure existing data remains accessible
- **Migration Scripts**: Handle any necessary data transformations

### Backup Compatibility
- **Schema Versioning**: Update backup schema version for invoice support
- **Restore Handling**: Gracefully handle backups without invoice data
- **Cloud Backup**: Ensure invoice data is included in cloud backup encryption
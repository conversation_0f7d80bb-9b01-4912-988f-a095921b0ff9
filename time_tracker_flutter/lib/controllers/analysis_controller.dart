import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/analysis_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

/// Controller class for analysis screen
class AnalysisController extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final AnalysisService _analysisService = AnalysisService();
  
  bool _isLoading = true;
  List<Project> _projects = [];
  String? _selectedProjectId;
  DateTimeRange _dateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  );
  AnalysisPeriod _selectedPeriod = AnalysisPeriod.month;
  AnalysisResult? _analysisResult;

  // Getters
  bool get isLoading => _isLoading;
  List<Project> get projects => _projects;
  String? get selectedProjectId => _selectedProjectId;
  DateTimeRange get dateRange => _dateRange;
  AnalysisPeriod get selectedPeriod => _selectedPeriod;
  AnalysisResult? get analysisResult => _analysisResult;
  AnalysisService get analysisService => _analysisService;

  /// Initialize the controller
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _databaseService.initialize();
      _projects = await _databaseService.getProjects();
      _projects.sort((a, b) => a.order.compareTo(b.order));

      await _generateAnalysis();
    } catch (e) {
      debugPrint("Error initializing analysis: $e");
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Generate analysis based on current filters
  Future<void> _generateAnalysis() async {
    _isLoading = true;
    notifyListeners();

    try {
      _analysisResult = await _analysisService.generateAnalysis(
        dateRange: _dateRange,
        projectId: _selectedProjectId,
      );
    } catch (e) {
      debugPrint("Error generating analysis: $e");
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update the selected project
  void updateSelectedProject(String? projectId) {
    if (_selectedProjectId != projectId) {
      _selectedProjectId = projectId;
      _generateAnalysis();
    }
  }

  /// Update the date range based on period
  void updatePeriod(AnalysisPeriod period) {
    if (_selectedPeriod == period) return;
    
    final now = DateTime.now();
    DateTimeRange newRange;

    switch (period) {
      case AnalysisPeriod.day:
        newRange = DateTimeRange(start: now, end: now);
        break;
      case AnalysisPeriod.week:
        // Start of week (Monday)
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        newRange = DateTimeRange(
          start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
          end: now,
        );
        break;
      case AnalysisPeriod.month:
        // Start of month
        newRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: now,
        );
        break;
      case AnalysisPeriod.allTime:
        // Start from a year ago or some reasonable "all time" period
        newRange = DateTimeRange(
          start: DateTime(2000, 1, 1),
          end: now,
        );
        break;
      case AnalysisPeriod.custom:
        // Keep current range for custom
        newRange = _dateRange;
        break;
    }

    _selectedPeriod = period;
    _dateRange = newRange;
    _generateAnalysis();
  }

  /// Update the date range directly
  void updateDateRange(DateTimeRange dateRange) {
    if (_dateRange != dateRange) {
      _dateRange = dateRange;
      _selectedPeriod = AnalysisPeriod.custom;
      _generateAnalysis();
    }
  }

  /// Calculate consistency metric for time series data
  String calculateConsistencyMetric(List<TimeSeriesData> timeData) {
    if (timeData.length < 3) return 'Insufficient data';

    // Sort by date
    final sortedData = List<TimeSeriesData>.from(timeData)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Calculate standard deviation of hours
    final avgHours = sortedData.fold<double>(0, (sum, item) => sum + item.hours) / sortedData.length;
    final sumSquaredDiff = sortedData.fold<double>(
      0, (sum, item) => sum + ((item.hours - avgHours) * (item.hours - avgHours))
    );
    final stdDev = sqrt(sumSquaredDiff / sortedData.length);

    // Calculate coefficient of variation (CV) - lower means more consistent
    final cv = stdDev / avgHours;

    // Calculate date consistency (gaps between entries)
    final dateGaps = <int>[];
    for (int i = 1; i < sortedData.length; i++) {
      final gap = sortedData[i].date.difference(sortedData[i-1].date).inDays;
      dateGaps.add(gap);
    }

    final avgGap = dateGaps.fold<double>(0, (sum, gap) => sum + gap) / dateGaps.length;

    // Combine metrics into a consistency score (0-100)
    // Lower CV and lower avg gap means higher consistency
    double consistencyScore = 100;

    // Penalize for high variation in hours (up to -50 points)
    if (cv > 0.1) consistencyScore -= (cv * 100).clamp(0, 50);

    // Penalize for gaps in tracking (up to -50 points)
    if (avgGap > 1) consistencyScore -= ((avgGap - 1) * 10).clamp(0, 50);

    // Determine consistency level
    String consistencyLevel;
    if (consistencyScore >= 90) {
      consistencyLevel = 'Excellent';
    } else if (consistencyScore >= 75) {
      consistencyLevel = 'Good';
    } else if (consistencyScore >= 60) {
      consistencyLevel = 'Moderate';
    } else if (consistencyScore >= 40) {
      consistencyLevel = 'Fair';
    } else {
      consistencyLevel = 'Poor';
    }

    return consistencyLevel;
  }
}

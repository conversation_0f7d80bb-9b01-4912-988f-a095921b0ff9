// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AddressAdapter extends TypeAdapter<Address> {
  @override
  final int typeId = 7;

  @override
  Address read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Address(
      street: fields[0] as String,
      street2: fields[1] as String?,
      city: fields[2] as String,
      state: fields[3] as String?,
      postalCode: fields[4] as String,
      country: fields[5] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Address obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.street)
      ..writeByte(1)
      ..write(obj.street2)
      ..writeByte(2)
      ..write(obj.city)
      ..writeByte(3)
      ..write(obj.state)
      ..writeByte(4)
      ..write(obj.postalCode)
      ..writeByte(5)
      ..write(obj.country);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ClientAdapter extends TypeAdapter<Client> {
  @override
  final int typeId = 5;

  @override
  Client read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Client(
      id: fields[0] as String?,
      name: fields[1] as String,
      email: fields[2] as String?,
      phone: fields[3] as String?,
      address: fields[4] as Address?,
      taxId: fields[5] as String?,
      notes: fields[6] as String?,
      createdAt: fields[7] as String?,
      updatedAt: fields[8] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Client obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.phone)
      ..writeByte(4)
      ..write(obj.address)
      ..writeByte(5)
      ..write(obj.taxId)
      ..writeByte(6)
      ..write(obj.notes)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ClientAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceLineItemAdapter extends TypeAdapter<InvoiceLineItem> {
  @override
  final int typeId = 6;

  @override
  InvoiceLineItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InvoiceLineItem(
      id: fields[0] as String?,
      description: fields[1] as String,
      quantity: fields[2] as double,
      rate: fields[3] as double,
      amount: fields[4] as double?,
      type: fields[5] as InvoiceLineItemType,
      timeEntryId: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, InvoiceLineItem obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.description)
      ..writeByte(2)
      ..write(obj.quantity)
      ..writeByte(3)
      ..write(obj.rate)
      ..writeByte(4)
      ..write(obj.amount)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.timeEntryId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceLineItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BusinessInfoAdapter extends TypeAdapter<BusinessInfo> {
  @override
  final int typeId = 11;

  @override
  BusinessInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BusinessInfo(
      name: fields[0] as String,
      email: fields[1] as String?,
      phone: fields[2] as String?,
      address: fields[3] as Address?,
      website: fields[4] as String?,
      taxId: fields[5] as String?,
      logoPath: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BusinessInfo obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.email)
      ..writeByte(2)
      ..write(obj.phone)
      ..writeByte(3)
      ..write(obj.address)
      ..writeByte(4)
      ..write(obj.website)
      ..writeByte(5)
      ..write(obj.taxId)
      ..writeByte(6)
      ..write(obj.logoPath);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BusinessInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceSettingsAdapter extends TypeAdapter<InvoiceSettings> {
  @override
  final int typeId = 12;

  @override
  InvoiceSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InvoiceSettings(
      defaultCurrency: fields[0] as String,
      defaultLocale: fields[1] as String,
      defaultTemplate: fields[2] as InvoiceTemplate,
      businessInfo: fields[3] as BusinessInfo?,
      invoiceNumberPrefix: fields[4] as String,
      invoiceNumberDateFormat: fields[5] as String,
      invoiceNumberSequenceLength: fields[6] as int,
      defaultTaxRate: fields[7] as double,
      defaultDueDays: fields[8] as int,
      defaultNotes: fields[9] as String?,
      createdAt: fields[10] as String?,
      updatedAt: fields[11] as String?,
      defaultInvoiceLanguage: fields[12] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, InvoiceSettings obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.defaultCurrency)
      ..writeByte(1)
      ..write(obj.defaultLocale)
      ..writeByte(2)
      ..write(obj.defaultTemplate)
      ..writeByte(3)
      ..write(obj.businessInfo)
      ..writeByte(4)
      ..write(obj.invoiceNumberPrefix)
      ..writeByte(5)
      ..write(obj.invoiceNumberDateFormat)
      ..writeByte(6)
      ..write(obj.invoiceNumberSequenceLength)
      ..writeByte(7)
      ..write(obj.defaultTaxRate)
      ..writeByte(8)
      ..write(obj.defaultDueDays)
      ..writeByte(9)
      ..write(obj.defaultNotes)
      ..writeByte(10)
      ..write(obj.createdAt)
      ..writeByte(11)
      ..write(obj.updatedAt)
      ..writeByte(12)
      ..write(obj.defaultInvoiceLanguage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceAdapter extends TypeAdapter<Invoice> {
  @override
  final int typeId = 4;

  @override
  Invoice read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Invoice(
      id: fields[0] as String?,
      invoiceNumber: fields[1] as String,
      clientId: fields[2] as String,
      timeEntryIds: (fields[3] as List?)?.cast<String>(),
      additionalItems: (fields[4] as List?)?.cast<InvoiceLineItem>(),
      currency: fields[5] as String,
      locale: fields[6] as String,
      issueDate: fields[7] as DateTime?,
      dueDate: fields[8] as DateTime?,
      status: fields[9] as InvoiceStatus,
      subtotal: fields[10] as double,
      taxRate: fields[11] as double,
      taxAmount: fields[12] as double,
      total: fields[13] as double,
      notes: fields[14] as String?,
      createdAt: fields[15] as String?,
      updatedAt: fields[16] as String?,
      servicePeriodStart: fields[17] as DateTime?,
      servicePeriodEnd: fields[18] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Invoice obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.invoiceNumber)
      ..writeByte(2)
      ..write(obj.clientId)
      ..writeByte(3)
      ..write(obj.timeEntryIds)
      ..writeByte(4)
      ..write(obj.additionalItems)
      ..writeByte(5)
      ..write(obj.currency)
      ..writeByte(6)
      ..write(obj.locale)
      ..writeByte(7)
      ..write(obj.issueDate)
      ..writeByte(8)
      ..write(obj.dueDate)
      ..writeByte(9)
      ..write(obj.status)
      ..writeByte(10)
      ..write(obj.subtotal)
      ..writeByte(11)
      ..write(obj.taxRate)
      ..writeByte(12)
      ..write(obj.taxAmount)
      ..writeByte(13)
      ..write(obj.total)
      ..writeByte(14)
      ..write(obj.notes)
      ..writeByte(15)
      ..write(obj.createdAt)
      ..writeByte(16)
      ..write(obj.updatedAt)
      ..writeByte(17)
      ..write(obj.servicePeriodStart)
      ..writeByte(18)
      ..write(obj.servicePeriodEnd);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceLineItemTypeAdapter extends TypeAdapter<InvoiceLineItemType> {
  @override
  final int typeId = 9;

  @override
  InvoiceLineItemType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return InvoiceLineItemType.timeEntry;
      case 1:
        return InvoiceLineItemType.expense;
      case 2:
        return InvoiceLineItemType.discount;
      case 3:
        return InvoiceLineItemType.adjustment;
      default:
        return InvoiceLineItemType.timeEntry;
    }
  }

  @override
  void write(BinaryWriter writer, InvoiceLineItemType obj) {
    switch (obj) {
      case InvoiceLineItemType.timeEntry:
        writer.writeByte(0);
        break;
      case InvoiceLineItemType.expense:
        writer.writeByte(1);
        break;
      case InvoiceLineItemType.discount:
        writer.writeByte(2);
        break;
      case InvoiceLineItemType.adjustment:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceLineItemTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceStatusAdapter extends TypeAdapter<InvoiceStatus> {
  @override
  final int typeId = 8;

  @override
  InvoiceStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return InvoiceStatus.draft;
      case 1:
        return InvoiceStatus.sent;
      case 2:
        return InvoiceStatus.paid;
      case 3:
        return InvoiceStatus.overdue;
      case 4:
        return InvoiceStatus.cancelled;
      default:
        return InvoiceStatus.draft;
    }
  }

  @override
  void write(BinaryWriter writer, InvoiceStatus obj) {
    switch (obj) {
      case InvoiceStatus.draft:
        writer.writeByte(0);
        break;
      case InvoiceStatus.sent:
        writer.writeByte(1);
        break;
      case InvoiceStatus.paid:
        writer.writeByte(2);
        break;
      case InvoiceStatus.overdue:
        writer.writeByte(3);
        break;
      case InvoiceStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InvoiceTemplateAdapter extends TypeAdapter<InvoiceTemplate> {
  @override
  final int typeId = 10;

  @override
  InvoiceTemplate read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return InvoiceTemplate.professional;
      case 1:
        return InvoiceTemplate.minimal;
      case 2:
        return InvoiceTemplate.detailed;
      default:
        return InvoiceTemplate.professional;
    }
  }

  @override
  void write(BinaryWriter writer, InvoiceTemplate obj) {
    switch (obj) {
      case InvoiceTemplate.professional:
        writer.writeByte(0);
        break;
      case InvoiceTemplate.minimal:
        writer.writeByte(1);
        break;
      case InvoiceTemplate.detailed:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

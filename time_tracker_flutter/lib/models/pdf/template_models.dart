import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class PDFTemplate {
  final String id;
  final String name;
  final String version;
  final String description;
  final String category;
  final bool isDefault;
  final PdfPageFormat pageFormat;
  final PDFMargins margins;
  final List<PDFSection> sections;
  final Map<String, ComponentStyle> styles;
  final Map<String, dynamic> metadata;

  const PDFTemplate({
    required this.id,
    required this.name,
    required this.version,
    this.description = '',
    this.category = 'custom',
    this.isDefault = false,
    required this.pageFormat,
    required this.margins,
    required this.sections,
    required this.styles,
    this.metadata = const {},
  });

  factory PDFTemplate.fromJson(Map<String, dynamic> json) {
    return PDFTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      version: json['version'] as String,
      description: json['description'] as String? ?? '',
      category: json['category'] as String? ?? 'custom',
      isDefault: json['isDefault'] as bool? ?? false,
      pageFormat: _parsePageFormat(json['pageFormat'] as String? ?? 'A4'),
      margins: PDFMargins.fromJson(json['margins'] as Map<String, dynamic>),
      sections: (json['sections'] as List<dynamic>)
          .map((section) => PDFSection.fromJson(section as Map<String, dynamic>))
          .toList(),
      styles: (json['styles'] as Map<String, dynamic>)
          .map((key, value) => MapEntry(key, ComponentStyle.fromJson(value as Map<String, dynamic>))),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'version': version,
      'description': description,
      'category': category,
      'isDefault': isDefault,
      'pageFormat': _pageFormatToString(pageFormat),
      'margins': margins.toJson(),
      'sections': sections.map((section) => section.toJson()).toList(),
      'styles': styles.map((key, value) => MapEntry(key, value.toJson())),
      'metadata': metadata,
    };
  }

  static PdfPageFormat _parsePageFormat(String format) {
    switch (format.toUpperCase()) {
      case 'A4':
        return PdfPageFormat.a4;
      case 'A3':
        return PdfPageFormat.a3;
      case 'LETTER':
        return PdfPageFormat.letter;
      case 'LEGAL':
        return PdfPageFormat.legal;
      default:
        return PdfPageFormat.a4;
    }
  }

  static String _pageFormatToString(PdfPageFormat format) {
    if (format == PdfPageFormat.a4) return 'A4';
    if (format == PdfPageFormat.a3) return 'A3';
    if (format == PdfPageFormat.letter) return 'LETTER';
    if (format == PdfPageFormat.legal) return 'LEGAL';
    return 'A4';
  }
}

class PDFMargins {
  final double top;
  final double bottom;
  final double left;
  final double right;

  const PDFMargins({
    this.top = 40,
    this.bottom = 40,
    this.left = 40,
    this.right = 40,
  });

  factory PDFMargins.fromJson(Map<String, dynamic> json) {
    return PDFMargins(
      top: (json['top'] as num?)?.toDouble() ?? 40,
      bottom: (json['bottom'] as num?)?.toDouble() ?? 40,
      left: (json['left'] as num?)?.toDouble() ?? 40,
      right: (json['right'] as num?)?.toDouble() ?? 40,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'top': top,
      'bottom': bottom,
      'left': left,
      'right': right,
    };
  }

  pw.EdgeInsets toPdfEdgeInsets() {
    return pw.EdgeInsets.fromLTRB(left, top, right, bottom);
  }
}

class PDFSection {
  final String id;
  final String type;
  final ComponentPosition position;
  final List<PDFComponent> components;
  final Map<String, dynamic> properties;

  const PDFSection({
    required this.id,
    required this.type,
    required this.position,
    required this.components,
    this.properties = const {},
  });

  factory PDFSection.fromJson(Map<String, dynamic> json) {
    print('PDFSection.fromJson: Parsing section "${json['id']}" with type "${json['type']}"');
    print('PDFSection.fromJson: Components data: ${json['components']}');

    final components = (json['components'] as List<dynamic>?)
        ?.map((component) => PDFComponent.fromJson(component as Map<String, dynamic>))
        .toList() ?? [];

    print('PDFSection.fromJson: Parsed ${components.length} components');

    return PDFSection(
      id: json['id'] as String,
      type: json['type'] as String,
      position: ComponentPosition.fromJson(json['position'] as Map<String, dynamic>),
      components: components,
      properties: json['properties'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'position': position.toJson(),
      'components': components.map((component) => component.toJson()).toList(),
      'properties': properties,
    };
  }
}

class PDFComponent {
  final String id;
  final String type;
  final ComponentPosition position;
  final ComponentStyle style;
  final Map<String, dynamic> dataSource;
  final Map<String, dynamic> properties;

  const PDFComponent({
    required this.id,
    required this.type,
    required this.position,
    required this.style,
    required this.dataSource,
    this.properties = const {},
  });

  factory PDFComponent.fromJson(Map<String, dynamic> json) {
    print('PDFComponent.fromJson: Parsing component "${json['id']}" of type "${json['type']}"');

    return PDFComponent(
      id: json['id'] as String,
      type: json['type'] as String,
      position: ComponentPosition.fromJson(json['position'] as Map<String, dynamic>),
      style: ComponentStyle.fromJson(json['style'] as Map<String, dynamic>),
      dataSource: json['dataSource'] as Map<String, dynamic>? ?? {},
      properties: json['properties'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'position': position.toJson(),
      'style': style.toJson(),
      'dataSource': dataSource,
      'properties': properties,
    };
  }
}

class ComponentPosition {
  final double? x;
  final double? y;
  final double? width;
  final double? height;
  final String? alignment;
  final bool isRelative;

  const ComponentPosition({
    this.x,
    this.y,
    this.width,
    this.height,
    this.alignment,
    this.isRelative = false,
  });

  factory ComponentPosition.fromJson(Map<String, dynamic> json) {
    return ComponentPosition(
      x: _parseNumericValue(json['x']),
      y: _parseNumericValue(json['y']),
      width: _parseNumericValue(json['width']),
      height: _parseNumericValue(json['height']),
      alignment: json['alignment'] as String?,
      isRelative: json['isRelative'] as bool? ?? false,
    );
  }

  static double? _parseNumericValue(dynamic value) {
    if (value == null) return null;
    if (value is num) return value.toDouble();
    if (value is String) {
      // Handle percentage values like "100%"
      if (value.endsWith('%')) {
        final numericPart = value.substring(0, value.length - 1);
        final parsed = double.tryParse(numericPart);
        return parsed != null ? parsed / 100.0 : null;
      }
      // Handle "auto" or other string values
      if (value == 'auto' || value == 'right' || value == 'left' || value == 'center') {
        return null; // These will be handled by the layout engine
      }
      // Try to parse as a regular number
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'alignment': alignment,
      'isRelative': isRelative,
    };
  }
}

class ComponentStyle {
  final double? fontSize;
  final String? fontWeight;
  final String? fontFamily;
  final String? color;
  final String? backgroundColor;
  final double? padding;
  final double? margin;
  final String? textAlign;
  final bool? isBold;
  final bool? isItalic;
  final bool? isUnderlined;

  const ComponentStyle({
    this.fontSize,
    this.fontWeight,
    this.fontFamily,
    this.color,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.textAlign,
    this.isBold,
    this.isItalic,
    this.isUnderlined,
  });

  factory ComponentStyle.fromJson(Map<String, dynamic> json) {
    return ComponentStyle(
      fontSize: (json['fontSize'] as num?)?.toDouble(),
      fontWeight: json['fontWeight'] as String?,
      fontFamily: json['fontFamily'] as String?,
      color: json['color'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      padding: (json['padding'] as num?)?.toDouble(),
      margin: (json['margin'] as num?)?.toDouble(),
      textAlign: json['textAlign'] as String?,
      isBold: json['isBold'] as bool?,
      isItalic: json['isItalic'] as bool?,
      isUnderlined: json['isUnderlined'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'fontWeight': fontWeight,
      'fontFamily': fontFamily,
      'color': color,
      'backgroundColor': backgroundColor,
      'padding': padding,
      'margin': margin,
      'textAlign': textAlign,
      'isBold': isBold,
      'isItalic': isItalic,
      'isUnderlined': isUnderlined,
    };
  }

  pw.TextStyle toPdfTextStyle(pw.Font font, pw.Font boldFont) {
    final isBoldStyle = isBold ?? fontWeight == 'bold';
    final selectedFont = isBoldStyle ? boldFont : font;

    return pw.TextStyle(
      font: selectedFont,
      fontSize: fontSize,
      color: color != null ? _parseColor(color!) : null,
    );
  }

  PdfColor? _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      if (hex.length == 6) {
        final r = int.parse(hex.substring(0, 2), radix: 16);
        final g = int.parse(hex.substring(2, 4), radix: 16);
        final b = int.parse(hex.substring(4, 6), radix: 16);
        return PdfColor.fromInt((r << 16) | (g << 8) | b);
      }
    }
    return null;
  }
}

class PDFData {
  final Map<String, dynamic> invoice;
  final Map<String, dynamic> client;
  final Map<String, dynamic> businessInfo;
  final List<Map<String, dynamic>> timeEntries;
  final Map<String, dynamic> settings;

  const PDFData({
    required this.invoice,
    required this.client,
    required this.businessInfo,
    this.timeEntries = const [],
    this.settings = const {},
  });

  dynamic getValue(String path) {
    final keys = path.split('.');
    dynamic current;

    if (keys[0] == 'invoice') {
      current = invoice;
    } else if (keys[0] == 'client') {
      current = client;
    } else if (keys[0] == 'businessInfo') {
      current = businessInfo;
    } else if (keys[0] == 'settings') {
      current = settings;
    } else {
      return null;
    }

    for (int i = 1; i < keys.length; i++) {
      if (current is Map<String, dynamic> && current.containsKey(keys[i])) {
        current = current[keys[i]];
      } else {
        return null;
      }
    }

    return current;
  }
}
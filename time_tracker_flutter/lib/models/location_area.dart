import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'package:latlong2/latlong.dart';

part 'location_area.g.dart';

@HiveType(typeId: 3)
class LocationArea extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String projectId;

  @HiveField(2)
  String name;

  @HiveField(3)
  double centerLatitude;

  @HiveField(4)
  double centerLongitude;

  @HiveField(5)
  double radius; // in meters

  @HiveField(6)
  final String createdAt;

  @HiveField(7)
  bool isActive;

  @HiveField(8)
  int cooldownTime; // in minutes

  LocationArea({
    String? id,
    required this.projectId,
    required this.name,
    required this.centerLatitude,
    required this.centerLongitude,
    required this.radius,
    String? createdAt,
    this.isActive = true,
    this.cooldownTime = 60,
  }) :
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now().toIso8601String();

  LatLng get center => LatLng(centerLatitude, centerLongitude);

  factory LocationArea.fromJson(Map<String, dynamic> json) {
    return LocationArea(
      id: json['id'],
      projectId: json['projectId'],
      name: json['name'],
      centerLatitude: json['centerLatitude'],
      centerLongitude: json['centerLongitude'],
      radius: json['radius'],
      createdAt: json['createdAt'],
      isActive: json['isActive'] ?? true,
      cooldownTime: json['cooldownTime'] ?? 60,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'name': name,
      'centerLatitude': centerLatitude,
      'centerLongitude': centerLongitude,
      'radius': radius,
      'createdAt': createdAt,
      'isActive': isActive,
      'cooldownTime': cooldownTime,
    };
  }

  LocationArea copyWith({
    String? name,
    double? centerLatitude,
    double? centerLongitude,
    double? radius,
    bool? isActive,
    int? cooldownTime,
  }) {
    return LocationArea(
      id: id,
      projectId: projectId,
      name: name ?? this.name,
      centerLatitude: centerLatitude ?? this.centerLatitude,
      centerLongitude: centerLongitude ?? this.centerLongitude,
      radius: radius ?? this.radius,
      createdAt: createdAt,
      isActive: isActive ?? this.isActive,
      cooldownTime: cooldownTime ?? this.cooldownTime,
    );
  }
}
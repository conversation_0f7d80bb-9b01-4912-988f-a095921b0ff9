import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

enum AnalysisPeriod {
  day,
  week,
  month,
  allTime,
  custom
}

enum AnalysisGroupBy {
  weekday,
  project,
  date
}

/// Defines the level of data aggregation for time series visualization
enum DataAggregationLevel {
  day,        // Show each day individually
  twoDays,    // Group every two days
  week,       // Group by week
  month,      // Group by month
  quarter     // Group by quarter (3 months)
}

class TimeDistributionData {
  final String label;
  final double hours;
  final double percentage;
  final Color? color;
  final DateTime? date;  // Optional date this data point represents
  final DataAggregationLevel? aggregationLevel;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final int? dataPointCount;

  TimeDistributionData({
    required this.label,
    required this.hours,
    required this.percentage,
    this.color,
    this.date,
    this.aggregationLevel,
    this.rangeStart,
    this.rangeEnd,
    this.dataPointCount,
  });

  /// Returns true if this data point represents aggregated data
  bool get isAggregated => aggregationLevel != null && aggregationLevel != DataAggregationLevel.day;

  /// Returns a formatted string describing the date context
  String getDateContext([Locale? locale]) {
    if (date == null) {
      return label;
    }

    // For non-date labels (like weekday names or project names)
    if (!label.contains('/') && !label.contains('-') && !RegExp(r'\d').hasMatch(label)) {
      return label;
    }

    // For aggregated data with range information
    if (isAggregated && rangeStart != null && rangeEnd != null) {
      return LocaleDateUtils.formatDateRange(rangeStart!, rangeEnd!, locale);
    }

    // For single dates, provide more context
    return LocaleDateUtils.formatDate(date!, locale);
  }
}

class ProjectAnalysisData {
  final Project project;
  final double totalHours;
  final double averageHoursPerEntry;
  final double percentageOfTotal;
  final int entryCount;

  ProjectAnalysisData({
    required this.project,
    required this.totalHours,
    required this.averageHoursPerEntry,
    required this.percentageOfTotal,
    required this.entryCount,
  });
}

class WeekdayAnalysisData {
  final String weekday;
  final double totalHours;
  final double percentageOfTotal;
  final int entryCount;

  WeekdayAnalysisData({
    required this.weekday,
    required this.totalHours,
    required this.percentageOfTotal,
    required this.entryCount,
  });
}

class TimeSeriesData {
  final DateTime date;
  final double hours;
  final String? projectId;
  final DataAggregationLevel? aggregationLevel;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final int? dataPointCount;

  TimeSeriesData({
    required this.date,
    required this.hours,
    this.projectId,
    this.aggregationLevel,
    this.rangeStart,
    this.rangeEnd,
    this.dataPointCount,
  });

  /// Returns true if this data point represents aggregated data
  bool get isAggregated => aggregationLevel != null && aggregationLevel != DataAggregationLevel.day;

  /// Returns a formatted string describing the aggregation range
  String getAggregationDescription([Locale? locale]) {
    if (!isAggregated || rangeStart == null || rangeEnd == null) {
      return '';
    }

    final rangeText = LocaleDateUtils.formatDateRange(rangeStart!, rangeEnd!, locale);

    switch (aggregationLevel) {
      case DataAggregationLevel.quarter:
        return 'Q${(date.month / 3).ceil()} $rangeText';
      case DataAggregationLevel.month:
        return LocaleDateUtils.getMonthYearFormat(locale).format(date);
      case DataAggregationLevel.week:
        return 'Week: $rangeText';
      case DataAggregationLevel.twoDays:
        return rangeText;
      default:
        return rangeText;
    }
  }
}

class AnalysisResult {
  final double totalHours;
  final int totalEntries;
  final double averageHoursPerEntry;
  final DateTimeRange dateRange;
  final List<ProjectAnalysisData> projectAnalysis;
  final List<WeekdayAnalysisData> weekdayAnalysis;
  final List<TimeSeriesData> timeSeriesData;

  // For outlier detection
  final double? minHours;
  final double? maxHours;
  final TimeSeriesData? mostProductiveDay;

  AnalysisResult({
    required this.totalHours,
    required this.totalEntries,
    required this.averageHoursPerEntry,
    required this.dateRange,
    required this.projectAnalysis,
    required this.weekdayAnalysis,
    required this.timeSeriesData,
    this.minHours,
    this.maxHours,
    this.mostProductiveDay,
  });
}
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'location_area.dart';
import 'invoice_models.dart';
import 'package:flutter/foundation.dart';

class BackupData {
  final List<Project> projects;
  final Map<String, List<TimeEntry>> timeEntries;
  final List<LocationArea> locationAreas;
  final List<Invoice> invoices;
  final List<Client> clients;
  final int schemaVersion;
  final String timestamp;

  BackupData({
    required this.projects,
    required this.timeEntries,
    this.locationAreas = const [],
    this.invoices = const [],
    this.clients = const [],
    this.schemaVersion = 3,
    String? timestamp,
  }) : timestamp = timestamp ?? DateTime.now().toIso8601String();

  factory BackupData.fromJson(Map<String, dynamic> json) {
    // Parse projects
    final List<Project> projects = (json['projects'] as List)
        .map((projectJson) => Project.fromJson(projectJson))
        .toList();

    // Parse time entries
    final Map<String, List<TimeEntry>> timeEntries = {};
    final Map<String, dynamic> entriesJson = json['timeEntries'];

    entriesJson.forEach((projectId, entriesList) {
      timeEntries[projectId] = (entriesList as List)
          .map((entryJson) => TimeEntry.fromJson(entryJson))
          .toList();
    });

    // Parse location areas (for backward compatibility, default to empty list)
    final List<LocationArea> locationAreas = [];
    if (json.containsKey('locationAreas') && json['locationAreas'] != null) {
      locationAreas.addAll((json['locationAreas'] as List)
          .map((areaJson) => LocationArea.fromJson(areaJson))
          .toList());
    }

    // Parse invoices (for backward compatibility, default to empty list)
    final List<Invoice> invoices = [];
    if (json.containsKey('invoices') && json['invoices'] != null) {
      invoices.addAll((json['invoices'] as List)
          .map((invoiceJson) => Invoice.fromJson(invoiceJson))
          .toList());
    }

    // Parse clients (for backward compatibility, default to empty list)
    final List<Client> clients = [];
    if (json.containsKey('clients') && json['clients'] != null) {
      clients.addAll((json['clients'] as List)
          .map((clientJson) => Client.fromJson(clientJson))
          .toList());
    }

    // Handle potential timestamp type mismatch (int vs String)
    String? finalTimestamp;
    if (json['timestamp'] is int) {
      // Convert int (likely milliseconds since epoch) to ISO string
      finalTimestamp = DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int, isUtc: true).toIso8601String();
      debugPrint('Converted integer timestamp to string: $finalTimestamp');
    } else if (json['timestamp'] is String) {
      finalTimestamp = json['timestamp'] as String?;
    } else if (json['timestamp'] != null) {
      // Handle unexpected type by trying toString()
      debugPrint('Warning: Unexpected timestamp type: ${json['timestamp'].runtimeType}. Trying toString().');
      finalTimestamp = json['timestamp'].toString();
    }

    final schemaVersion = (json['version'] as int?) ?? (json['schemaVersion'] as int?) ?? 1;

    // Warn about older schema versions for debugging
    if (schemaVersion < 2) {
      debugPrint('Loading older backup (schema v$schemaVersion) - location areas will be empty');
    }

    return BackupData(
      projects: projects,
      timeEntries: timeEntries,
      locationAreas: locationAreas,
      invoices: invoices,
      clients: clients,
      schemaVersion: schemaVersion,
      timestamp: finalTimestamp,
    );
  }

  Map<String, dynamic> toJson() {
    // Convert time entries to JSON
    final Map<String, dynamic> timeEntriesJson = {};
    timeEntries.forEach((projectId, entries) {
      timeEntriesJson[projectId] = entries.map((entry) => entry.toJson()).toList();
    });

    return {
      'projects': projects.map((project) => project.toJson()).toList(),
      'timeEntries': timeEntriesJson,
      'locationAreas': locationAreas.map((area) => area.toJson()).toList(),
      'invoices': invoices.map((invoice) => invoice.toJson()).toList(),
      'clients': clients.map((client) => client.toJson()).toList(),
      'version': schemaVersion,
      'timestamp': timestamp,
    };
  }
}

import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';

part 'project.g.dart';

@HiveType(typeId: 0)
class Project extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  int order;

  @HiveField(3)
  int? minimumWeeklyHours;

  @HiveField(4)
  int maxWeeksInOverview;

  @HiveField(5)
  final String createdAt;

  @HiveField(6)
  String? colorHex;

  @HiveField(7)
  int? iconDataCodePoint;

  @HiveField(8)
  int? roundingMinutes;

  Project({
    String? id,
    required this.name,
    this.order = 0,
    this.minimumWeeklyHours,
    this.maxWeeksInOverview = 3,
    String? createdAt,
    this.colorHex,
    this.iconDataCodePoint,
    this.roundingMinutes,
  }) :
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now().toIso8601String();

  Color? get color {
    if (colorHex == null) return null;
    final buffer = StringBuffer();
    if (colorHex!.length == 6 || colorHex!.length == 7) buffer.write('ff');
    buffer.write(colorHex!.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  IconData? get iconData {
    if (iconDataCodePoint == null) return null;
    return IconData(iconDataCodePoint!, fontFamily: 'MaterialIcons');
  }

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      id: json['id'],
      name: json['name'],
      order: json['order'] ?? 0,
      minimumWeeklyHours: json['minimumWeeklyHours'] as int?,
      maxWeeksInOverview: json['maxWeeksInOverview'] ?? 3,
      createdAt: json['createdAt'] as String?,
      colorHex: json['colorHex'] as String?,
      iconDataCodePoint: json['iconDataCodePoint'] as int?,
      roundingMinutes: json['roundingMinutes'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'order': order,
      'minimumWeeklyHours': minimumWeeklyHours,
      'maxWeeksInOverview': maxWeeksInOverview,
      'createdAt': createdAt,
      'colorHex': colorHex,
      'iconDataCodePoint': iconDataCodePoint,
      'roundingMinutes': roundingMinutes,
    };
  }

  Project copyWith({
    String? name,
    int? order,
    int? minimumWeeklyHours,
    int? maxWeeksInOverview,
    String? colorHex,
    int? iconDataCodePoint,
    int? roundingMinutes,
  }) {
    return Project(
      id: id,
      name: name ?? this.name,
      order: order ?? this.order,
      minimumWeeklyHours: minimumWeeklyHours ?? this.minimumWeeklyHours,
      maxWeeksInOverview: maxWeeksInOverview ?? this.maxWeeksInOverview,
      createdAt: createdAt,
      colorHex: colorHex ?? this.colorHex,
      iconDataCodePoint: iconDataCodePoint ?? this.iconDataCodePoint,
      roundingMinutes: roundingMinutes ?? this.roundingMinutes,
    );
  }
}

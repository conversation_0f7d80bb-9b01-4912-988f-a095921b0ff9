import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'time_entry.g.dart';

@HiveType(typeId: 1)
class TimeEntry extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String projectId;

  @HiveField(2)
  final String date;

  @HiveField(3)
  final String? start;

  @HiveField(4)
  final String? end;

  @HiveField(5)
  final String? duration;

  @HiveField(6)
  final bool inProgress;

  TimeEntry({
    String? id,
    required this.projectId,
    required this.date,
    this.start,
    this.end,
    this.duration,
    this.inProgress = false,
  }) : id = id ?? const Uuid().v4() {
    // Validate that either both start and end are provided, or duration is provided
    if ((start == null || end == null) && duration == null) {
      throw ArgumentError('Either both start and end times or duration must be provided');
    }
    if (start != null && end != null && duration != null) {
      throw ArgumentError('Cannot provide both time range and duration');
    }
  }

  factory TimeEntry.fromJson(Map<String, dynamic> json) {
    // ID from JSON is an int, convert to String for Flutter model
    String idString;
    if (json['id'] == null) {
      // Fallback if ID is missing in JSON, though unlikely for backups
      idString = const Uuid().v4();
    } else if (json['id'] is int) {
      idString = (json['id'] as int).toString();
    } else if (json['id'] is String) {
      idString = json['id'] as String;
    } else {
      // Unexpected type, fallback
      idString = json['id'].toString();
    }

    return TimeEntry(
      id: idString,
      projectId: json['projectId'] as String,
      date: json['date'] as String,
      start: json['start'] as String?,
      end: json['end'] as String?,
      duration: json['duration'] as String?,
      inProgress: json['inProgress'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'date': date,
      'start': start,
      'end': end,
      'duration': duration,
      'inProgress': inProgress,
    };
  }

  TimeEntry copyWith({
    String? projectId,
    String? date,
    String? start,
    String? end,
    String? duration,
    bool? inProgress,
  }) {
    return TimeEntry(
      id: id,
      projectId: projectId ?? this.projectId,
      date: date ?? this.date,
      start: start ?? this.start,
      end: end ?? this.end,
      duration: duration ?? this.duration,
      inProgress: inProgress ?? this.inProgress,
    );
  }

  bool get isTimeRange => start != null && end != null;
  
  // A time entry is considered active if it has both start and end times,
  // but represents an ongoing activity rather than a completed one.
  // We'll represent this by adding a small metadata field indicating an active entry
  bool get isActive => inProgress;
}

import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'invoice_models.g.dart';

@HiveType(typeId: 7)
class Address extends HiveObject {
  @HiveField(0)
  final String street;

  @HiveField(1)
  final String? street2;

  @HiveField(2)
  final String city;

  @HiveField(3)
  final String? state;

  @HiveField(4)
  final String postalCode;

  @HiveField(5)
  final String country;

  Address({
    required this.street,
    this.street2,
    required this.city,
    this.state,
    required this.postalCode,
    required this.country,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'] as String,
      street2: json['street2'] as String?,
      city: json['city'] as String,
      state: json['state'] as String?,
      postalCode: json['postalCode'] as String,
      country: json['country'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'street2': street2,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'country': country,
    };
  }

  Address copyWith({
    String? street,
    String? street2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
  }) {
    return Address(
      street: street ?? this.street,
      street2: street2 ?? this.street2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
    );
  }

  String get formattedAddress {
    final parts = <String>[];
    parts.add(street);
    if (street2 != null && street2!.isNotEmpty) {
      parts.add(street2!);
    }
    parts.add(city);
    if (state != null && state!.isNotEmpty) {
      parts.add(state!);
    }
    parts.add(postalCode);
    parts.add(country);
    return parts.join(', ');
  }

  bool isValid() {
    return street.isNotEmpty &&
           city.isNotEmpty &&
           postalCode.isNotEmpty &&
           country.isNotEmpty;
  }

  @override
  String toString() => formattedAddress;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Address &&
           other.street == street &&
           other.street2 == street2 &&
           other.city == city &&
           other.state == state &&
           other.postalCode == postalCode &&
           other.country == country;
  }

  @override
  int get hashCode {
    return Object.hash(street, street2, city, state, postalCode, country);
  }
}

@HiveType(typeId: 5)
class Client extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String? email;

  @HiveField(3)
  String? phone;

  @HiveField(4)
  Address? address;

  @HiveField(5)
  String? taxId;

  @HiveField(6)
  String? notes;

  @HiveField(7)
  final String createdAt;

  @HiveField(8)
  String? updatedAt;

  Client({
    String? id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.taxId,
    this.notes,
    String? createdAt,
    this.updatedAt,
  }) :
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now().toIso8601String();

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id'] as String?,
      name: json['name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] != null
          ? Address.fromJson(json['address'] as Map<String, dynamic>)
          : null,
      taxId: json['taxId'] as String?,
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address?.toJson(),
      'taxId': taxId,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Client copyWith({
    String? name,
    String? email,
    String? phone,
    Address? address,
    String? taxId,
    String? notes,
    String? updatedAt,
  }) {
    return Client(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      taxId: taxId ?? this.taxId,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now().toIso8601String(),
    );
  }

  bool isValid() {
    if (name.trim().isEmpty) return false;
    if (email != null && email!.isNotEmpty && !_isValidEmail(email!)) return false;
    if (address != null && !address!.isValid()) return false;
    return true;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 9)
enum InvoiceLineItemType {
  @HiveField(0)
  timeEntry,

  @HiveField(1)
  expense,

  @HiveField(2)
  discount,

  @HiveField(3)
  adjustment,
}

@HiveType(typeId: 6)
class InvoiceLineItem extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String description;

  @HiveField(2)
  double quantity;

  @HiveField(3)
  double rate;

  @HiveField(4)
  double amount;

  @HiveField(5)
  InvoiceLineItemType type;

  @HiveField(6)
  String? timeEntryId;

  InvoiceLineItem({
    String? id,
    required this.description,
    required this.quantity,
    required this.rate,
    double? amount,
    required this.type,
    this.timeEntryId,
  }) :
    id = id ?? const Uuid().v4(),
    amount = amount ?? (quantity * rate);

  factory InvoiceLineItem.fromJson(Map<String, dynamic> json) {
    return InvoiceLineItem(
      id: json['id'] as String?,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      amount: (json['amount'] as num?)?.toDouble(),
      type: InvoiceLineItemType.values[json['type'] as int],
      timeEntryId: json['timeEntryId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'quantity': quantity,
      'rate': rate,
      'amount': amount,
      'type': type.index,
      'timeEntryId': timeEntryId,
    };
  }

  InvoiceLineItem copyWith({
    String? description,
    double? quantity,
    double? rate,
    double? amount,
    InvoiceLineItemType? type,
    String? timeEntryId,
  }) {
    final newQuantity = quantity ?? this.quantity;
    final newRate = rate ?? this.rate;
    final newAmount = amount ?? (newQuantity * newRate);

    return InvoiceLineItem(
      id: id,
      description: description ?? this.description,
      quantity: newQuantity,
      rate: newRate,
      amount: newAmount,
      type: type ?? this.type,
      timeEntryId: timeEntryId ?? this.timeEntryId,
    );
  }

  void recalculateAmount() {
    amount = quantity * rate;
  }

  bool isValid() {
    return description.trim().isNotEmpty &&
           quantity >= 0 &&
           rate >= 0;
  }

  @override
  String toString() => '$description (${quantity}x$rate = $amount)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceLineItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@HiveType(typeId: 8)
enum InvoiceStatus {
  @HiveField(0)
  draft,

  @HiveField(1)
  sent,

  @HiveField(2)
  paid,

  @HiveField(3)
  overdue,

  @HiveField(4)
  cancelled,
}

@HiveType(typeId: 10)
enum InvoiceTemplate {
  @HiveField(0)
  professional,

  @HiveField(1)
  minimal,

  @HiveField(2)
  detailed,
}

@HiveType(typeId: 11)
class BusinessInfo extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  String? email;

  @HiveField(2)
  String? phone;

  @HiveField(3)
  Address? address;

  @HiveField(4)
  String? website;

  @HiveField(5)
  String? taxId;

  @HiveField(6)
  String? logoPath;

  BusinessInfo({
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.website,
    this.taxId,
    this.logoPath,
  });

  factory BusinessInfo.fromJson(Map<String, dynamic> json) {
    return BusinessInfo(
      name: json['name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] != null
          ? Address.fromJson(json['address'] as Map<String, dynamic>)
          : null,
      website: json['website'] as String?,
      taxId: json['taxId'] as String?,
      logoPath: json['logoPath'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'address': address?.toJson(),
      'website': website,
      'taxId': taxId,
      'logoPath': logoPath,
    };
  }

  BusinessInfo copyWith({
    String? name,
    String? email,
    String? phone,
    Address? address,
    String? website,
    String? taxId,
    String? logoPath,
  }) {
    return BusinessInfo(
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      website: website ?? this.website,
      taxId: taxId ?? this.taxId,
      logoPath: logoPath ?? this.logoPath,
    );
  }

  bool isValid() {
    if (name.trim().isEmpty) return false;
    if (email != null && email!.isNotEmpty && !_isValidEmail(email!)) return false;
    if (address != null && !address!.isValid()) return false;
    return true;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BusinessInfo &&
           other.name == name &&
           other.email == email &&
           other.phone == phone &&
           other.address == address &&
           other.website == website &&
           other.taxId == taxId &&
           other.logoPath == logoPath;
  }

  @override
  int get hashCode {
    return Object.hash(name, email, phone, address, website, taxId, logoPath);
  }
}

@HiveType(typeId: 12)
class InvoiceSettings extends HiveObject {
  @HiveField(0)
  String defaultCurrency;

  @HiveField(1)
  String defaultLocale;

  @HiveField(2)
  InvoiceTemplate defaultTemplate;

  @HiveField(3)
  BusinessInfo? businessInfo;

  @HiveField(4)
  String invoiceNumberPrefix;

  @HiveField(5)
  String invoiceNumberDateFormat;

  @HiveField(6)
  int invoiceNumberSequenceLength;

  @HiveField(7)
  double defaultTaxRate;

  @HiveField(8)
  int defaultDueDays;

  @HiveField(9)
  String? defaultNotes;

  @HiveField(10)
  final String createdAt;

  @HiveField(11)
  String? updatedAt;

  @HiveField(12)
  String? defaultInvoiceLanguage;

  InvoiceSettings({
    this.defaultCurrency = 'USD',
    this.defaultLocale = 'en_US',
    this.defaultTemplate = InvoiceTemplate.professional,
    this.businessInfo,
    this.invoiceNumberPrefix = 'INV',
    this.invoiceNumberDateFormat = 'yyyyMM',
    this.invoiceNumberSequenceLength = 4,
    this.defaultTaxRate = 0.0,
    this.defaultDueDays = 30,
    this.defaultNotes,
    String? createdAt,
    this.updatedAt,
    this.defaultInvoiceLanguage,
  }) : createdAt = createdAt ?? DateTime.now().toIso8601String();

  factory InvoiceSettings.fromJson(Map<String, dynamic> json) {
    return InvoiceSettings(
      defaultCurrency: json['defaultCurrency'] as String? ?? 'USD',
      defaultLocale: json['defaultLocale'] as String? ?? 'en_US',
      defaultTemplate: json['defaultTemplate'] != null
          ? InvoiceTemplate.values[json['defaultTemplate'] as int]
          : InvoiceTemplate.professional,
      businessInfo: json['businessInfo'] != null
          ? BusinessInfo.fromJson(json['businessInfo'] as Map<String, dynamic>)
          : null,
      invoiceNumberPrefix: json['invoiceNumberPrefix'] as String? ?? 'INV',
      invoiceNumberDateFormat: json['invoiceNumberDateFormat'] as String? ?? 'yyyyMM',
      invoiceNumberSequenceLength: json['invoiceNumberSequenceLength'] as int? ?? 4,
      defaultTaxRate: (json['defaultTaxRate'] as num?)?.toDouble() ?? 0.0,
      defaultDueDays: json['defaultDueDays'] as int? ?? 30,
      defaultNotes: json['defaultNotes'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      defaultInvoiceLanguage: json['defaultInvoiceLanguage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'defaultCurrency': defaultCurrency,
      'defaultLocale': defaultLocale,
      'defaultTemplate': defaultTemplate.index,
      'businessInfo': businessInfo?.toJson(),
      'invoiceNumberPrefix': invoiceNumberPrefix,
      'invoiceNumberDateFormat': invoiceNumberDateFormat,
      'invoiceNumberSequenceLength': invoiceNumberSequenceLength,
      'defaultTaxRate': defaultTaxRate,
      'defaultDueDays': defaultDueDays,
      'defaultNotes': defaultNotes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'defaultInvoiceLanguage': defaultInvoiceLanguage,
    };
  }

  InvoiceSettings copyWith({
    String? defaultCurrency,
    String? defaultLocale,
    InvoiceTemplate? defaultTemplate,
    BusinessInfo? businessInfo,
    String? invoiceNumberPrefix,
    String? invoiceNumberDateFormat,
    int? invoiceNumberSequenceLength,
    double? defaultTaxRate,
    int? defaultDueDays,
    String? defaultNotes,
    String? updatedAt,
    String? defaultInvoiceLanguage,
  }) {
    return InvoiceSettings(
      defaultCurrency: defaultCurrency ?? this.defaultCurrency,
      defaultLocale: defaultLocale ?? this.defaultLocale,
      defaultTemplate: defaultTemplate ?? this.defaultTemplate,
      businessInfo: businessInfo ?? this.businessInfo,
      invoiceNumberPrefix: invoiceNumberPrefix ?? this.invoiceNumberPrefix,
      invoiceNumberDateFormat: invoiceNumberDateFormat ?? this.invoiceNumberDateFormat,
      invoiceNumberSequenceLength: invoiceNumberSequenceLength ?? this.invoiceNumberSequenceLength,
      defaultTaxRate: defaultTaxRate ?? this.defaultTaxRate,
      defaultDueDays: defaultDueDays ?? this.defaultDueDays,
      defaultNotes: defaultNotes ?? this.defaultNotes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now().toIso8601String(),
      defaultInvoiceLanguage: defaultInvoiceLanguage ?? this.defaultInvoiceLanguage,
    );
  }

  bool isValid() {
    if (defaultCurrency.trim().isEmpty) return false;
    if (defaultLocale.trim().isEmpty) return false;
    if (invoiceNumberPrefix.trim().isEmpty) return false;
    if (invoiceNumberSequenceLength < 1 || invoiceNumberSequenceLength > 10) return false;
    if (defaultTaxRate < 0 || defaultTaxRate > 100) return false;
    if (defaultDueDays < 0) return false;
    if (businessInfo != null && !businessInfo!.isValid()) return false;
    return true;
  }

  String generateInvoiceNumberPreview() {
    final now = DateTime.now();
    final dateStr = _formatDateForInvoiceNumber(now, invoiceNumberDateFormat);
    final sequenceStr = '1'.padLeft(invoiceNumberSequenceLength, '0');
    return '$invoiceNumberPrefix-$dateStr-$sequenceStr';
  }

  String _formatDateForInvoiceNumber(DateTime date, String format) {
    switch (format) {
      case 'yyyy':
        return date.year.toString();
      case 'yyyyMM':
        return '${date.year}${date.month.toString().padLeft(2, '0')}';
      case 'yyyyMMdd':
        return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
      default:
        return '${date.year}${date.month.toString().padLeft(2, '0')}';
    }
  }

  @override
  String toString() => 'InvoiceSettings($defaultCurrency, $defaultTemplate)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceSettings &&
           other.defaultCurrency == defaultCurrency &&
           other.defaultLocale == defaultLocale &&
           other.defaultTemplate == defaultTemplate &&
           other.businessInfo == businessInfo &&
           other.invoiceNumberPrefix == invoiceNumberPrefix &&
           other.invoiceNumberDateFormat == invoiceNumberDateFormat &&
           other.invoiceNumberSequenceLength == invoiceNumberSequenceLength &&
           other.defaultTaxRate == defaultTaxRate &&
           other.defaultDueDays == defaultDueDays &&
           other.defaultNotes == defaultNotes &&
           other.defaultInvoiceLanguage == defaultInvoiceLanguage;
  }

  @override
  int get hashCode {
    return Object.hash(
      defaultCurrency,
      defaultLocale,
      defaultTemplate,
      businessInfo,
      invoiceNumberPrefix,
      invoiceNumberDateFormat,
      invoiceNumberSequenceLength,
      defaultTaxRate,
      defaultDueDays,
      defaultNotes,
      defaultInvoiceLanguage,
    );
  }
}

@HiveType(typeId: 4)
class Invoice extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String invoiceNumber;

  @HiveField(2)
  String clientId;

  @HiveField(3)
  List<String> timeEntryIds;

  @HiveField(4)
  List<InvoiceLineItem> additionalItems;

  @HiveField(5)
  String currency;

  @HiveField(6)
  String locale;

  @HiveField(7)
  DateTime issueDate;

  @HiveField(8)
  DateTime? dueDate;

  @HiveField(9)
  InvoiceStatus status;

  @HiveField(10)
  double subtotal;

  @HiveField(11)
  double taxRate;

  @HiveField(12)
  double taxAmount;

  @HiveField(13)
  double total;

  @HiveField(14)
  String? notes;

  @HiveField(15)
  final String createdAt;

  @HiveField(16)
  String? updatedAt;

  @HiveField(17)
  DateTime? servicePeriodStart;

  @HiveField(18)
  DateTime? servicePeriodEnd;

  Invoice({
    String? id,
    required this.invoiceNumber,
    required this.clientId,
    List<String>? timeEntryIds,
    List<InvoiceLineItem>? additionalItems,
    this.currency = 'USD',
    this.locale = 'en_US',
    DateTime? issueDate,
    this.dueDate,
    this.status = InvoiceStatus.draft,
    this.subtotal = 0.0,
    this.taxRate = 0.0,
    this.taxAmount = 0.0,
    this.total = 0.0,
    this.notes,
    String? createdAt,
    this.updatedAt,
    this.servicePeriodStart,
    this.servicePeriodEnd,
  }) :
    id = id ?? const Uuid().v4(),
    timeEntryIds = timeEntryIds ?? [],
    additionalItems = additionalItems ?? [],
    issueDate = issueDate ?? DateTime.now(),
    createdAt = createdAt ?? DateTime.now().toIso8601String();

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'] as String?,
      invoiceNumber: json['invoiceNumber'] as String,
      clientId: json['clientId'] as String,
      timeEntryIds: (json['timeEntryIds'] as List<dynamic>?)?.cast<String>() ?? [],
      additionalItems: (json['additionalItems'] as List<dynamic>?)
          ?.map((item) => InvoiceLineItem.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      currency: json['currency'] as String? ?? 'USD',
      locale: json['locale'] as String? ?? 'en_US',
      issueDate: json['issueDate'] != null
          ? DateTime.parse(json['issueDate'] as String)
          : DateTime.now(),
      dueDate: json['dueDate'] != null
          ? DateTime.parse(json['dueDate'] as String)
          : null,
      status: json['status'] != null
          ? InvoiceStatus.values[json['status'] as int]
          : InvoiceStatus.draft,
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      taxRate: (json['taxRate'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['taxAmount'] as num?)?.toDouble() ?? 0.0,
      total: (json['total'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      servicePeriodStart: json['servicePeriodStart'] != null
          ? DateTime.parse(json['servicePeriodStart'] as String)
          : null,
      servicePeriodEnd: json['servicePeriodEnd'] != null
          ? DateTime.parse(json['servicePeriodEnd'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'clientId': clientId,
      'timeEntryIds': timeEntryIds,
      'additionalItems': additionalItems.map((item) => item.toJson()).toList(),
      'currency': currency,
      'locale': locale,
      'issueDate': issueDate.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'status': status.index,
      'subtotal': subtotal,
      'taxRate': taxRate,
      'taxAmount': taxAmount,
      'total': total,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'servicePeriodStart': servicePeriodStart?.toIso8601String(),
      'servicePeriodEnd': servicePeriodEnd?.toIso8601String(),
    };
  }

  Invoice copyWith({
    String? invoiceNumber,
    String? clientId,
    List<String>? timeEntryIds,
    List<InvoiceLineItem>? additionalItems,
    String? currency,
    String? locale,
    DateTime? issueDate,
    DateTime? dueDate,
    InvoiceStatus? status,
    double? subtotal,
    double? taxRate,
    double? taxAmount,
    double? total,
    String? notes,
    String? updatedAt,
    DateTime? servicePeriodStart,
    DateTime? servicePeriodEnd,
  }) {
    return Invoice(
      id: id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      clientId: clientId ?? this.clientId,
      timeEntryIds: timeEntryIds ?? List<String>.from(this.timeEntryIds),
      additionalItems: additionalItems ?? List<InvoiceLineItem>.from(this.additionalItems),
      currency: currency ?? this.currency,
      locale: locale ?? this.locale,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      subtotal: subtotal ?? this.subtotal,
      taxRate: taxRate ?? this.taxRate,
      taxAmount: taxAmount ?? this.taxAmount,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now().toIso8601String(),
      servicePeriodStart: servicePeriodStart ?? this.servicePeriodStart,
      servicePeriodEnd: servicePeriodEnd ?? this.servicePeriodEnd,
    );
  }

  void recalculateTotals() {
    // Calculate subtotal from additional items
    subtotal = additionalItems.fold(0.0, (sum, item) => sum + item.amount);

    // Calculate tax amount
    taxAmount = subtotal * (taxRate / 100);

    // Calculate total
    total = subtotal + taxAmount;

    // Update timestamp
    updatedAt = DateTime.now().toIso8601String();
  }

  bool isValid() {
    if (invoiceNumber.trim().isEmpty) return false;
    if (clientId.trim().isEmpty) return false;
    if (currency.trim().isEmpty) return false;
    if (locale.trim().isEmpty) return false;
    if (taxRate < 0 || taxRate > 100) return false;
    if (subtotal < 0 || taxAmount < 0 || total < 0) return false;

    // Validate all additional items
    for (final item in additionalItems) {
      if (!item.isValid()) return false;
    }

    return true;
  }

  bool isServicePeriodValid() {
    if (servicePeriodStart == null && servicePeriodEnd == null) return true;
    if (servicePeriodStart == null || servicePeriodEnd == null) return false;
    return servicePeriodEnd!.isAfter(servicePeriodStart!) || 
           servicePeriodEnd!.isAtSameMomentAs(servicePeriodStart!);
  }

  bool get isOverdue {
    if (dueDate == null || status == InvoiceStatus.paid || status == InvoiceStatus.cancelled) {
      return false;
    }
    return DateTime.now().isAfter(dueDate!) && status != InvoiceStatus.paid;
  }

  int get daysPastDue {
    if (!isOverdue) return 0;
    return DateTime.now().difference(dueDate!).inDays;
  }

  @override
  String toString() => 'Invoice $invoiceNumber ($status)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Invoice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
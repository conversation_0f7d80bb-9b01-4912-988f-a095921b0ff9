// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_area.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationAreaAdapter extends TypeAdapter<LocationArea> {
  @override
  final int typeId = 3;

  @override
  LocationArea read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationArea(
      id: fields[0] as String?,
      projectId: fields[1] as String,
      name: fields[2] as String,
      centerLatitude: fields[3] as double,
      centerLongitude: fields[4] as double,
      radius: fields[5] as double,
      createdAt: fields[6] as String?,
      isActive: fields[7] as bool,
      cooldownTime: fields[8] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LocationArea obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.projectId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.centerLatitude)
      ..writeByte(4)
      ..write(obj.centerLongitude)
      ..writeByte(5)
      ..write(obj.radius)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.isActive)
      ..writeByte(8)
      ..write(obj.cooldownTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationAreaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

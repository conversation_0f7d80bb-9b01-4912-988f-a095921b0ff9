import 'dart:async';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/services/location/location_events.dart';

/// Widget that demonstrates the new event-based location tracking system
/// This replaces the old polling approach with real-time event streams
class LocationTrackingWidget extends StatefulWidget {
  final String? projectId;
  final Widget child;

  const LocationTrackingWidget({
    Key? key,
    this.projectId,
    required this.child,
  }) : super(key: key);

  @override
  State<LocationTrackingWidget> createState() => _LocationTrackingWidgetState();
}

class _LocationTrackingWidgetState extends State<LocationTrackingWidget> {
  final LocationService _locationService = LocationService();
  StreamSubscription<LocationEvent>? _eventSubscription;
  StreamSubscription<TrackingStatus>? _statusSubscription;

  TrackingStatus? _currentStatus;
  LocationEvent? _lastEvent;

  @override
  void initState() {
    super.initState();
    _subscribeToLocationEvents();
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _statusSubscription?.cancel();
    super.dispose();
  }

  void _subscribeToLocationEvents() {
    // Listen to location events (area entered/exited, time entries created/updated)
    _eventSubscription = _locationService.eventStream.listen((event) {
      if (mounted) {
        setState(() {
          _lastEvent = event;
        });

        // Handle specific events
        switch (event.type) {
          case LocationEventType.areaEntered:
            _showLocationSnackBar(
              'Started tracking time for ${event.projectName} in ${event.areaName}',
              Colors.green,
            );
            break;
          case LocationEventType.areaExited:
            _showLocationSnackBar(
              'Stopped tracking time for ${event.projectName}',
              Colors.orange,
            );
            break;
          case LocationEventType.trackingStarted:
            _showLocationSnackBar(
              'Location tracking started',
              Colors.blue,
            );
            break;
          case LocationEventType.trackingStopped:
            _showLocationSnackBar(
              'Location tracking stopped',
              Colors.grey,
            );
            break;
          case LocationEventType.timeEntryCreated:
            debugPrint('Time entry created: ${event.timeEntryId}');
            break;
          case LocationEventType.timeEntryResumed:
            debugPrint('Time entry resumed: ${event.timeEntryId}');
            break;
          case LocationEventType.timeEntryUpdated:
            debugPrint('Time entry updated: ${event.timeEntryId}');
            break;
          case LocationEventType.statusChanged:
            debugPrint('Tracking status changed');
            break;
        }
      }
    });

    // Listen to status changes (tracking active/inactive, current project, etc.)
    _statusSubscription = _locationService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
        });
      }
    });

    // Get initial status
    _currentStatus = _locationService.currentStatus;
  }

  void _showLocationSnackBar(String message, Color color) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.location_on, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,

        // Show tracking status indicator
        if (_currentStatus?.isActive == true)
          Positioned(
            top: 8,
            right: 8,
            child: _buildTrackingIndicator(),
          ),
      ],
    );
  }

  Widget _buildTrackingIndicator() {
    final status = _currentStatus;
    if (status == null || !status.isActive) {
      return const SizedBox.shrink();
    }

    final activeCount = status.activeTimeEntries.length;
    final currentProject = status.currentProjectName;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.location_on,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            currentProject != null
                ? 'Tracking $currentProject'
                : 'Tracking ($activeCount)',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (status.trackingStartTime != null) ...[
            const SizedBox(width: 8),
            StreamBuilder<void>(
              stream: Stream.periodic(const Duration(seconds: 1)),
              builder: (context, snapshot) {
                final duration = _locationService.formatTrackingDuration(
                  status.trackingStartTime!,
                );
                return Text(
                  duration,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }
}

/// Extension methods for easier integration with existing widgets
extension LocationTrackingExtension on Widget {
  /// Wrap any widget with location tracking capabilities
  Widget withLocationTracking({String? projectId}) {
    return LocationTrackingWidget(
      projectId: projectId,
      child: this,
    );
  }
}

/// Utility widget for project-specific tracking status
class ProjectTrackingStatus extends StatefulWidget {
  final String projectId;
  final String projectName;

  const ProjectTrackingStatus({
    Key? key,
    required this.projectId,
    required this.projectName,
  }) : super(key: key);

  @override
  State<ProjectTrackingStatus> createState() => _ProjectTrackingStatusState();
}

class _ProjectTrackingStatusState extends State<ProjectTrackingStatus> {
  final LocationService _locationService = LocationService();
  StreamSubscription<LocationEvent>? _eventSubscription;
  StreamSubscription<TrackingStatus>? _statusSubscription;

  bool _isTracking = false;
  String? _currentLocationName;
  DateTime? _trackingStartTime;

  @override
  void initState() {
    super.initState();
    _checkInitialState();
    _subscribeToEvents();
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _statusSubscription?.cancel();
    super.dispose();
  }

  void _checkInitialState() async {
    _isTracking = await _locationService.isProjectBeingTracked(widget.projectId);
    if (_isTracking) {
      final info = await _locationService.getActiveTrackingInfo(widget.projectId);
      if (info != null && mounted) {
        setState(() {
          _currentLocationName = info['locationName'] as String?;
          _trackingStartTime = info['startTime'] as DateTime?;
        });
      }
    }
  }

  void _subscribeToEvents() {
    // Listen for events related to this specific project
    _eventSubscription = _locationService.eventStream
        .where((event) => event.projectId == widget.projectId)
        .listen((event) {
      if (!mounted) return;

      switch (event.type) {
        case LocationEventType.areaEntered:
          setState(() {
            _isTracking = true;
            _currentLocationName = event.areaName;
            _trackingStartTime = event.trackingStartTime ?? event.timestamp;
          });
          break;
        case LocationEventType.areaExited:
          setState(() {
            _isTracking = false;
            _currentLocationName = null;
            _trackingStartTime = null;
          });
          break;
        case LocationEventType.timeEntryResumed:
        case LocationEventType.timeEntryCreated:
          if (!_isTracking) {
            setState(() {
              _isTracking = true;
              _trackingStartTime = event.trackingStartTime ?? event.timestamp;
            });
            _checkInitialState();
          }
          break;
        default:
          break;
      }
    });

    // Listen for general status changes
    _statusSubscription = _locationService.statusStream.listen((status) {
      if (!mounted) return;

      // Check if this project is still being tracked
      _checkInitialState();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isTracking) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.location_on,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            _currentLocationName ?? 'Tracking',
            style: TextStyle(
              color: Colors.green,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (_trackingStartTime != null) ...[
            const SizedBox(width: 8),
            StreamBuilder<void>(
              stream: Stream.periodic(const Duration(seconds: 1)),
              builder: (context, snapshot) {
                final duration = _locationService.formatTrackingDuration(
                  _trackingStartTime!,
                );
                return Text(
                  duration,
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }
}
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/report_models.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/reporting_service.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class ReportsDialog extends StatefulWidget {
  const ReportsDialog({super.key});

  @override
  State<ReportsDialog> createState() => _ReportsDialogState();
}

class _ReportsDialogState extends State<ReportsDialog> {
  final DatabaseService _databaseService = DatabaseService();
  final ReportingService _reportingService = ReportingService();

  List<Project> _projects = [];
  String? _selectedProjectId; // null means "All Projects"
  DateTimeRange? _dateRange;
  TimeReport? _reportData;
  bool _isLoading = true;
  bool _isGeneratingReport = false;

  // Add a constant for "All Projects"
  static const String _allProjectsId = '__all__';

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    setState(() { _isLoading = true; });
    try {
      await _databaseService.initialize();
      _projects = await _databaseService.getProjects();
      _projects.sort((a, b) => a.order.compareTo(b.order)); // Sort projects
      await _initializeDateRange();
      if (_dateRange != null) {
        await _generateReport();
      }
    } catch (e) {
      debugPrint("Error initializing reports: $e");
      if (mounted) {
        showErrorSnackBar('Error initializing reports: $e', context);
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  Future<void> _initializeDateRange() async {
    final allEntries = await _databaseService.getTimeEntries(); // Get all entries initially
    if (allEntries.isEmpty) {
      final today = DateTime.now();
      _dateRange = DateTimeRange(start: today, end: today);
      return;
    }

    allEntries.sort((a, b) => a.date.compareTo(b.date));
    try {
       _dateRange = DateTimeRange(
         // Parse date strings to DateTime
         start: DateTime.parse(allEntries.first.date),
         end: DateTime.parse(allEntries.last.date),
       );
    } catch (e) {
       debugPrint("Error parsing date for initial range: $e");
       // Fallback if parsing fails
       final today = DateTime.now();
       _dateRange = DateTimeRange(start: today, end: today);
    }
  }

  Future<void> _updateDateRangeForProject(String? projectId) async {
     final entries = await _databaseService.getTimeEntries(projectId: projectId == _allProjectsId ? null : projectId);
     if (entries.isEmpty) {
       // Keep current range or set default if no entries found for project?
       // Let's keep the current range for now to avoid resetting unnecessarily
       if (_dateRange == null) {
          final today = DateTime.now();
         _dateRange = DateTimeRange(start: today, end: today);
       }
       return;
     }
     entries.sort((a, b) => a.date.compareTo(b.date));
     try {
       final newRange = DateTimeRange(
         // Parse date strings to DateTime
         start: DateTime.parse(entries.first.date),
         end: DateTime.parse(entries.last.date),
       );
        // Only update if the range actually changed
       if (newRange != _dateRange) {
          setState(() {
            _dateRange = newRange;
          });
       }
     } catch (e) {
        debugPrint("Error parsing date for project range update: $e");
        // Handle error: Maybe keep the existing range or show a message?
     }

  }


  Future<void> _generateReport() async {
    if (_dateRange == null) return;
    setState(() { _isGeneratingReport = true; });
    try {
      _reportData = await _reportingService.generateReport(
        _dateRange!,
        projectId: _selectedProjectId == _allProjectsId ? null : _selectedProjectId,
      );
    } catch (e) {
      debugPrint("Error generating report: $e");
       if (mounted) {
        showErrorSnackBar('Error generating report: $e', context);
      }
      _reportData = null; // Clear previous report on error
    } finally {
       if (mounted) {
        setState(() { _isGeneratingReport = false; });
      }
    }
  }

  Future<void> _selectDateRange() async {
    final initialRange = _dateRange ?? DateTimeRange(start: DateTime.now(), end: DateTime.now());
    final firstDate = DateTime(2000);
    final lastDate = DateTime.now().add(const Duration(days: 365));

    final picked = await showDateRangePicker(
      context: context,
      initialDateRange: initialRange,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null && picked != _dateRange) {
      setState(() {
        _dateRange = picked;
      });
      await _generateReport();
    }
  }

   Future<void> _downloadCsv() async {
    if (_reportData == null) return;

    try {
      final csvData = await _reportingService.generateCsvData(_reportData!);
      final String csvString = const ListToCsvConverter().convert(csvData);

      final directory = await getTemporaryDirectory();
      final locale = LocaleDateUtils.getCurrentLocale(context);
      final startDate = await LocaleDateUtils.formatDateWithCustom(_reportData!.dateRange.start, locale);
      final endDate = await LocaleDateUtils.formatDateWithCustom(_reportData!.dateRange.end, locale);
      final fileName = 'time-report-$startDate-to-$endDate.csv';
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsString(csvString);

      // Use the new SharePlus API
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(filePath)],
          text: 'Time Report $startDate - $endDate',
        ),
      );
    } catch (e) {
       debugPrint("Error downloading CSV: $e");
        if (mounted) {
         showErrorSnackBar('Error creating CSV: $e', context);
       }
    }
  }

  Future<String> _formatDateRange() async {
    if (_dateRange == null) return 'Select Date Range';
    final startDate = await LocaleDateUtils.formatDateWithCustom(_dateRange!.start, LocaleDateUtils.getCurrentLocale(context));
    final endDate = await LocaleDateUtils.formatDateWithCustom(_dateRange!.end, LocaleDateUtils.getCurrentLocale(context));
    return '$startDate - $endDate';
  }

  @override
  Widget build(BuildContext context) {
    // Check screen width to apply responsive values
    final bool isSmallScreen = MediaQuery.of(context).size.width < 400;

    // Define responsive values
    final double titleFontSize = 18.0;
    final double loadingIndicatorStrokeWidth = 3.0;
    final double dropdownHintFontSize = 14.0;
    final double dropdownItemFontSize = 14.0;
    final double dropdownContentHorizontalPadding = 12.0;
    final double dropdownContentVerticalPadding = 8.0;
    final double spacerHeight = 16.0;
    final double datePickerIconSize = 20.0;
    final double datePickerLabelFontSize = 13.0;
    final double datePickerVerticalPadding = 12.0;
    final double downloadIconSize = 24.0;
    final double noDataFontSize = 14.0;
    // Adjust column spacing based on screen size
    final double tableColumnSpacing = isSmallScreen ? 6.0 : 18.0;
    final double tableHeadingRowHeight = 40.0;
    final double tableDataRowHeight = 40.0;
    // Smaller font size for small screens
    final double tableCellFontSize = isSmallScreen ? 11.0 : 12.0;
    final double tableHeaderFontSize = isSmallScreen ? 12.0 : 13.0;
    final double closeButtonFontSize = 14.0;

    // Dynamically set column sizes based on screen width
    final ColumnSize projectColSize = isSmallScreen ? ColumnSize.S : ColumnSize.L;
    final ColumnSize dateColSize = isSmallScreen ? ColumnSize.S : ColumnSize.M;
    final ColumnSize durationColSize = ColumnSize.S;
    final ColumnSize totalColSize = ColumnSize.S;

    return ResponsiveDialog(
      title: Text('Generate Time Report', style: TextStyle(fontSize: titleFontSize)),
      content: _isLoading
          ? Center(child: CircularProgressIndicator(strokeWidth: loadingIndicatorStrokeWidth))
          : SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.7,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<String?>(
                    value: _selectedProjectId ?? _allProjectsId,
                    hint: Text('Select Project', style: TextStyle(fontSize: dropdownHintFontSize)),
                    isExpanded: true,
                    items: [
                      DropdownMenuItem<String?>(
                        value: _allProjectsId,
                        child: Text('All Projects', style: TextStyle(fontSize: dropdownItemFontSize)),
                      ),
                      ..._projects.map((project) {
                        return DropdownMenuItem<String?>(
                          value: project.id,
                          child: Text(project.name, overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: dropdownItemFontSize)),
                        );
                      }),
                    ],
                    onChanged: (value) async {
                      setState(() {
                        _selectedProjectId = value == _allProjectsId ? null : value;
                      });
                      await _generateReport();
                    },
                    decoration: InputDecoration(
                      border: const OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: dropdownContentHorizontalPadding,
                        vertical: dropdownContentVerticalPadding
                      ),
                    ),
                  ),
                  SizedBox(height: spacerHeight),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _selectDateRange,
                          icon: Icon(Icons.calendar_today, size: datePickerIconSize),
                          label: _dateRange != null
                              ? FutureBuilder<String>(
                                  future: _formatDateRange(),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState == ConnectionState.waiting) {
                                      return const Text('Loading...');
                                    } else if (snapshot.hasError) {
                                      return const Text('Error loading dates');
                                    } else {
                                      return Text(
                                        snapshot.data!,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(fontSize: datePickerLabelFontSize),
                                      );
                                    }
                                  },
                                )
                              : Text(
                                  'Select Date Range',
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(fontSize: datePickerLabelFontSize),
                                ),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: datePickerVerticalPadding),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8.0),
                      Tooltip(
                        message: 'Download CSV',
                        child: IconButton(
                          icon: Icon(Icons.download, size: downloadIconSize),
                          onPressed: (_reportData != null && !_isGeneratingReport) ? _downloadCsv : null,
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: spacerHeight),
                  Expanded(
                    child: _isGeneratingReport
                        ? Center(child: CircularProgressIndicator(strokeWidth: loadingIndicatorStrokeWidth))
                        : _reportData == null || _reportData!.projectBreakdown.isEmpty
                            ? Center(child: Text('No data for selected range.', style: TextStyle(fontSize: noDataFontSize)))
                            : Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: DataTable2(
                                  columnSpacing: tableColumnSpacing,
                                  headingRowHeight: tableHeadingRowHeight,
                                  dataRowHeight: tableDataRowHeight,
                                  fixedTopRows: 1,
                                  minWidth: isSmallScreen ? 350 : 450,
                                  horizontalMargin: isSmallScreen ? 8.0 : 12.0,
                                  empty: Center(child: Text('No data available')),
                                  columns: [
                                    DataColumn2(
                                      label: Text('Project', style: TextStyle(fontWeight: FontWeight.bold, fontSize: tableHeaderFontSize)),
                                      size: projectColSize,
                                    ),
                                    DataColumn2(
                                      label: Text('Date', style: TextStyle(fontWeight: FontWeight.bold, fontSize: tableHeaderFontSize)),
                                      size: dateColSize,
                                    ),
                                    DataColumn2(
                                      label: Text('Duration', style: TextStyle(fontWeight: FontWeight.bold, fontSize: tableHeaderFontSize)),
                                      numeric: true,
                                      size: durationColSize,
                                    ),
                                    DataColumn2(
                                      label: Text(isSmallScreen ? 'Total' : 'Project Total',
                                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: tableHeaderFontSize)),
                                      numeric: true,
                                      size: totalColSize,
                                    ),
                                  ],
                                  rows: _buildTableRows(tableCellFontSize),
                                ),
                              ),
                  ),
                ],
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Close', style: TextStyle(fontSize: closeButtonFontSize)),
        ),
      ],
      maxWidth: 600,
    );
  }

 List<DataRow> _buildTableRows(double cellFontSize) {
    final List<DataRow> rows = [];
    if (_reportData == null) return rows;

    // Check if we're on a small screen
    final bool isSmallScreen = MediaQuery.of(context).size.width < 400;

    final sortedProjects = _reportData!.projectBreakdown.values.toList()
                           ..sort((a, b) => a.order.compareTo(b.order));

    for (int i = 0; i < sortedProjects.length; i++) {
      final projectReport = sortedProjects[i];
      bool firstEntryOfProject = true;
      for (final entry in projectReport.entries) {
        rows.add(DataRow2(
          cells: [
            DataCell(Text(
              // Trim project name on small screens if too long
              isSmallScreen && projectReport.projectName.length > 12
                ? '${projectReport.projectName.substring(0, 10)}...'
                : projectReport.projectName,
              style: TextStyle(fontSize: cellFontSize),
              overflow: TextOverflow.ellipsis,
            )),
            DataCell(FutureBuilder<String>(
              future: isSmallScreen
                ? LocaleDateUtils.formatDateAsync(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context))
                : LocaleDateUtils.formatDateAsync(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context)),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Text(
                    isSmallScreen
                      ? LocaleDateUtils.formatShortDate(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context))
                      : LocaleDateUtils.formatDate(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context)),
                    style: TextStyle(fontSize: cellFontSize),
                  );
                } else if (snapshot.hasError) {
                  return Text(
                    isSmallScreen
                      ? LocaleDateUtils.formatShortDate(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context))
                      : LocaleDateUtils.formatDate(DateTime.parse(entry.date), LocaleDateUtils.getCurrentLocale(context)),
                    style: TextStyle(fontSize: cellFontSize),
                  );
                } else {
                  return Text(
                    snapshot.data!,
                    style: TextStyle(fontSize: cellFontSize),
                  );
                }
              },
            )),
            DataCell(Text(entry.duration, style: TextStyle(fontSize: cellFontSize))),
            DataCell(Text(firstEntryOfProject ? projectReport.totalHours : '', style: TextStyle(fontSize: cellFontSize))),
          ],
        ));
        firstEntryOfProject = false;
      }

      if (i < sortedProjects.length - 1) {
         rows.add(DataRow2(
           cells: [
             DataCell(SizedBox.shrink()),
             DataCell(SizedBox.shrink()),
             DataCell(SizedBox.shrink()),
             DataCell(SizedBox.shrink()),
           ],
         ));
      }
    }
    return rows;
  }
}
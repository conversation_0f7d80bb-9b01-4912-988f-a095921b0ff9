import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// A widget for selecting currencies with search functionality and popular currencies section
class CurrencyPicker extends StatefulWidget {
  /// Currently selected currency code
  final String? selectedCurrency;

  /// Callback when a currency is selected
  final ValueChanged<String> onCurrencySelected;

  /// Whether to show the popular currencies section
  final bool showPopularCurrencies;

  /// Whether to show currency formatting preview
  final bool showFormattingPreview;

  /// Custom locale for formatting preview (if null, uses system locale)
  final Locale? previewLocale;

  /// Whether to validate currency support and show warnings
  final bool validateCurrency;

  /// Custom title for the picker dialog
  final String? title;

  /// Whether to show as a dialog or inline widget
  final bool showAsDialog;

  const CurrencyPicker({
    super.key,
    this.selectedCurrency,
    required this.onCurrencySelected,
    this.showPopularCurrencies = true,
    this.showFormattingPreview = true,
    this.previewLocale,
    this.validateCurrency = true,
    this.title,
    this.showAsDialog = true,
  });

  @override
  State<CurrencyPicker> createState() => _CurrencyPickerState();
}

class _CurrencyPickerState extends State<CurrencyPicker> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _allCurrencies = [];
  List<Map<String, dynamic>> _filteredCurrencies = [];
  List<String> _popularCurrencies = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _initializeCurrencies();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _initializeCurrencies() {
    _allCurrencies = CurrencyService.getCurrenciesSortedByName();
    _popularCurrencies = CurrencyService.getPopularCurrencies();
    _filteredCurrencies = List.from(_allCurrencies);
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      if (_searchQuery.isEmpty) {
        _filteredCurrencies = List.from(_allCurrencies);
      } else {
        _filteredCurrencies = _allCurrencies.where((currency) {
          final code = currency['code'].toString().toLowerCase();
          final name = currency['name'].toString().toLowerCase();
          return code.contains(_searchQuery) || name.contains(_searchQuery);
        }).toList();
      }
    });
  }

  void _selectCurrency(String currencyCode) {
    if (widget.validateCurrency && !CurrencyService.isCurrencySupported(currencyCode)) {
      _showUnsupportedCurrencyDialog(currencyCode);
      return;
    }

    widget.onCurrencySelected(currencyCode);
    if (widget.showAsDialog) {
      Navigator.of(context).pop();
    }
  }

  void _showUnsupportedCurrencyDialog(String currencyCode) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsupported Currency'),
        content: Text(
          'The currency "$currencyCode" is not currently supported. '
          'Please select a different currency or contact support to request this currency.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search currencies...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget _buildPopularCurrenciesSection() {
    if (!widget.showPopularCurrencies || _searchQuery.isNotEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            'Popular Currencies',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: _popularCurrencies.length,
            itemBuilder: (context, index) {
              final currencyCode = _popularCurrencies[index];
              final currencyInfo = CurrencyService.getCurrencyInfo(currencyCode);
              final isSelected = widget.selectedCurrency == currencyCode;

              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: 12.0),
                child: Card(
                  elevation: isSelected ? 4 : 1,
                  color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
                  child: InkWell(
                    onTap: () => _selectCurrency(currencyCode),
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            currencyInfo?['symbol'] ?? currencyCode,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.onPrimaryContainer
                                  : null,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            currencyCode,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.onPrimaryContainer
                                  : Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          if (widget.showFormattingPreview)
                            FutureBuilder<String>(
                              future: _getFormattingPreview(currencyCode),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? '...',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: isSelected 
                                        ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7)
                                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const Divider(height: 32),
      ],
    );
  }

  Widget _buildCurrencyList() {
    return Expanded(
      child: ListView.builder(
        itemCount: _filteredCurrencies.length,
        itemBuilder: (context, index) {
          final currency = _filteredCurrencies[index];
          final currencyCode = currency['code'] as String;
          final currencyName = currency['name'] as String;
          final currencySymbol = currency['symbol'] as String;
          final isSelected = widget.selectedCurrency == currencyCode;
          final isSupported = CurrencyService.isCurrencySupported(currencyCode);

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: isSelected 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surfaceVariant,
              child: Text(
                currencySymbol,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isSelected 
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
            title: Text(
              currencyName,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(currencyCode),
                if (widget.showFormattingPreview)
                  FutureBuilder<String>(
                    future: _getFormattingPreview(currencyCode),
                    builder: (context, snapshot) {
                      return Text(
                        'Example: ${snapshot.data ?? '...'}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                        ),
                      );
                    },
                  ),
                if (widget.validateCurrency && !isSupported)
                  Text(
                    'Not supported',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
            trailing: isSelected 
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  )
                : null,
            onTap: () => _selectCurrency(currencyCode),
            selected: isSelected,
          );
        },
      ),
    );
  }

  Future<String> _getFormattingPreview(String currencyCode) async {
    try {
      if (widget.previewLocale != null) {
        return CurrencyService.getCurrencyFormattingExample(currencyCode, widget.previewLocale);
      }
      
      // Try to get effective locale, but fall back to default if it fails
      try {
        final locale = await LocaleDateUtils.getEffectiveLocale(context);
        return CurrencyService.getCurrencyFormattingExample(currencyCode, locale);
      } catch (e) {
        // Fallback to default formatting if locale service fails
        return CurrencyService.getCurrencyFormattingExample(currencyCode);
      }
    } catch (e) {
      return CurrencyService.getCurrencyFormattingExample(currencyCode);
    }
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildSearchField(),
        _buildPopularCurrenciesSection(),
        _buildCurrencyList(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsDialog) {
      return AlertDialog(
        title: Text(widget.title ?? 'Select Currency'),
        contentPadding: EdgeInsets.zero,
        content: SizedBox(
          width: double.maxFinite,
          height: 500,
          child: _buildContent(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      );
    } else {
      return _buildContent();
    }
  }
}

/// A button widget that opens the currency picker dialog
class CurrencyPickerButton extends StatelessWidget {
  /// Currently selected currency code
  final String? selectedCurrency;

  /// Callback when a currency is selected
  final ValueChanged<String> onCurrencySelected;

  /// Whether to show the popular currencies section
  final bool showPopularCurrencies;

  /// Whether to show currency formatting preview
  final bool showFormattingPreview;

  /// Custom locale for formatting preview
  final Locale? previewLocale;

  /// Whether to validate currency support
  final bool validateCurrency;

  /// Custom title for the picker dialog
  final String? title;

  /// Button text when no currency is selected
  final String? placeholder;

  /// Whether the button is enabled
  final bool enabled;

  const CurrencyPickerButton({
    super.key,
    this.selectedCurrency,
    required this.onCurrencySelected,
    this.showPopularCurrencies = true,
    this.showFormattingPreview = true,
    this.previewLocale,
    this.validateCurrency = true,
    this.title,
    this.placeholder = 'Select Currency',
    this.enabled = true,
  });

  void _showCurrencyPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CurrencyPicker(
        selectedCurrency: selectedCurrency,
        onCurrencySelected: onCurrencySelected,
        showPopularCurrencies: showPopularCurrencies,
        showFormattingPreview: showFormattingPreview,
        previewLocale: previewLocale,
        validateCurrency: validateCurrency,
        title: title,
        showAsDialog: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currencyInfo = selectedCurrency != null 
        ? CurrencyService.getCurrencyInfo(selectedCurrency!)
        : null;

    return OutlinedButton.icon(
      onPressed: enabled ? () => _showCurrencyPicker(context) : null,
      icon: currencyInfo != null
          ? Text(
              currencyInfo['symbol'] ?? selectedCurrency!,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            )
          : const Icon(Icons.currency_exchange),
      label: Text(
        currencyInfo != null 
            ? '${currencyInfo['name']} (${selectedCurrency!})'
            : placeholder!,
      ),
    );
  }
}
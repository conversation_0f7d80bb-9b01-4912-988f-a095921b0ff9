import 'package:flutter/material.dart';

/// Common loading indicator widget with consistent styling across the app
class CommonLoadingIndicator extends StatelessWidget {
  final double? size;
  final double strokeWidth;
  final Color? color;
  final String? message;
  final bool showMessage;

  const CommonLoadingIndicator({
    super.key,
    this.size,
    this.strokeWidth = 2.0,
    this.color,
    this.message,
    this.showMessage = false,
  });

  /// Creates a small loading indicator for buttons and inline use
  const CommonLoadingIndicator.small({
    super.key,
    this.color,
  }) : size = 16,
       strokeWidth = 2.0,
       message = null,
       showMessage = false;

  /// Creates a medium loading indicator for cards and sections
  const CommonLoadingIndicator.medium({
    super.key,
    this.color,
  }) : size = 24,
       strokeWidth = 2.5,
       message = null,
       showMessage = false;

  /// Creates a large loading indicator for full screen loading
  const CommonLoadingIndicator.large({
    super.key,
    this.message,
    this.showMessage = true,
  }) : size = 32,
       strokeWidth = 3.0,
       color = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.primary;

    Widget indicator = SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        color: effectiveColor,
      ),
    );

    if (showMessage && message != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          const SizedBox(height: 16),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return indicator;
  }
}

/// Full screen loading overlay with consistent theming
class LoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;
  final Widget child;

  const LoadingOverlay({
    super.key,
    required this.child,
    this.message,
    this.isVisible = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isVisible)
          Container(
            color: Colors.black54,
            alignment: Alignment.center,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: CommonLoadingIndicator.large(
                message: message ?? 'Loading...',
              ),
            ),
          ),
      ],
    );
  }
}

/// Progress indicator for specific invoice operations
class InvoiceProgressIndicator extends StatelessWidget {
  final String operation;
  final double? progress;
  final bool isIndeterminate;

  const InvoiceProgressIndicator({
    super.key,
    required this.operation,
    this.progress,
    this.isIndeterminate = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isIndeterminate)
            const CommonLoadingIndicator.medium()
          else
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                value: progress,
                strokeWidth: 3.0,
                color: theme.colorScheme.primary,
              ),
            ),
          const SizedBox(height: 16),
          Text(
            operation,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          if (!isIndeterminate && progress != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                '${(progress! * 100).toInt()}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A widget for selecting locales with search functionality and popular locales section
class LocalePicker extends StatefulWidget {
  /// Currently selected locale
  final Locale? selectedLocale;

  /// Callback when a locale is selected
  final ValueChanged<Locale> onLocaleSelected;

  /// Whether to show the popular locales section
  final bool showPopularLocales;

  /// Whether to show locale formatting preview
  final bool showFormattingPreview;

  /// Custom title for the picker dialog
  final String? title;

  /// Whether to show as a dialog or inline widget
  final bool showAsDialog;

  /// List of supported locales (if null, uses common locales)
  final List<Locale>? supportedLocales;

  const LocalePicker({
    super.key,
    this.selectedLocale,
    required this.onLocaleSelected,
    this.showPopularLocales = true,
    this.showFormattingPreview = true,
    this.title,
    this.showAsDialog = true,
    this.supportedLocales,
  });

  @override
  State<LocalePicker> createState() => _LocalePickerState();
}

class _LocalePickerState extends State<LocalePicker> {
  final TextEditingController _searchController = TextEditingController();
  List<Locale> _allLocales = [];
  List<Locale> _filteredLocales = [];
  List<Locale> _popularLocales = [];
  String _searchQuery = '';

  // Common locales that are widely used
  static const List<Locale> _commonLocales = [
    Locale('en', 'US'), // English (United States)
    Locale('en', 'GB'), // English (United Kingdom)
    Locale('en', 'CA'), // English (Canada)
    Locale('en', 'AU'), // English (Australia)
    Locale('de', 'DE'), // German (Germany)
    Locale('fr', 'FR'), // French (France)
    Locale('es', 'ES'), // Spanish (Spain)
    Locale('it', 'IT'), // Italian (Italy)
    Locale('pt', 'PT'), // Portuguese (Portugal)
    Locale('pt', 'BR'), // Portuguese (Brazil)
    Locale('nl', 'NL'), // Dutch (Netherlands)
    Locale('ru', 'RU'), // Russian (Russia)
    Locale('ja', 'JP'), // Japanese (Japan)
    Locale('ko', 'KR'), // Korean (South Korea)
    Locale('zh', 'CN'), // Chinese (China)
    Locale('zh', 'TW'), // Chinese (Taiwan)
    Locale('ar', 'SA'), // Arabic (Saudi Arabia)
    Locale('hi', 'IN'), // Hindi (India)
    Locale('th', 'TH'), // Thai (Thailand)
    Locale('vi', 'VN'), // Vietnamese (Vietnam)
    Locale('tr', 'TR'), // Turkish (Turkey)
    Locale('pl', 'PL'), // Polish (Poland)
    Locale('cs', 'CZ'), // Czech (Czech Republic)
    Locale('hu', 'HU'), // Hungarian (Hungary)
    Locale('sv', 'SE'), // Swedish (Sweden)
    Locale('no', 'NO'), // Norwegian (Norway)
    Locale('da', 'DK'), // Danish (Denmark)
    Locale('fi', 'FI'), // Finnish (Finland)
    Locale('el', 'GR'), // Greek (Greece)
    Locale('he', 'IL'), // Hebrew (Israel)
  ];

  // Popular locales (most commonly used)
  static const List<Locale> _popularLocalesList = [
    Locale('en', 'US'),
    Locale('en', 'GB'),
    Locale('de', 'DE'),
    Locale('fr', 'FR'),
    Locale('es', 'ES'),
    Locale('ja', 'JP'),
    Locale('zh', 'CN'),
    Locale('pt', 'BR'),
  ];

  @override
  void initState() {
    super.initState();
    _initializeLocales();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _initializeLocales() {
    _allLocales = widget.supportedLocales ?? List.from(_commonLocales);
    _popularLocales = _popularLocalesList.where((locale) => _allLocales.contains(locale)).toList();
    _filteredLocales = List.from(_allLocales);
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      if (_searchQuery.isEmpty) {
        _filteredLocales = List.from(_allLocales);
      } else {
        _filteredLocales = _allLocales.where((locale) {
          final localeString = _getLocaleDisplayName(locale).toLowerCase();
          final localeCode = locale.toString().toLowerCase();
          return localeString.contains(_searchQuery) || localeCode.contains(_searchQuery);
        }).toList();
      }
    });
  }

  void _selectLocale(Locale locale) {
    widget.onLocaleSelected(locale);
    if (widget.showAsDialog) {
      Navigator.of(context).pop();
    }
  }

  String _getLocaleDisplayName(Locale locale) {
    try {
      // Try to get the native name of the locale
      final localeName = Intl.canonicalizedLocale(locale.toString());
      
      // Map of locale codes to display names
      const localeNames = {
        'en_US': 'English (United States)',
        'en_GB': 'English (United Kingdom)',
        'en_CA': 'English (Canada)',
        'en_AU': 'English (Australia)',
        'de_DE': 'Deutsch (Deutschland)',
        'fr_FR': 'Français (France)',
        'es_ES': 'Español (España)',
        'it_IT': 'Italiano (Italia)',
        'pt_PT': 'Português (Portugal)',
        'pt_BR': 'Português (Brasil)',
        'nl_NL': 'Nederlands (Nederland)',
        'ru_RU': 'Русский (Россия)',
        'ja_JP': '日本語 (日本)',
        'ko_KR': '한국어 (대한민국)',
        'zh_CN': '中文 (中国)',
        'zh_TW': '中文 (台灣)',
        'ar_SA': 'العربية (السعودية)',
        'hi_IN': 'हिन्दी (भारत)',
        'th_TH': 'ไทย (ประเทศไทย)',
        'vi_VN': 'Tiếng Việt (Việt Nam)',
        'tr_TR': 'Türkçe (Türkiye)',
        'pl_PL': 'Polski (Polska)',
        'cs_CZ': 'Čeština (Česká republika)',
        'hu_HU': 'Magyar (Magyarország)',
        'sv_SE': 'Svenska (Sverige)',
        'no_NO': 'Norsk (Norge)',
        'da_DK': 'Dansk (Danmark)',
        'fi_FI': 'Suomi (Suomi)',
        'el_GR': 'Ελληνικά (Ελλάδα)',
        'he_IL': 'עברית (ישראל)',
      };

      return localeNames[localeName] ?? '${locale.languageCode}_${locale.countryCode}';
    } catch (e) {
      return locale.toString();
    }
  }

  String _getLocaleFlag(Locale locale) {
    // Map of country codes to flag emojis
    const countryFlags = {
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'ES': '🇪🇸',
      'IT': '🇮🇹',
      'PT': '🇵🇹',
      'BR': '🇧🇷',
      'NL': '🇳🇱',
      'RU': '🇷🇺',
      'JP': '🇯🇵',
      'KR': '🇰🇷',
      'CN': '🇨🇳',
      'TW': '🇹🇼',
      'SA': '🇸🇦',
      'IN': '🇮🇳',
      'TH': '🇹🇭',
      'VN': '🇻🇳',
      'TR': '🇹🇷',
      'PL': '🇵🇱',
      'CZ': '🇨🇿',
      'HU': '🇭🇺',
      'SE': '🇸🇪',
      'NO': '🇳🇴',
      'DK': '🇩🇰',
      'FI': '🇫🇮',
      'GR': '🇬🇷',
      'IL': '🇮🇱',
    };

    return countryFlags[locale.countryCode] ?? '🌐';
  }

  Widget _buildSearchField() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search locales...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget _buildPopularLocalesSection() {
    if (!widget.showPopularLocales || _searchQuery.isNotEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            'Popular Locales',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: _popularLocales.length,
            itemBuilder: (context, index) {
              final locale = _popularLocales[index];
              final isSelected = widget.selectedLocale == locale;

              return Container(
                width: 120,
                margin: const EdgeInsets.only(right: 12.0),
                child: Card(
                  elevation: isSelected ? 4 : 1,
                  color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
                  child: InkWell(
                    onTap: () => _selectLocale(locale),
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _getLocaleFlag(locale),
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            locale.toString(),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.onPrimaryContainer
                                  : Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          if (widget.showFormattingPreview)
                            Text(
                              _getFormattingPreview(locale),
                              style: TextStyle(
                                fontSize: 10,
                                color: isSelected 
                                    ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7)
                                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const Divider(height: 32),
      ],
    );
  }

  Widget _buildLocaleList() {
    return Expanded(
      child: ListView.builder(
        itemCount: _filteredLocales.length,
        itemBuilder: (context, index) {
          final locale = _filteredLocales[index];
          final displayName = _getLocaleDisplayName(locale);
          final flag = _getLocaleFlag(locale);
          final isSelected = widget.selectedLocale == locale;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: isSelected 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surfaceVariant,
              child: Text(
                flag,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            title: Text(
              displayName,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(locale.toString()),
                if (widget.showFormattingPreview)
                  Text(
                    'Example: ${_getFormattingPreview(locale)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
              ],
            ),
            trailing: isSelected 
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  )
                : null,
            onTap: () => _selectLocale(locale),
            selected: isSelected,
          );
        },
      ),
    );
  }

  String _getFormattingPreview(Locale locale) {
    try {
      final now = DateTime.now();
      final dateFormat = DateFormat.yMMMd(locale.toString());
      return dateFormat.format(now);
    } catch (e) {
      return locale.toString();
    }
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildSearchField(),
        _buildPopularLocalesSection(),
        _buildLocaleList(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsDialog) {
      return AlertDialog(
        title: Text(widget.title ?? 'Select Locale'),
        contentPadding: EdgeInsets.zero,
        content: SizedBox(
          width: double.maxFinite,
          height: 500,
          child: _buildContent(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      );
    } else {
      return _buildContent();
    }
  }
}

/// A button widget that opens the locale picker dialog
class LocalePickerButton extends StatelessWidget {
  /// Currently selected locale
  final Locale? selectedLocale;

  /// Callback when a locale is selected
  final ValueChanged<Locale> onLocaleSelected;

  /// Whether to show the popular locales section
  final bool showPopularLocales;

  /// Whether to show locale formatting preview
  final bool showFormattingPreview;

  /// Custom title for the picker dialog
  final String? title;

  /// Button text when no locale is selected
  final String? placeholder;

  /// Whether the button is enabled
  final bool enabled;

  /// List of supported locales
  final List<Locale>? supportedLocales;

  const LocalePickerButton({
    super.key,
    this.selectedLocale,
    required this.onLocaleSelected,
    this.showPopularLocales = true,
    this.showFormattingPreview = true,
    this.title,
    this.placeholder = 'Select Locale',
    this.enabled = true,
    this.supportedLocales,
  });

  void _showLocalePicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => LocalePicker(
        selectedLocale: selectedLocale,
        onLocaleSelected: onLocaleSelected,
        showPopularLocales: showPopularLocales,
        showFormattingPreview: showFormattingPreview,
        title: title,
        showAsDialog: true,
        supportedLocales: supportedLocales,
      ),
    );
  }

  String _getLocaleDisplayName(Locale locale) {
    const localeNames = {
      'en_US': 'English (US)',
      'en_GB': 'English (UK)',
      'de_DE': 'German',
      'fr_FR': 'French',
      'es_ES': 'Spanish',
      'ja_JP': 'Japanese',
      'zh_CN': 'Chinese',
      'pt_BR': 'Portuguese (BR)',
    };

    return localeNames[locale.toString()] ?? locale.toString();
  }

  String _getLocaleFlag(Locale locale) {
    const countryFlags = {
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'ES': '🇪🇸',
      'JP': '🇯🇵',
      'CN': '🇨🇳',
      'BR': '🇧🇷',
    };

    return countryFlags[locale.countryCode] ?? '🌐';
  }

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: enabled ? () => _showLocalePicker(context) : null,
      icon: selectedLocale != null
          ? Text(
              _getLocaleFlag(selectedLocale!),
              style: const TextStyle(fontSize: 16),
            )
          : const Icon(Icons.language),
      label: Text(
        selectedLocale != null 
            ? _getLocaleDisplayName(selectedLocale!)
            : placeholder!,
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/widgets/week_entry_group.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class AllWeeksDialog extends StatelessWidget {
  final String projectName;
  final Map<String, Map<String, dynamic>> weeklyEntries;
  final Map<String, bool> expandedWeeks;
  final Function(String) onToggleWeek;
  final Function(TimeEntry) onEditEntry;
  final Function(TimeEntry) onDeleteEntry;
  final Function(List<TimeEntry>) onDeleteWeek;
  final int? minimumWeeklyHours;
  final Color? projectColor;
  final String currentWeekKey;

  const AllWeeksDialog({
    super.key,
    required this.projectName,
    required this.weeklyEntries,
    required this.expandedWeeks,
    required this.onToggleWeek,
    required this.onEditEntry,
    required this.onDeleteEntry,
    required this.onDeleteWeek,
    required this.currentWeekKey,
    this.minimumWeeklyHours,
    this.projectColor,
  });

  @override
  Widget build(BuildContext context) {
    final weekKeys = weeklyEntries.keys.toList()
      ..sort((a, b) {
        final dateA = DateTime.parse(a.split(' - ')[0]);
        final dateB = DateTime.parse(b.split(' - ')[0]);
        return dateB.compareTo(dateA);
      });

    // Define responsive values for dialog elements
    final double itemHorizontalPadding = 12;
    final double itemVerticalPadding = 4;

    return ResponsiveDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'All Weeks - $projectName',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'View and manage all time entries by week',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.6,
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: weekKeys.length,
          physics: const AlwaysScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final weekKey = weekKeys[index];
            final weekData = weeklyEntries[weekKey]!;
            final isExpanded = expandedWeeks[weekKey] ?? false;

            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: itemHorizontalPadding,
                vertical: itemVerticalPadding
              ),
              child: WeekEntryGroup(
                weekKey: weekKey,
                totalTime: weekData['totalTime'] as String,
                entries: weekData['entries'] as List<TimeEntry>,
                isExpanded: isExpanded,
                onToggle: () => onToggleWeek(weekKey),
                onEdit: onEditEntry,
                onDelete: onDeleteEntry,
                onDeleteWeek: onDeleteWeek,
                isCurrentWeek: weekKey == currentWeekKey,
                minimumWeeklyHours: minimumWeeklyHours,
                projectColor: projectColor,
              ),
            );
          },
        ),
      ),
      scrollable: false,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      actions: [
        TextButton.icon(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          label: const Text('Close'),
        ),
      ],
      maxWidth: 600,
    );
  }
}

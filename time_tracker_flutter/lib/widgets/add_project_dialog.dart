import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

class AddProjectDialog extends StatefulWidget {
  final Function(Project) onProjectAdded;

  const AddProjectDialog({
    super.key,
    required this.onProjectAdded,
  });

  @override
  State<AddProjectDialog> createState() => _AddProjectDialogState();
}

class _AddProjectDialogState extends State<AddProjectDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _weeklyHoursController = TextEditingController();

  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _weeklyHoursController.dispose();
    super.dispose();
  }

  Future<void> _addProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the current number of projects to set the order
      final projects = await _databaseService.getProjects();
      final order = projects.length;

      // Create the new project
      final project = Project(
        name: _nameController.text,
        order: order,
        minimumWeeklyHours: _weeklyHoursController.text.isNotEmpty
            ? double.parse(_weeklyHoursController.text)
            : null,
      );

      // Save the project
      await _databaseService.saveProject(project);

      // Notify the parent
      widget.onProjectAdded(project);

      // Close the dialog
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error adding project: ${e.toString()}', context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Project'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Project Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a project name';
                }
                return null;
              },
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _weeklyHoursController,
              decoration: const InputDecoration(
                labelText: 'Target Hours per Week (optional)',
                border: OutlineInputBorder(),
                hintText: 'e.g. 40',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  try {
                    final hours = double.parse(value);
                    if (hours <= 0) {
                      return 'Hours must be greater than 0';
                    }
                  } catch (e) {
                    return 'Please enter a valid number';
                  }
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addProject,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Add'),
        ),
      ],
    );
  }
}

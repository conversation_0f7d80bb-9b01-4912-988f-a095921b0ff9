import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class RecoveryCodeDialog extends StatefulWidget {
  final String? recoveryCode;
  final VoidCallback? onCodeSaved;

  const RecoveryCodeDialog({
    super.key,
    required this.recoveryCode,
    this.onCodeSaved,
  });

  @override
  State<RecoveryCodeDialog> createState() => _RecoveryCodeDialogState();
}

class _RecoveryCodeDialogState extends State<RecoveryCodeDialog> {
  bool _codeCopied = false;

  void _copyCodeToClipboard() {
    if (widget.recoveryCode != null) {
      Clipboard.setData(ClipboardData(text: widget.recoveryCode!));
      setState(() {
        _codeCopied = true;
      });

      // Reset the copied state after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _codeCopied = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final borderColor = isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300;
    final codeBackgroundColor = isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100;
    final textColor = isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700;

    return ResponsiveDialog(
      title: const Text('Recovery Code'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Save this recovery code in a safe place. You\'ll need it to recover your backups if you lose your device.',
              style: TextStyle(fontSize: 14, color: textColor),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: codeBackgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: borderColor),
              ),
              child: SelectableText(
                widget.recoveryCode ?? 'Error generating recovery code',
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Write down this code or save it somewhere secure. Anyone with this code can access your backups.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      scrollable: true,
      actions: [
        TextButton.icon(
          onPressed: _copyCodeToClipboard,
          icon: Icon(_codeCopied ? Icons.check : Icons.copy),
          label: Text(_codeCopied ? 'Copied!' : 'Copy Code'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: () {
            if (widget.onCodeSaved != null) {
              widget.onCodeSaved!();
            }
            Navigator.pop(context);
          },
          child: const Text('I\'ve Saved It'),
        ),
      ],
    );
  }
}

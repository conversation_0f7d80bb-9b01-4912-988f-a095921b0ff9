import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';

/// A widget for selecting multiple time entries with filtering and grouping capabilities
class TimeEntrySelector extends StatefulWidget {
  /// List of initially selected time entry IDs
  final List<String> initialSelectedIds;

  /// Callback when selection changes
  final Function(List<TimeEntry> selectedEntries) onSelectionChanged;

  /// Optional project filter - if provided, only shows entries for this project
  final String? projectFilter;

  /// Whether to show only unbilled entries (entries not already in invoices)
  final bool showOnlyUnbilled;

  /// Optional date range filter
  final DateTimeRange? dateRange;

  /// Currency for formatting estimated totals
  final String currency;

  /// Locale for formatting numbers and currency
  final String locale;

  /// Default hourly rate for estimation (optional)
  final double? defaultHourlyRate;

  /// Callback when rate changes
  final Function(double rate)? onRateChanged;

  /// Current hourly rate from the controller
  final double? currentHourlyRate;

  /// Optional database service for dependency injection (mainly for testing)
  final DatabaseService? databaseService;

  const TimeEntrySelector({
    super.key,
    this.initialSelectedIds = const [],
    required this.onSelectionChanged,
    this.projectFilter,
    this.showOnlyUnbilled = true,
    this.dateRange,
    this.currency = 'USD',
    this.locale = 'en_US',
    this.defaultHourlyRate,
    this.onRateChanged,
    this.currentHourlyRate,
    this.databaseService,
  });

  @override
  State<TimeEntrySelector> createState() => _TimeEntrySelectorState();
}

class _TimeEntrySelectorState extends State<TimeEntrySelector> {
  late final DatabaseService _databaseService;

  List<TimeEntry> _allTimeEntries = [];
  Map<String, Project> _projects = {};
  Set<String> _selectedIds = {};
  Map<String, bool> _expandedProjects = {};
  Set<String> _billedTimeEntryIds = {};

  // Filter states
  DateTimeRange? _dateRange;
  bool _showOnlyUnbilled = true;
  String? _projectFilter;

  // Rate for estimation
  late final TextEditingController _rateController;
  double _hourlyRate = 50.0;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _databaseService = widget.databaseService ?? DatabaseService();
    _selectedIds = Set.from(widget.initialSelectedIds);
    _showOnlyUnbilled = widget.showOnlyUnbilled;
    _projectFilter = widget.projectFilter;
    _dateRange = widget.dateRange;

    // Initialize rate controller
    _hourlyRate = widget.defaultHourlyRate ?? 50.0;
    _rateController = TextEditingController(text: _hourlyRate.toString());

    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load projects
      final projects = await _databaseService.getProjects();
      _projects = {for (var project in projects) project.id: project};

      // Load time entries with basic filtering
      final timeEntries = await _databaseService.getTimeEntries(
        projectId: _projectFilter,
        startDate: _dateRange?.start,
        endDate: _dateRange?.end,
      );

      // Get billed time entry IDs
      _billedTimeEntryIds = await _databaseService.getBilledTimeEntryIds();

      // Filter out entries that are in progress, don't have proper time data, or are already billed
      _allTimeEntries = timeEntries.where((entry) {
        // Skip entries in progress
        if (entry.inProgress) return false;

        // Must have either duration or both start/end times
        if (entry.duration == null && (entry.start == null || entry.end == null)) {
          return false;
        }

        // Skip billed entries if showOnlyUnbilled is true
        if (_showOnlyUnbilled && _billedTimeEntryIds.contains(entry.id)) {
          return false;
        }

        return true;
      }).toList();

      // Sort by date (newest first)
      _allTimeEntries.sort((a, b) => b.date.compareTo(a.date));

      // Initialize expanded state for projects that have entries
      final projectsWithEntries = _allTimeEntries.map((e) => e.projectId).toSet();
      for (final projectId in projectsWithEntries) {
        _expandedProjects[projectId] ??= false; // Collapse by default
      }

    } catch (e) {
      debugPrint('Error loading time entries: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<TimeEntry> get _filteredTimeEntries {
    return _allTimeEntries.where((entry) {
      // Project filter
      if (_projectFilter != null && entry.projectId != _projectFilter) {
        return false;
      }

      // Date range filter
      if (_dateRange != null) {
        final entryDate = DateTime.parse(entry.date);
        if (entryDate.isBefore(_dateRange!.start) ||
            entryDate.isAfter(_dateRange!.end)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  Map<String, List<TimeEntry>> get _groupedEntries {
    final grouped = <String, List<TimeEntry>>{};

    for (final entry in _filteredTimeEntries) {
      grouped.putIfAbsent(entry.projectId, () => []).add(entry);
    }

    // Sort entries within each project by date (newest first)
    for (final entries in grouped.values) {
      entries.sort((a, b) => b.date.compareTo(a.date));
    }

    return grouped;
  }

  List<TimeEntry> get _selectedEntries {
    return _allTimeEntries.where((entry) => _selectedIds.contains(entry.id)).toList();
  }

  void _toggleSelection(String entryId) {
    setState(() {
      if (_selectedIds.contains(entryId)) {
        _selectedIds.remove(entryId);
      } else {
        _selectedIds.add(entryId);
      }
    });

    widget.onSelectionChanged(_selectedEntries);
  }

  void _toggleProjectSelection(String projectId) {
    final projectEntries = _groupedEntries[projectId] ?? [];
    final projectEntryIds = projectEntries.map((e) => e.id).toSet();

    setState(() {
      if (projectEntryIds.every((id) => _selectedIds.contains(id))) {
        // All selected, deselect all
        _selectedIds.removeAll(projectEntryIds);
      } else {
        // Some or none selected, select all
        _selectedIds.addAll(projectEntryIds);
      }
    });

    widget.onSelectionChanged(_selectedEntries);
  }

  void _updateRate(String value) {
    final rate = double.tryParse(value) ?? _hourlyRate;
    setState(() {
      _hourlyRate = rate;
    });

    // Notify the controller if a callback is provided
    if (widget.onRateChanged != null) {
      widget.onRateChanged!(rate);
    }
  }

  @override
  void dispose() {
    _rateController.dispose();
    super.dispose();
  }

  void _toggleProjectExpansion(String projectId) {
    setState(() {
      _expandedProjects[projectId] = !(_expandedProjects[projectId] ?? false);
    });
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
      _loadData();
    }
  }

  void _clearDateRange() {
    setState(() {
      _dateRange = null;
    });
    _loadData();
  }

  String _formatDuration(TimeEntry entry) {
    if (entry.duration != null) {
      return entry.duration!;
    } else if (entry.start != null && entry.end != null) {
      final minutes = calculateMinutesBetween(entry.start!, entry.end!);
      return minutesToTime(minutes);
    }
    return '0:00';
  }

  double _calculateHours(TimeEntry entry) {
    if (entry.duration != null) {
      return parseDurationToHours(entry.duration!);
    } else if (entry.start != null && entry.end != null) {
      return calculateHours(entry.start!, entry.end!);
    }
    return 0.0;
  }

  Widget _buildFilterBar() {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12.0 : 14.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.filter_list_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Wrap(
            spacing: isSmallScreen ? 8 : 12,
            runSpacing: 12,
            children: [
              // Date range filter
              Container(
                decoration: BoxDecoration(
                  gradient: _dateRange != null
                      ? LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                          ],
                        )
                      : null,
                  color: _dateRange == null
                      ? Theme.of(context).colorScheme.surfaceContainerHighest
                      : null,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _dateRange != null
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  boxShadow: _dateRange != null
                      ? [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _selectDateRange(),
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 12 : 14,
                        vertical: isSmallScreen ? 6 : 8
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.calendar_today_rounded,
                            size: isSmallScreen ? 14 : 16,
                            color: _dateRange != null
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Flexible(
                            child: Text(
                              _dateRange == null
                                ? 'All Dates'
                                : isSmallScreen
                                  ? '${LocaleDateUtils.formatShortDate(_dateRange!.start)} - ${LocaleDateUtils.formatShortDate(_dateRange!.end)}'
                                  : '${LocaleDateUtils.formatDate(_dateRange!.start)} - ${LocaleDateUtils.formatDate(_dateRange!.end)}',
                              style: TextStyle(
                                color: _dateRange != null
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.w600,
                                fontSize: isSmallScreen ? 12 : 13,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          if (_dateRange != null) ...[
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _clearDateRange,
                              child: Icon(
                                Icons.close_rounded,
                                size: 16,
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Unbilled filter
              Container(
                decoration: BoxDecoration(
                  gradient: _showOnlyUnbilled
                      ? LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                          ],
                        )
                      : null,
                  color: !_showOnlyUnbilled
                      ? Theme.of(context).colorScheme.surfaceContainerHighest
                      : null,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _showOnlyUnbilled
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  boxShadow: _showOnlyUnbilled
                      ? [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _showOnlyUnbilled = !_showOnlyUnbilled;
                      });
                      _loadData();
                    },
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 12 : 16,
                        vertical: isSmallScreen ? 6 : 8
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.receipt_long_rounded,
                            size: 16,
                            color: _showOnlyUnbilled
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Unbilled Only',
                            style: TextStyle(
                              color: _showOnlyUnbilled
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (_showOnlyUnbilled) ...[
                            const SizedBox(width: 8),
                            Icon(
                              Icons.check_rounded,
                              size: 16,
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final selectedEntries = _selectedEntries;
    final totalHours = selectedEntries.fold<double>(
      0.0,
      (sum, entry) => sum + _calculateHours(entry),
    );

    // Use the controller's rate if provided, otherwise use the local rate
    final rateToUse = widget.currentHourlyRate ?? _hourlyRate;
    final estimatedTotal = totalHours * rateToUse;

    // Update the controller if the rate has changed
    if (widget.currentHourlyRate != null && widget.currentHourlyRate != _hourlyRate) {
      setState(() {
        _hourlyRate = widget.currentHourlyRate!;
        _rateController.text = _hourlyRate.toString();
      });
    }

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 14.0 : 16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.summarize_rounded,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Selection Summary',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Entries',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '${selectedEntries.length}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Total Hours',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '${totalHours.toStringAsFixed(2)}h',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (selectedEntries.isNotEmpty) ...[
            SizedBox(height: isSmallScreen ? 12 : 14),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Estimated: ${CurrencyService.formatCurrency(
                      estimatedTotal,
                      widget.currency,
                      InvoiceLocalizationService.getLocaleFromLanguage(widget.locale),
                    )}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 14 : 16,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 100,
                  child: TextFormField(
                    controller: _rateController,
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Rate',
                      labelStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                      prefixText: CurrencyService.getCurrencySymbol(widget.currency),
                      prefixStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.3),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.1),
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    onChanged: _updateRate,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProjectGroup(String projectId, List<TimeEntry> entries) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final project = _projects[projectId];
    final projectName = project?.name ?? 'Unknown Project';
    final isExpanded = _expandedProjects[projectId] ?? false;

    final projectEntryIds = entries.map((e) => e.id).toSet();
    final selectedInProject = projectEntryIds.where((id) => _selectedIds.contains(id)).length;
    final allSelected = selectedInProject == entries.length;
    final someSelected = selectedInProject > 0 && selectedInProject < entries.length;

    final projectHours = entries.fold<double>(0.0, (sum, entry) => sum + _calculateHours(entry));

    return Container(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 10),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: allSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
              : someSelected
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: allSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: allSelected
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                : Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: allSelected ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 14),
            decoration: BoxDecoration(
              color: allSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : someSelected
                      ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                      : null,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: allSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : someSelected
                            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                            : Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: allSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : someSelected
                              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Checkbox(
                    value: allSelected,
                    tristate: true,
                    onChanged: (_) => _toggleProjectSelection(projectId),
                    activeColor: allSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.primary,
                    checkColor: allSelected
                        ? Theme.of(context).colorScheme.primaryContainer
                        : Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 12 : 14),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          if (project?.iconData != null) ...[
                            Icon(
                              project!.iconData,
                              color: project.color,
                              size: isSmallScreen ? 18 : 20,
                            ),
                            SizedBox(width: isSmallScreen ? 6 : 8),
                          ],
                          Expanded(
                            child: Text(
                              projectName,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: isSmallScreen ? 14 : 16,
                                color: allSelected
                                    ? Theme.of(context).colorScheme.onPrimaryContainer
                                    : Theme.of(context).colorScheme.onSurface,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      Text(
                        '${entries.length} entries • ${projectHours.toStringAsFixed(1)}h total',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: isSmallScreen ? 12 : 13,
                          color: allSelected
                              ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8)
                              : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: Icon(
                      isExpanded ? Icons.expand_less_rounded : Icons.expand_more_rounded,
                      color: allSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                    onPressed: () => _toggleProjectExpansion(projectId),
                  ),
                ),
              ],
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: entries.map((entry) => _buildTimeEntryTile(entry)).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeEntryTile(TimeEntry entry) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isSelected = _selectedIds.contains(entry.id);
    final isBilled = _billedTimeEntryIds.contains(entry.id);
    final duration = _formatDuration(entry);
    final hours = _calculateHours(entry);

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 12 : 16,
        vertical: isSmallScreen ? 3 : 4
      ),
      padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
      decoration: BoxDecoration(
        color: isSelected
            ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5)
            : isBilled
                ? Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
                : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
              : isBilled
                  ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : isBilled
                      ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)
                      : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : isBilled
                        ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.5)
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Checkbox(
              value: isSelected,
              onChanged: isBilled ? null : (_) => _toggleSelection(entry.id),
              activeColor: Theme.of(context).colorScheme.primary,
              checkColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        LocaleDateUtils.formatDate(DateTime.parse(entry.date)),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? Theme.of(context).colorScheme.onPrimaryContainer
                              : isBilled
                                  ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)
                                  : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    if (isBilled)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 4 : 6,
                          vertical: isSmallScreen ? 1 : 2
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'BILLED',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: isSmallScreen ? 3 : 4),
                if (entry.start != null && entry.end != null)
                  Text(
                    '${entry.start} - ${entry.end}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8)
                          : isBilled
                              ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4)
                              : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                Text(
                  'Duration: $duration (${hours.toStringAsFixed(2)}h)',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8)
                        : isBilled
                            ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4)
                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    final groupedEntries = _groupedEntries;

    if (groupedEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.access_time,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            SizedBox(height: isSmallScreen ? 12 : 16),
            Text(
              'No time entries found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),
            Text(
              'Try adjusting your filters or add some time entries first.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFilterBar(),
        SizedBox(height: isSmallScreen ? 16 : 20),
        _buildSummaryCard(),
        SizedBox(height: isSmallScreen ? 16 : 20),
        Flexible(
          child: ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              ...groupedEntries.entries.map((entry) =>
                Padding(
                  padding: EdgeInsets.only(bottom: isSmallScreen ? 6.0 : 8.0),
                  child: _buildProjectGroup(entry.key, entry.value),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class LocationAreaDialog extends StatefulWidget {
  final String projectId;
  final LocationArea? area;
  final Project project;

  const LocationAreaDialog({
    super.key,
    required this.projectId,
    required this.project,
    this.area,
  });

  @override
  State<LocationAreaDialog> createState() => _LocationAreaDialogState();
}

class _LocationAreaDialogState extends State<LocationAreaDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _radiusController = TextEditingController();
  final _cooldownController = TextEditingController();
  bool _isActive = true;
  LatLng _center = const LatLng(51.509865, -0.118092); // Default to London
  final _mapController = MapController();
  bool _isLoading = false;
  bool _permissionDenied = false;
  bool _mapReady = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
    _checkLocationPermission();
  }

  Future<void> _initializeFields() async {
    if (widget.area != null) {
      _nameController.text = widget.area!.name;
      _radiusController.text = widget.area!.radius.toString();
      _cooldownController.text = widget.area!.cooldownTime.toString();
      _isActive = widget.area!.isActive;
      _center = LatLng(widget.area!.centerLatitude, widget.area!.centerLongitude);
    } else {
      _nameController.text = '';
      _radiusController.text = '100'; // Default radius in meters
      _cooldownController.text = '60'; // Default cooldown in seconds
    }
  }

  Future<void> _checkLocationPermission() async {
    setState(() => _isLoading = true);

    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _permissionDenied = true;
          _isLoading = false;
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _permissionDenied = true;
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _permissionDenied = true;
          _isLoading = false;
        });
        return;
      }

      // Permission granted, get current position
      if (widget.area == null) {
        final position = await Geolocator.getCurrentPosition();
        setState(() {
          _center = LatLng(position.latitude, position.longitude);
        });
      }

      setState(() {
        _isLoading = false;
        _permissionDenied = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _permissionDenied = true;
      });
    }
  }

  Future<void> _saveLocationArea() async {
    if (!_formKey.currentState!.validate()) return;

    final name = _nameController.text.trim();
    final radius = double.parse(_radiusController.text);
    final cooldown = int.parse(_cooldownController.text);

    // Create or update location area
    LocationArea area;
    if (widget.area != null) {
      area = widget.area!.copyWith(
        name: name,
        centerLatitude: _center.latitude,
        centerLongitude: _center.longitude,
        radius: radius,
        isActive: _isActive,
        cooldownTime: cooldown,
      );
    } else {
      area = LocationArea(
        projectId: widget.projectId,
        name: name,
        centerLatitude: _center.latitude,
        centerLongitude: _center.longitude,
        radius: radius,
        isActive: _isActive,
        cooldownTime: cooldown,
      );
    }

    // Save to database and update tracking service if needed
    if (widget.area != null) {
      // For existing areas, use LocationService to handle updates and notify background service
      await LocationService().updateLocationArea(area);
    } else {
      // For new areas, save directly to database
      await DatabaseService().saveLocationArea(area);
    }

    if (mounted) {
      Navigator.of(context).pop(true);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _radiusController.dispose();
    _cooldownController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveDialog(
      title: Text(widget.area == null ? 'Add Location Area' : 'Edit Location Area'),
      content: _buildContent(),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _saveLocationArea,
          child: const Text('Save'),
        ),
      ],
      scrollable: true,
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const SizedBox(
        height: 300,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_permissionDenied) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Location permission is required to use this feature.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _checkLocationPermission,
                child: const Text('Request Permission'),
              ),
            ],
          ),
        ),
      );
    }

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Name',
              hintText: 'Enter a name for this location',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Map for selecting location
          SizedBox(
            height: 300,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                  initialCenter: _center,
                  initialZoom: 14,
                  onTap: (_, point) {
                    setState(() {
                      _center = point;
                    });
                  },
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                    userAgentPackageName: 'com.example.time_tracker_flutter',
                    maxZoom: 19,
                  ),
                  MarkerLayer(
                    markers: [
                      Marker(
                        height: 40,
                        width: 40,
                        point: _center,
                        child: const Icon(
                          Icons.location_on,
                          color: Colors.red,
                          size: 40,
                        ),
                      ),
                    ],
                  ),
                  CircleLayer(
                    circles: [
                      CircleMarker(
                        point: _center,
                        radius: double.tryParse(_radiusController.text) ?? 100,
                        color: Colors.blue.withOpacity(0.3),
                        borderStrokeWidth: 2,
                        borderColor: Colors.blue,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _radiusController,
                  decoration: const InputDecoration(
                    labelText: 'Radius (meters)',
                    hintText: 'Enter tracking radius',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a radius';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    if (double.parse(value) < 10) {
                      return 'Radius must be at least 10 meters';
                    }
                    return null;
                  },
                  onChanged: (_) => setState(() {}),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _cooldownController,
                  decoration: const InputDecoration(
                    labelText: 'Cooldown (seconds)',
                    hintText: 'Enter cooldown time',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter cooldown time';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    if (int.parse(value) < 1) {
                      return 'Cooldown must be at least 1 second';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Active toggle
          SwitchListTile(
            title: const Text('Active'),
            subtitle: const Text('Enable location tracking for this area'),
            value: _isActive,
            onChanged: (value) {
              setState(() {
                _isActive = value;
              });
            },
          ),
        ],
      ),
    );
  }
}
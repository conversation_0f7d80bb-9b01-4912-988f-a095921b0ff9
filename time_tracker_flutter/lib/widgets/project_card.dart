import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/index.dart';
import 'package:time_tracker_flutter/widgets/project_card_notifiers.dart';

/// ProjectCard is a wrapper widget that provides a consistent interface for displaying projects
/// It uses the EnhancedProjectCard implementation for modern UI
class ProjectCard extends StatefulWidget {
  final Project project;
  final Function(Project)? onEdit;
  final Function(Project)? onDelete;
  final Function(Project)? onAddTimeEntry;
  final ProjectCardNotifiers? notifiers;
  final int? projectIndex;
  final Function(int)? onReorderStart;
  final Function(Project)? onTap;

  const ProjectCard({
    super.key,
    required this.project,
    this.onEdit,
    this.onDelete,
    this.onAddTimeEntry,
    this.notifiers,
    this.projectIndex,
    this.onReorderStart,
    this.onTap,
  });

  @override
  State<ProjectCard> createState() => _ProjectCardState();
}

class _ProjectCardState extends State<ProjectCard> {
  late ProjectCardNotifiers _notifiers;
  late ValueNotifier<bool> _isCollapsed;
  late ValueNotifier<bool> _showAllWeeks;

  @override
  void initState() {
    super.initState();
    _isCollapsed = ValueNotifier<bool>(true);
    _showAllWeeks = ValueNotifier<bool>(false);
    _notifiers = widget.notifiers ?? ProjectCardNotifiers(
      isCollapsed: _isCollapsed,
      showAllWeeks: _showAllWeeks,
    );
  }

  @override
  void dispose() {
    if (widget.notifiers == null) {
      _isCollapsed.dispose();
      _showAllWeeks.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap != null ? () => widget.onTap!(widget.project) : null,
      child: EnhancedProjectCard(
        project: widget.project,
        onEdit: widget.onEdit ?? (_) {},
        onDelete: widget.onDelete ?? (_) {},
        onAddTimeEntry: widget.onAddTimeEntry ?? (_) {},
        notifiers: _notifiers,
        projectIndex: widget.projectIndex ?? 0,
        onReorderStart: widget.onReorderStart ?? (_) {},
      ),
    );
  }
}

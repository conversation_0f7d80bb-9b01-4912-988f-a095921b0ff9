import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class TransferQRDialog extends StatefulWidget {
  const TransferQRDialog({super.key});

  @override
  State<TransferQRDialog> createState() => _TransferQRDialogState();
}

class _TransferQRDialogState extends State<TransferQRDialog> {
  final DatabaseService _databaseService = DatabaseService();
  bool _dataCopied = false;
  String _transferData = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _generateTransferData();
  }

  Future<void> _generateTransferData() async {
    setState(() { _isLoading = true; });

    try {
      // The DatabaseService should handle the recovery code generation internally
      final transferData = await _databaseService.getTransferData();
      _transferData = jsonEncode(transferData);
    } catch (e) {
      _transferData = 'Error generating transfer data: ${e.toString().replaceFirst("Exception: ", "")}';
      debugPrint('Error in _generateTransferData: $e');
      if (mounted) {
        showErrorSnackBar('Error generating transfer data: ${e.toString().replaceFirst("Exception: ", "")}', context);
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  void _copyDataToClipboard() {
    Clipboard.setData(ClipboardData(text: _transferData));
    setState(() {
      _dataCopied = true;
    });

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _dataCopied = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? Colors.grey.shade900 : Colors.white;
    final borderColor = isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300;
    final textColor = isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700;

    final double contentHeight = 500;

    return ResponsiveDialog(
      title: const Text('Transfer to Another Device'),
      content: SizedBox(
        height: contentHeight,
        width: double.maxFinite,
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _transferData.startsWith('Error')
                          ? _transferData
                          : 'Scan this QR code on your new device or copy the transfer data below',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: _transferData.startsWith('Error')
                            ? Theme.of(context).colorScheme.error
                            : textColor,
                      ),
                    ),
                    const SizedBox(height: 24),
                    if (!_transferData.startsWith('Error'))
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: borderColor),
                        ),
                        child: QrImageView(
                          data: _transferData,
                          version: QrVersions.auto,
                          size: 200,
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                        ),
                      ),
                    if (!_transferData.startsWith('Error'))
                      ...[
                        const SizedBox(height: 24),
                        Text(
                          'Transfer Data',
                          style: TextStyle(fontWeight: FontWeight.bold, color: Theme.of(context).textTheme.bodyLarge?.color),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: borderColor),
                          ),
                          child: SelectableText(
                            _transferData,
                            style: TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'On your new device, go to Recovery tab and paste this data',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 12, color: textColor),
                        ),
                      ],
                  ],
                ),
              ),
      ),
      actions: [
        if (!_isLoading && !_transferData.startsWith('Error'))
          TextButton.icon(
            onPressed: _copyDataToClipboard,
            icon: Icon(_dataCopied ? Icons.check : Icons.copy),
            label: Text(_dataCopied ? 'Copied!' : 'Copy Data'),
          ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
      maxWidth: 600,
    );
  }
}

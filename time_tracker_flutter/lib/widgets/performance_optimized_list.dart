import 'package:flutter/material.dart';

class PerformanceOptimizedList<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;

  const PerformanceOptimizedList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    // Use ListView.builder for better performance with large lists
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      // Add cache extent for better scrolling performance
      cacheExtent: 600.0,
      itemCount: items.length,
      itemBuilder: (context, index) {
        // Wrap each item in RepaintBoundary to isolate repaints
        return RepaintBoundary(
          child: itemBuilder(context, items[index], index),
        );
      },
    );
  }
}

class OptimizedCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsets? margin;

  const OptimizedCard({
    super.key,
    required this.child,
    this.onTap,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Card(
        margin: margin,
        child: onTap != null
            ? InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(8),
                child: child,
              )
            : child,
      ),
    );
  }
}

// Mixin for widgets that need automatic disposal
mixin AutoDisposeMixin<T extends StatefulWidget> on State<T> {
  final List<VoidCallback> _disposables = [];

  void addDisposable(VoidCallback disposable) {
    _disposables.add(disposable);
  }

  @override
  void dispose() {
    for (final disposable in _disposables) {
      try {
        disposable();
      } catch (e) {
        debugPrint('Error during disposal: $e');
      }
    }
    _disposables.clear();
    super.dispose();
  }
} 
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';
// import 'package:time_tracker_flutter/utils/responsive_utils.dart';

class ProjectDialog extends StatefulWidget {
  final Project? initialProject;
  final Function(Project) onSave;

  const ProjectDialog({
    super.key,
    this.initialProject,
    required this.onSave,
  });

  @override
  State<ProjectDialog> createState() => _ProjectDialogState();
}

class _ProjectDialogState extends State<ProjectDialog> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _minimumWeeklyHoursController = TextEditingController();
  final TextEditingController _maxWeeksController = TextEditingController(text: '3');
  final TextEditingController _roundingMinutesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.initialProject != null) {
      _nameController.text = widget.initialProject!.name;
      _minimumWeeklyHoursController.text =
          widget.initialProject!.minimumWeeklyHours?.toString() ?? '';
      _maxWeeksController.text = widget.initialProject!.maxWeeksInOverview.toString();
      _roundingMinutesController.text = widget.initialProject!.roundingMinutes?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _minimumWeeklyHoursController.dispose();
    _maxWeeksController.dispose();
    _roundingMinutesController.dispose();
    super.dispose();
  }

  void _saveProject() {
    if (_nameController.text.isEmpty) {
      return;
    }

    int? minimumWeeklyHours;
    if (_minimumWeeklyHoursController.text.isNotEmpty) {
      minimumWeeklyHours = int.tryParse(_minimumWeeklyHoursController.text);
    }

    int maxWeeks = 3;
    if (_maxWeeksController.text.isNotEmpty) {
      final parsedWeeks = int.tryParse(_maxWeeksController.text);
      if (parsedWeeks != null && parsedWeeks > 0) {
        maxWeeks = parsedWeeks;
      }
    }

    int? roundingMinutes;
    if (_roundingMinutesController.text.isNotEmpty) {
      roundingMinutes = int.tryParse(_roundingMinutesController.text);
    }

    final project = Project(
      id: widget.initialProject?.id,
      name: _nameController.text,
      order: widget.initialProject?.order ?? 0,
      minimumWeeklyHours: minimumWeeklyHours,
      maxWeeksInOverview: maxWeeks,
      roundingMinutes: roundingMinutes,
    );

    widget.onSave(project);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.initialProject != null;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return ResponsiveDialog(
      title: Text(
        isEditing ? 'Edit Project' : 'Add Project',
        style: TextStyle(
          fontSize: isSmallScreen ? 20 : 24,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Project Name',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 16),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _minimumWeeklyHoursController,
              decoration: const InputDecoration(
                labelText: 'Minimum Weekly Hours',
                hintText: 'e.g. 40',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 16),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _maxWeeksController,
              decoration: const InputDecoration(
                labelText: 'Weeks to Show in Overview',
                hintText: 'e.g. 3',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 16),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _roundingMinutesController,
              decoration: const InputDecoration(
                labelText: 'Rounding Minutes',
                hintText: 'e.g. 15',
            ),
            style: const TextStyle(fontSize: 16),
            keyboardType: TextInputType.number,
          ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'Cancel',
            style: TextStyle(fontSize: 14),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _saveProject,
          child: Text(
            isEditing ? 'Save' : 'Add',
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }
}

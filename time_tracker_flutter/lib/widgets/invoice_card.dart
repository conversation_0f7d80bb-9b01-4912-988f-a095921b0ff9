import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';

/// A modern card widget that displays invoice summary information
class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final Client? client;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStatusChange;
  final VoidCallback? onDetail;
  final VoidCallback? onPreviewPdf;

  const InvoiceCard({
    super.key,
    required this.invoice,
    this.client,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
    this.onDetail,
    this.onPreviewPdf,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final cardPadding = ResponsiveUtils.getCardPadding(context);
    final cardMargin = isSmallScreen ? 8.0 : 12.0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: cardMargin, vertical: 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: theme.colorScheme.surface,
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(cardPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with invoice number and status
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Invoice',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              invoice.invoiceNumber,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildModernStatusChip(context),
                    ],
                  ),

                  SizedBox(height: isSmallScreen ? 12 : 16),

                  // Client section with avatar
                  if (client != null) ...[
                    Row(
                      children: [
                        Container(
                          width: isSmallScreen ? 36 : 40,
                          height: isSmallScreen ? 36 : 40,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Center(
                            child: Text(
                              client!.name.isNotEmpty ? client!.name[0].toUpperCase() : 'C',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                                fontSize: isSmallScreen ? 14 : 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                client!.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  fontSize: isSmallScreen ? 14 : 16,
                                ),
                              ),
                              if (client!.email != null) ...[
                                const SizedBox(height: 1),
                                Text(
                                  client!.email!,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                    fontSize: isSmallScreen ? 12 : 13, // Increased for better readability
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isSmallScreen ? 12 : 16),
                  ],

                  // Invoice details in compact layout
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 14),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.outline.withValues(alpha: 0.1),
                      ),
                    ),
                    child: Row(
                            children: [
                              Expanded(
                                child: _buildDetailItem(
                                  context,
                                  'Issue Date',
                                  LocaleDateUtils.formatDate(invoice.issueDate),
                                  Icons.calendar_today,
                                ),
                              ),
                              if (invoice.dueDate != null) ...[
                                Container(
                                  width: 1,
                                  height: 32,
                                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                                ),
                                Expanded(
                                  child: _buildDetailItem(
                                    context,
                                    'Due Date',
                                    LocaleDateUtils.formatDate(invoice.dueDate!),
                                    Icons.schedule,
                                    isOverdue: invoice.isOverdue,
                                    alignment: CrossAxisAlignment.end,
                                  ),
                                ),
                              ],
                            ],
                          ),
                  ),

                  SizedBox(height: isSmallScreen ? 10 : 12),

                  // Total amount with compact styling
                  Container(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 14),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.account_balance_wallet,
                          color: theme.colorScheme.onPrimaryContainer,
                          size: isSmallScreen ? 18 : 20,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Total Amount',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.w500,
                                  fontSize: isSmallScreen ? 12 : 13, // Increased for better readability
                                ),
                              ),
                              Text(
                                CurrencyService.formatCurrency(
                                  invoice.total,
                                  invoice.currency,
                                  InvoiceLocalizationService.getLocaleFromLanguage(invoice.locale),
                                ),
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onPrimaryContainer,
                                  fontSize: isSmallScreen ? 18 : 20,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Overdue warning with compact design
                  if (invoice.isOverdue) ...[
                    SizedBox(height: isSmallScreen ? 8 : 10),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 10 : 12,
                        vertical: isSmallScreen ? 6 : 8,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: theme.colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            size: isSmallScreen ? 16 : 18,
                            color: theme.colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${invoice.daysPastDue} days overdue',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onErrorContainer,
                              fontWeight: FontWeight.w600,
                              fontSize: isSmallScreen ? 12 : 13, // Increased for better readability
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Responsive action buttons
                  if (onEdit != null || onDelete != null || onStatusChange != null || onDetail != null || onPreviewPdf != null) ...[
                    SizedBox(height: isSmallScreen ? 12 : 14),
                    _buildActionButtons(context, isSmallScreen),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isSmallScreen) {
    final actions = <Widget>[];

    if (onDetail != null) {
      actions.add(Expanded(
        child: _buildModernActionButton(
          context,
          'Details',
          Icons.info_outline_rounded,
          onDetail!,
          isPrimary: true,
        ),
      ));
    }

    if (onPreviewPdf != null) {
      actions.add(Expanded(
        child: _buildModernActionButton(
          context,
          'Preview',
          Icons.visibility_rounded,
          onPreviewPdf!,
        ),
      ));
    }

    if (onStatusChange != null) {
      actions.add(Expanded(
        child: _buildModernActionButton(
          context,
          'Status',
          Icons.edit_note_rounded,
          onStatusChange!,
        ),
      ));
    }

    if (onEdit != null) {
      actions.add(Expanded(
        child: _buildModernActionButton(
          context,
          'Edit',
          Icons.edit_rounded,
          onEdit!,
        ),
      ));
    }

    if (onDelete != null) {
      actions.add(Expanded(
        child: _buildModernActionButton(
          context,
          'Delete',
          Icons.delete_rounded,
          onDelete!,
          isDestructive: true,
        ),
      ));
    }

    // For small screens with many actions, use a more compact layout
    if (isSmallScreen && actions.length > 3) {
      return Column(
        children: [
          Row(
            children: [
              ...actions.take(2).expand((action) => [action, const SizedBox(width: 4)]).take(3),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              ...actions.skip(2).expand((action) => [action, const SizedBox(width: 4)]).take(actions.length > 2 ? (actions.length - 2) * 2 - 1 : 0),
            ],
          ),
        ],
      );
    }

    return Row(
      children: actions.expand((action) => [action, const SizedBox(width: 4)]).take(actions.length * 2 - 1).toList(),
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isOverdue = false,
    CrossAxisAlignment alignment = CrossAxisAlignment.start,
  }) {
    final theme = Theme.of(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Column(
      crossAxisAlignment: alignment,
      children: [
        Row(
          mainAxisAlignment: alignment == CrossAxisAlignment.start
              ? MainAxisAlignment.start
              : MainAxisAlignment.end,
          children: [
            if (alignment == CrossAxisAlignment.end) ...[
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                  fontSize: isSmallScreen ? 11 : 12, // Increased for better readability
                ),
              ),
              const SizedBox(width: 4),
            ],
            Icon(
              icon,
              size: isSmallScreen ? 12 : 14,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            if (alignment == CrossAxisAlignment.start) ...[
              const SizedBox(width: 4),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w500,
                  fontSize: isSmallScreen ? 11 : 12, // Increased for better readability
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: isSmallScreen ? 12 : 13,
            color: isOverdue
                ? theme.colorScheme.error
                : theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed, {
    bool isPrimary = false,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isSmallishDevice = ResponsiveUtils.isSmallishScreen(context);

    return Container(
      height: isSmallScreen ? 44 : 48, // Increased for better touch targets
      decoration: BoxDecoration(
        color: isPrimary
            ? theme.colorScheme.primary
            : isDestructive
                ? theme.colorScheme.error
                : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isPrimary
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : isDestructive
                  ? theme.colorScheme.error.withValues(alpha: 0.3)
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Tooltip(
            message: label,
            child: Center(
              child: isSmallishDevice
                  ? Icon(
                      icon,
                      size: isSmallScreen ? 18 : 20, // Increased for better visibility
                      color: isPrimary
                          ? theme.colorScheme.onPrimary
                          : isDestructive
                              ? theme.colorScheme.onError
                              : theme.colorScheme.onSurface,
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          icon,
                          size: 16, // Increased from 14
                          color: isPrimary
                              ? theme.colorScheme.onPrimary
                              : isDestructive
                                  ? theme.colorScheme.onError
                                  : theme.colorScheme.onSurface,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          label,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isPrimary
                                ? theme.colorScheme.onPrimary
                                : isDestructive
                                    ? theme.colorScheme.onError
                                    : theme.colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                            fontSize: 12, // Increased for better readability
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernStatusChip(BuildContext context) {
    final theme = Theme.of(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    Color backgroundColor;
    Color textColor;
    IconData icon;
    Color borderColor;

    switch (invoice.status) {
      case InvoiceStatus.draft:
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurface;
        icon = Icons.edit_note_rounded;
        borderColor = theme.colorScheme.outline.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.sent:
        backgroundColor = theme.colorScheme.primaryContainer;
        textColor = theme.colorScheme.onPrimaryContainer;
        icon = Icons.send_rounded;
        borderColor = theme.colorScheme.primary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.paid:
        backgroundColor = theme.colorScheme.tertiaryContainer;
        textColor = theme.colorScheme.onTertiaryContainer;
        icon = Icons.check_circle_rounded;
        borderColor = theme.colorScheme.tertiary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.overdue:
        backgroundColor = theme.colorScheme.errorContainer;
        textColor = theme.colorScheme.onErrorContainer;
        icon = Icons.warning_rounded;
        borderColor = theme.colorScheme.error.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.cancelled:
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurface.withValues(alpha: 0.6);
        icon = Icons.cancel_rounded;
        borderColor = theme.colorScheme.outline.withValues(alpha: 0.3);
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 8 : 10,
        vertical: isSmallScreen ? 4 : 5,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: borderColor,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: isSmallScreen ? 12 : 13,
            color: textColor,
          ),
          const SizedBox(width: 4),
          Text(
            _getStatusDisplayText(invoice.status),
            style: theme.textTheme.bodySmall?.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: isSmallScreen ? 11 : 12, // Increased for better readability
              letterSpacing: 0.2,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDisplayText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
    }
  }
}
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/utils/ui_utils.dart';

class ProjectCardProgressBar extends StatelessWidget {
  final List<TimeEntry> entries;
  final int targetHours;
  final Color projectColor;
  final double? indicatorMinHeight;

  const ProjectCardProgressBar({
    super.key,
    required this.entries,
    required this.targetHours,
    required this.projectColor,
    this.indicatorMinHeight,
  });

  @override
  Widget build(BuildContext context) {
    if (entries.isEmpty) {
      return const SizedBox.shrink();
    }

    final dates = entries.map((e) => DateTime.parse(e.date).millisecondsSinceEpoch).toList();
    final earliestDate = DateTime.fromMillisecondsSinceEpoch(dates.reduce((a, b) => a < b ? a : b));
    final latestDate = DateTime.fromMillisecondsSinceEpoch(dates.reduce((a, b) => a > b ? a : b));

    final startWeek = getWeekStartDate(earliestDate);
    final endWeek = getWeekStartDate(latestDate);

    final diffDays = endWeek.difference(startWeek).inDays;
    final weeksSinceStart = (diffDays / 7).round() + 1;
    final adjustedWeeksSinceStart = weeksSinceStart < 1 ? 1 : weeksSinceStart;

    final targetTotalHours = adjustedWeeksSinceStart * targetHours.toDouble();
    final totalTime = calculateTotalTime(entries);
    final totalMinutes = timeToMinutes(totalTime);
    final actualHours = totalMinutes / 60;
    final percentage = (actualHours / targetTotalHours * 100).round();
    final clampedPercentage = percentage > 100 ? 100 : percentage;
    final progressValue = clampedPercentage / 100;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 4.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${actualHours.toStringAsFixed(1)} / ${targetTotalHours.toStringAsFixed(1)} hours',
              style: TextStyle(
                fontSize: 11.0,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              '$clampedPercentage%',
              style: TextStyle(
                fontSize: 11.0,
                fontWeight: FontWeight.bold,
                color: getProgressColor(clampedPercentage),
              ),
            ),
          ],
        ),
        SizedBox(height: 5.0),
        ClipRRect(
          borderRadius: BorderRadius.circular(6.0),
          child: LinearProgressIndicator(
            value: progressValue,
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? projectColor.withOpacity(0.2)
                : projectColor.withOpacity(0.15),
            valueColor: AlwaysStoppedAnimation<Color>(getProgressColor(clampedPercentage)),
            minHeight: indicatorMinHeight ?? 8.0,
          ),
        ),
      ],
    );
  }
}

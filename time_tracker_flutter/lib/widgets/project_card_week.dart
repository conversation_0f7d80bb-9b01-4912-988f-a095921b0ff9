import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/widgets/week_entry_group.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

class ProjectCardWeek extends StatelessWidget {
  final String weekKey;
  final String totalTime;
  final List<TimeEntry> entries;
  final bool isCurrentWeek;
  final int? minimumWeeklyHours;
  final Color projectColor;
  final Map<String, bool> weekExpansionState;
  final ValueChanged<String> onToggle;
  final ValueChanged<TimeEntry> onEdit;
  final ValueChanged<TimeEntry> onDelete;
  final Future<void> Function(List<TimeEntry>) onDeleteWeek;
  final DatabaseService databaseService;
  final VoidCallback refreshTimeEntries;

  const ProjectCardWeek({
    super.key,
    required this.weekKey,
    required this.totalTime,
    required this.entries,
    required this.isCurrentWeek,
    required this.minimumWeeklyHours,
    required this.projectColor,
    required this.weekExpansionState,
    required this.onToggle,
    required this.onEdit,
    required this.onDelete,
    required this.onDeleteWeek,
    required this.databaseService,
    required this.refreshTimeEntries,
  });

  @override
  Widget build(BuildContext context) {
    final bool isExpanded = weekExpansionState[weekKey] ?? isCurrentWeek;
    return Container(
      margin: EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4.0,
            offset: Offset(0, 1.0),
          ),
        ],
      ),
      child: WeekEntryGroup(
        weekKey: weekKey,
        totalTime: totalTime,
        entries: entries,
        isExpanded: isExpanded,
        onToggle: () => onToggle(weekKey),
        onEdit: onEdit,
        onDelete: onDelete,
        onDeleteWeek: onDeleteWeek,
        isCurrentWeek: isCurrentWeek,
        minimumWeeklyHours: minimumWeeklyHours,
        projectColor: projectColor,
      ),
    );
  }
}

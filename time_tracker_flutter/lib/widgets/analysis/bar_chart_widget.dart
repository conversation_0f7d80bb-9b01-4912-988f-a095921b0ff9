import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/utils/date_format_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'dart:math' as math;

// Alias for backward compatibility
class BarChartWidget extends AnalysisBarChart {
  const BarChartWidget({
    super.key,
    required super.data,
    required super.title,
    super.height,
    super.width,
    super.showPercentages,
    super.maxBars,
    super.showAverage,
    super.period,
    super.dateRange,
  });
}

class _ChartConfig {
  final bool isSmallScreen;
  final bool isMediumScreen;
  final bool shouldRotateLabels;
  final int optimalBarCount;
  final double maxY;
  final double averageHours;

  _ChartConfig({
    required this.isSmallScreen,
    required this.isMediumScreen,
    required this.shouldRotateLabels,
    required this.optimalBarCount,
    required this.maxY,
    required this.averageHours,
  });

  factory _ChartConfig.fromContext({
    required BuildContext context,
    required List<TimeDistributionData> data,
    required int? maxBars,
    required bool showAverage,
  }) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isMediumScreen = ResponsiveUtils.isMediumScreen(context);

    final shouldRotateLabels = isSmallScreen || (isMediumScreen && data.length > 5);

    int optimalBarCount = maxBars ?? data.length;
    if (maxBars == null) {
      if (isSmallScreen) {
        optimalBarCount = math.min(6, data.length);
      } else if (isMediumScreen) {
        optimalBarCount = math.min(10, data.length);
      } else {
        optimalBarCount = math.min(15, data.length);
      }
    }

    final averageHours = data.fold<double>(0, (sum, item) => sum + item.hours) / data.length;
    final maxHours = data.map((e) => e.hours).reduce(math.max);
    final maxY = showAverage && averageHours > maxHours ? averageHours * 1.2 : maxHours * 1.1;

    return _ChartConfig(
      isSmallScreen: isSmallScreen,
      isMediumScreen: isMediumScreen,
      shouldRotateLabels: shouldRotateLabels,
      optimalBarCount: optimalBarCount,
      maxY: maxY,
      averageHours: averageHours,
    );
  }
}

class AnalysisBarChart extends StatefulWidget {
  final List<TimeDistributionData> data;
  final String title;
  final double? height;
  final double? width;
  final bool showPercentages;
  final int? maxBars;
  final bool showAverage;
  final AnalysisPeriod period;
  final DateTimeRange? dateRange;

  const AnalysisBarChart({
    super.key,
    required this.data,
    required this.title,
    this.height,
    this.width,
    this.showPercentages = false,
    this.maxBars,
    this.showAverage = false,
    this.period = AnalysisPeriod.month,
    this.dateRange,
  });

  @override
  State<AnalysisBarChart> createState() => _AnalysisBarChartState();
}

class _AnalysisBarChartState extends State<AnalysisBarChart> {
  int? _touchedIndex;
  final ScrollController _legendScrollController = ScrollController();

  final int _itemWidth = 200;
  final int _itemWidthSmallScreen = 166;

  // Get localized labels for chart elements
  Map<String, String> _getLocalizedLabels(BuildContext context) {
    // For now, keeping English labels as they are commonly understood
    // In a full internationalization implementation, these would come from localization files
    return {
      'average': 'Avg',
      'hours': 'Hours',
      'noData': 'No data available',
      'showingTop': 'Showing top',
      'of': 'of',
      'items': 'items',
    };
  }

  @override
  void dispose() {
    _legendScrollController.dispose();
    super.dispose();
  }

  void _scrollToSelectedLegend(bool isSmallScreen) {
    if (_touchedIndex == null || !_legendScrollController.hasClients) return;

    // Calculate the total width of all items before the touched index
    final padding = 6.0; // Increased padding for better spacing
    final totalWidth = _touchedIndex! * (isSmallScreen ? _itemWidthSmallScreen : _itemWidth + padding * 2);

    // Calculate the center position of the viewport
    final viewportCenter = _legendScrollController.position.viewportDimension / 2;

    // Calculate the target scroll offset to center the selected item
    final targetOffset = totalWidth - viewportCenter + (isSmallScreen ? _itemWidthSmallScreen / 2 : _itemWidth / 2);

    // Ensure the scroll position stays within bounds
    final maxScroll = _legendScrollController.position.maxScrollExtent;
    final safeOffset = targetOffset.clamp(0.0, maxScroll);

    _legendScrollController.animateTo(
      safeOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutCubic,
    );
  }

  // Enhanced label builder for the bar x-axis - similar to line chart approach
  Widget _buildAxisLabel(
    double value,
    TitleMeta meta,
    List<TimeDistributionData> data,
    _ChartConfig config,
  ) {
    final index = value.toInt();
    if (index < 0 || index >= data.length) return const Text('');

    final item = data[index];
    final isTouched = index == _touchedIndex;
    final interval = data.length <= 8 ? 1 : (config.isSmallScreen ? 2 : 3);

    if (index % interval != 0 && index != 0 && index != data.length - 1) {
      return const Text('');
    }

    // Use locale-aware date formatting with improved fallback
    String label;
    if (item.date != null) {
      final locale = LocaleDateUtils.getCurrentLocale(context);
      // Use the same formatting approach as the line chart for consistency
      if (widget.period == AnalysisPeriod.allTime || (widget.dateRange != null && DateFormatUtils.shouldShowMonthlyData(widget.dateRange!))) {
        label = LocaleDateUtils.formatMonthYear(item.date!, locale);
      } else {
        // For shorter periods, use more detailed formatting
        label = DateFormatUtils.formatChartDateWithContext(
          item.date!,
          widget.period,
          context,
          isCompactView: config.isSmallScreen,
          fullDateRange: widget.dateRange,
          dataPointIndex: index,
          totalDataPoints: data.length,
        );
      }
    } else {
      // Fallback to the item's label if no date is available
      label = item.label;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: config.shouldRotateLabels
          ? Transform.rotate(
              angle: math.pi / 4,
              alignment: Alignment.topRight,
              child: Text(
                label,
                style: TextStyle(
                  fontSize: config.isSmallScreen ? 9 : 10,
                  fontWeight: isTouched ? FontWeight.bold : FontWeight.normal,
                  color: isTouched
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            )
          : Text(
              label,
              style: TextStyle(
                fontSize: config.isSmallScreen ? 9 : 10,
                fontWeight: isTouched ? FontWeight.bold : FontWeight.normal,
                color: isTouched
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
    );
  }

  // Get detailed date information for tooltips and legends
  Future<String> _getDetailedTimeDescription(TimeDistributionData item) async {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    
    // Handle aggregated data with range information
    if (item.rangeStart != null && item.rangeEnd != null) {
      return LocaleDateUtils.formatDateRangeAsync(item.rangeStart!, item.rangeEnd!, locale);
    }
    
    // Handle single date data
    if (item.date != null) {
      return LocaleDateUtils.formatDateWithCustom(item.date!, locale);
    }
    
    // Try to parse month labels in a locale-aware way for legacy data
    if (widget.dateRange != null && item.label.length <= 4) {
      // Check if the label matches any month format in the current locale
      for (int month = 1; month <= 12; month++) {
        final testDate = DateTime(widget.dateRange!.end.year, month, 1);
        final monthShort = LocaleDateUtils.formatMonth(testDate, locale);
        final monthFull = LocaleDateUtils.getFullMonthFormat(locale).format(testDate);

        if (item.label == monthShort || item.label == monthFull) {
          final startDate = DateTime(widget.dateRange!.end.year, month, 1);
          final endDate = DateTime(widget.dateRange!.end.year, month + 1, 0);
          return LocaleDateUtils.formatWeekDisplayAsync(startDate, endDate, locale);
        }
      }
    }
    
    // Return empty string if no date information can be extracted
    return '';
  }

  Widget _buildEmptyState() {
    final labels = _getLocalizedLabels(context);
    return SizedBox(
      height: widget.height ?? 200,
      width: widget.width,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.bar_chart_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              labels['noData']!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(TimeDistributionData item, int index, bool isTouched, bool isSmallScreen) {
    return Container(
      width: isSmallScreen ? _itemWidthSmallScreen.toDouble() : _itemWidth.toDouble(),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      margin: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: isTouched
            ? (item.color ?? Theme.of(context).colorScheme.primary).withOpacity(0.15)
            : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isTouched
              ? (item.color ?? Theme.of(context).colorScheme.primary)
              : Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: item.color ?? Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    item.label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: isTouched ? FontWeight.bold : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '${item.hours.toStringAsFixed(1)}h',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                if (widget.showPercentages)
                  Text(
                    ' (${item.percentage.toStringAsFixed(1)}%)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
              ],
            ),
            FutureBuilder<String>(
              future: _getDetailedTimeDescription(item),
              builder: (context, snapshot) {
                final timeInfo = snapshot.data ?? '';
                final additionalInfo = item.dataPointCount != null && item.dataPointCount! > 1
                    ? '(${item.dataPointCount} points)'
                    : '';

                if (timeInfo.isNotEmpty || additionalInfo.isNotEmpty) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      [timeInfo, additionalInfo].where((e) => e.isNotEmpty).join(' '),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  BarChartData _createBarChartData(List<TimeDistributionData> displayData, _ChartConfig config) {
    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: config.maxY,
      barTouchData: BarTouchData(
        touchTooltipData: BarTouchTooltipData(
          tooltipBgColor: Theme.of(context).colorScheme.surface.withOpacity(0.9),
          tooltipRoundedRadius: 8,
          tooltipPadding: const EdgeInsets.all(8),
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final item = displayData[groupIndex];
            final hoursInfo = widget.showPercentages
                ? '${item.hours.toStringAsFixed(2)} hours (${item.percentage.toStringAsFixed(1)}%)'
                : '${item.hours.toStringAsFixed(2)} hours';
            final titleText = item.label; // Use label directly for tooltip since we can't await here

            return BarTooltipItem(
              titleText,
              TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              children: [
                TextSpan(
                  text: '\n$hoursInfo',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 11,
                  ),
                ),
              ],
            );
          },
        ),
        touchCallback: (event, response) {
          setState(() {
            _touchedIndex = !event.isInterestedForInteractions || response?.spot == null
                ? null
                : response!.spot!.touchedBarGroupIndex;
          });
        },
      ),
      titlesData: _createTitlesData(displayData, config),
      borderData: FlBorderData(show: false),
      barGroups: _createBarGroups(displayData, config),
      gridData: _createGridData(config),
      extraLinesData: widget.showAverage ? _createAverageLineData(config) : null,
    );
  }

  FlTitlesData _createTitlesData(List<TimeDistributionData> displayData, _ChartConfig config) {
    final labels = _getLocalizedLabels(context);
    return FlTitlesData(
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      bottomTitles: AxisTitles(
        axisNameWidget: widget.dateRange != null ? Text(
          DateFormatUtils.formatDateRangeWithContext(
            widget.dateRange!.start,
            widget.dateRange!.end,
            widget.period,
            context,
          ),
          style: TextStyle(
            fontSize: 10,
            fontStyle: FontStyle.italic,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ) : null,
        sideTitles: SideTitles(
          showTitles: true,
          getTitlesWidget: (value, meta) => _buildAxisLabel(value, meta, displayData, config),
          reservedSize: config.shouldRotateLabels ? 40 : 30,
        ),
      ),
      leftTitles: AxisTitles(
        axisNameWidget: Text(
          labels['hours']!,
          style: TextStyle(
            fontSize: 10,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 42,
          interval: config.maxY / 5,
          getTitlesWidget: (value, meta) => Text(
            value.toStringAsFixed(1),
            style: TextStyle(
              fontSize: 10,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ),
      ),
    );
  }

  List<BarChartGroupData> _createBarGroups(List<TimeDistributionData> displayData, _ChartConfig config) {
    return List.generate(displayData.length, (index) {
      final item = displayData[index];
      final isTouched = index == _touchedIndex;
      final barColor = item.color ?? Theme.of(context).colorScheme.primary;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item.hours,
            color: isTouched ? barColor : barColor.withOpacity(0.7),
            width: isTouched ? 22 : 18,
            borderRadius: BorderRadius.circular(4),
            backDrawRodData: BackgroundBarChartRodData(
              show: true,
              toY: config.maxY,
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1),
            ),
          ),
        ],
        showingTooltipIndicators: isTouched ? [0] : [],
      );
    });
  }

  FlGridData _createGridData(_ChartConfig config) {
    return FlGridData(
      show: true,
      drawVerticalLine: false,
      horizontalInterval: config.maxY / 5,
      getDrawingHorizontalLine: (value) {
        if (widget.showAverage && (value - config.averageHours).abs() < 0.1) {
          return FlLine(
            color: Theme.of(context).colorScheme.tertiary,
            strokeWidth: 1.5,
            dashArray: [5, 5],
          );
        }
        return FlLine(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          strokeWidth: 0.8,
        );
      },
    );
  }

  ExtraLinesData _createAverageLineData(_ChartConfig config) {
    final labels = _getLocalizedLabels(context);
    return ExtraLinesData(
      horizontalLines: [
        HorizontalLine(
          y: config.averageHours,
          color: Theme.of(context).colorScheme.tertiary,
          strokeWidth: 1.5,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            alignment: Alignment.topRight,
            padding: const EdgeInsets.only(right: 8, bottom: 4),
            style: TextStyle(
              color: Theme.of(context).colorScheme.tertiary,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
            labelResolver: (line) => '${labels['average']!}: ${config.averageHours.toStringAsFixed(1)}',
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return _buildEmptyState();
    }

    final config = _ChartConfig.fromContext(
      context: context,
      data: widget.data,
      maxBars: widget.maxBars,
      showAverage: widget.showAverage,
    );

    final sortedData = List<TimeDistributionData>.from(widget.data)
      ..sort((a, b) => b.hours.compareTo(a.hours));

    final displayData = config.optimalBarCount < sortedData.length
        ? sortedData.sublist(0, config.optimalBarCount)
        : sortedData;

    if (widget.showPercentages) {
      final totalHours = displayData.fold<double>(0, (sum, item) => sum + item.hours);
      for (var i = 0; i < displayData.length; i++) {
        final item = displayData[i];
        displayData[i] = TimeDistributionData(
          label: item.label,
          hours: item.hours,
          percentage: totalHours > 0 ? (item.hours / totalHours) * 100 : 0,
          color: item.color,
          date: item.date,
          aggregationLevel: item.aggregationLevel,
          rangeStart: item.rangeStart,
          rangeEnd: item.rangeEnd,
          dataPointCount: item.dataPointCount,
        );
      }
    }

    // Call _scrollToSelectedLegend when _touchedIndex changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedLegend(config.isSmallScreen);
    });

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 6.0),
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ),
        SizedBox(
          height: widget.height ?? 200,
          width: widget.width,
          child: BarChart(
            _createBarChartData(displayData, config),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 85, // Slightly increased height for better visibility
          child: ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.white.withOpacity(0.0),
                  Colors.white,
                  Colors.white,
                  Colors.white.withOpacity(0.0),
                ],
                stops: const [0.0, 0.05, 0.95, 1.0],
              ).createShader(bounds);
            },
            blendMode: BlendMode.dstIn,
            child: SingleChildScrollView(
              controller: _legendScrollController,
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0), // Increased padding
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: displayData.asMap().entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0), // Increased padding
                      child: _buildLegendItem(
                        entry.value,
                        entry.key,
                        entry.key == _touchedIndex,
                        config.isSmallScreen,
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
        if (displayData.length < sortedData.length) ...[
          const SizedBox(height: 6),
          Text(
            '${_getLocalizedLabels(context)['showingTop']!} ${displayData.length} ${_getLocalizedLabels(context)['of']!} ${sortedData.length} ${_getLocalizedLabels(context)['items']!}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontStyle: FontStyle.italic,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }
}

extension StringX on String {
  String ifEmpty(String fallback) => isEmpty ? fallback : this;
}
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';

class AnalysisPieChart extends StatefulWidget {
  final List<TimeDistributionData> data;
  final String title;
  final double? height;
  final double? width;

  const Analysis<PERSON>ie<PERSON>hart({
    super.key,
    required this.data,
    required this.title,
    this.height,
    this.width,
  });

  @override
  State<AnalysisPieChart> createState() => _AnalysisPieChartState();
}

class _AnalysisPieChartState extends State<AnalysisPieChart> {
  int? _touchedIndex;

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return SizedBox(
        height: widget.height ?? 200,
        width: widget.width,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.pie_chart_outline,
                size: 48,
                color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No data available',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    // Sort data by hours (descending) for better visualization
    final sortedData = List<TimeDistributionData>.from(widget.data)
      ..sort((a, b) => b.hours.compareTo(a.hours));

    // Calculate total hours to verify percentages
    final totalHours = sortedData.fold<double>(0, (sum, item) => sum + item.hours);

    // Ensure percentages add up to 100%
    double percentageSum = 0;
    for (var item in sortedData) {
      percentageSum += item.percentage;
    }

    // Apply correction factor if needed
    final correctionFactor = percentageSum > 0 ? 100 / percentageSum : 1;

    return Column(
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ),
        SizedBox(
          height: widget.height ?? 200,
          width: widget.width,
          child: PieChart(
            PieChartData(
              sectionsSpace: 2,
              centerSpaceRadius: 40,
              sections: sortedData.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isTouched = index == _touchedIndex;

                // Apply correction to ensure percentages add up to 100%
                final correctedPercentage = item.percentage * correctionFactor;

                return PieChartSectionData(
                  color: item.color ?? Theme.of(context).colorScheme.primary,
                  value: item.hours,
                  title: '${correctedPercentage.toStringAsFixed(1)}%',
                  radius: isTouched ? 60 : 50,
                  titleStyle: TextStyle(
                    fontSize: isTouched ? 16 : 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  badgeWidget: isTouched ? _Badge(
                    item.label,
                    item.color ?? Theme.of(context).colorScheme.primary,
                    item.hours,
                  ) : null,
                  badgePositionPercentageOffset: 1.2,
                );
              }).toList(),
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      _touchedIndex = null;
                      return;
                    }
                    _touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                  });
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16.0,
          runSpacing: 12.0,
          alignment: WrapAlignment.center,
          children: sortedData.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isTouched = index == _touchedIndex;

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isTouched
                    ? (item.color ?? Theme.of(context).colorScheme.primary).withOpacity(0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: isTouched
                    ? Border.all(
                        color: item.color ?? Theme.of(context).colorScheme.primary,
                        width: 1,
                      )
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: item.color ?? Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${item.label}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: isTouched ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '(${item.hours.toStringAsFixed(1)}h, ${item.percentage.toStringAsFixed(1)}%)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: isTouched ? FontWeight.bold : FontWeight.normal,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

// Badge widget for highlighted pie chart sections
class _Badge extends StatelessWidget {
  final String label;
  final Color color;
  final double value;

  const _Badge(this.label, this.color, this.value);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: color,
          width: 2,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          Text(
            '${value.toStringAsFixed(1)}h',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}
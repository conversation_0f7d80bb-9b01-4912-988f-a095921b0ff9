import 'dart:math' as math;

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/utils/date_format_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';

enum LegendPosition {
  left,
  right,
  top,
  bottom,
}

// Alias for backward compatibility
class LineChartWidget extends AnalysisLineChart {
  const LineChartWidget({
    super.key,
    required super.data,
    required super.title,
    super.height,
    super.width,
    super.showTrendline,
    super.showMinMax,
    super.period,
  });
}

class AnalysisLineChart extends StatefulWidget {
  final List<TimeSeriesData> data;
  final String title;
  final double? height;
  final double? width;
  final bool showTrendline;
  final bool showMinMax;
  final bool showLegend;
  final LegendPosition legendPosition;
  final AnalysisPeriod period;

  const AnalysisLineChart({
    super.key,
    required this.data,
    required this.title,
    this.height,
    this.width,
    this.showTrendline = false,
    this.showMinMax = false,
    this.showLegend = true,
    this.legendPosition = LegendPosition.bottom,
    this.period = AnalysisPeriod.month,
  });

  @override
  State<AnalysisLineChart> createState() => _AnalysisLineChartState();
}

class _LegendItem {
  final Color color;
  final String label;
  final bool isDashed;
  final bool isPoint;

  const _LegendItem({
    required this.color,
    required this.label,
    this.isDashed = false,
    this.isPoint = false,
  });
}

class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    required this.strokeWidth,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double startX = 0;
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(DashedLinePainter oldDelegate) =>
      color != oldDelegate.color ||
      strokeWidth != oldDelegate.strokeWidth ||
      dashWidth != oldDelegate.dashWidth ||
      dashSpace != oldDelegate.dashSpace;
}

class _AnalysisLineChartState extends State<AnalysisLineChart> {
  List<int> _touchedSpots = [];

  // Get localized legend labels
  Map<String, String> _getLocalizedLegendLabels(BuildContext context) {
    // For now, keeping English labels as they are commonly understood
    // In a full internationalization implementation, these would come from localization files
    return {
      'trend': 'Trend',
      'hours': 'Hours',
      'max': 'Max',
      'min': 'Min',
      'average': 'Avg',
    };
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return SizedBox(
        height: widget.height ?? 200,
        width: widget.width,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.show_chart,
                size: 48,
                color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No data available',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    // Sort data by date
    final sortedData = List<TimeSeriesData>.from(widget.data)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Find min and max values for Y axis
    final maxHours = sortedData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);
    final minHours = sortedData.map((e) => e.hours).reduce((a, b) => a < b ? a : b);
    final maxY = maxHours * 1.1;

    // Calculate average hours
    final averageHours = sortedData.fold<double>(0, (sum, item) => sum + item.hours) / sortedData.length;

    // Calculate trend line (simple linear regression)
    double? trendSlope;
    double? trendIntercept;

    if (widget.showTrendline && sortedData.length > 2) {
      final n = sortedData.length;
      final sumX = n * (n - 1) / 2; // Sum of indices 0 to n-1
      final sumY = sortedData.fold<double>(0, (sum, item) => sum + item.hours);
      final sumXY = sortedData.asMap().entries.fold<double>(
        0, (sum, entry) => sum + (entry.key * entry.value.hours)
      );
      final sumXX = sortedData.asMap().entries.fold<double>(
        0, (sum, entry) => sum + (entry.key * entry.key)
      );

      trendSlope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      trendIntercept = (sumY - trendSlope * sumX) / n;
    }

    // Get screen size to determine layout adaptations
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isMediumScreen = ResponsiveUtils.isMediumScreen(context);
    final isLargeScreen = ResponsiveUtils.isLargeScreen(context);

    // Create date range for context
    final dateRange = DateTimeRange(
      start: sortedData.first.date,
      end: sortedData.last.date
    );

    // Determine if we should show monthly data based on date range
    final isMonthlyData = DateFormatUtils.shouldShowMonthlyData(dateRange);

    // Calculate optimal interval for x-axis labels based on available width
    // Estimate chart width (accounting for padding and margins)
    final estimatedChartWidth = math.min(
      ResponsiveUtils.getScreenWidth(context) - 40, // Account for screen padding
      widget.width ?? double.infinity
    );

    // Calculate optimal interval
    final interval = DateFormatUtils.calculateOptimalLabelInterval(
      sortedData.length,
      estimatedChartWidth
    );

    // Determine if we need to rotate labels for better fit
    final shouldRotateLabels = isSmallScreen ||
                              (isMediumScreen && sortedData.length > 10) ||
                              sortedData.length > 20;

    // Find min and max dates for display
    final minDate = sortedData.first.date;
    final maxDate = sortedData.last.date;
    final dateRangeText = DateFormatUtils.formatDateRangeWithContext(minDate, maxDate, widget.period, context);

    // Determine aggregation level for display
    String aggregationLevelText = '';
    if (sortedData.isNotEmpty && sortedData.first.aggregationLevel != null) {
      switch (sortedData.first.aggregationLevel) {
        case DataAggregationLevel.quarter:
          aggregationLevelText = ' (Quarterly data)';
          break;
        case DataAggregationLevel.month:
          aggregationLevelText = ' (Monthly data)';
          break;
        case DataAggregationLevel.week:
          aggregationLevelText = ' (Weekly data)';
          break;
        case DataAggregationLevel.twoDays:
          aggregationLevelText = ' (2-day groups)';
          break;
        default:
          aggregationLevelText = '';
      }
    }

    // Find min and max points for highlighting
    final maxPoint = sortedData.reduce((a, b) => a.hours > b.hours ? a : b);
    final minPoint = sortedData.reduce((a, b) => a.hours < b.hours ? a : b);
    final maxIndex = sortedData.indexOf(maxPoint);
    final minIndex = sortedData.indexOf(minPoint);

    // Build legend items
    final legendLabels = _getLocalizedLegendLabels(context);
    final legendItems = [
      if (widget.showTrendline)
        _LegendItem(
          color: Colors.blue.shade800,
          label: legendLabels['trend']!,
          isDashed: true,
        ),
      _LegendItem(
        color: Theme.of(context).colorScheme.primary,
        label: legendLabels['hours']!,
      ),
      if (widget.showMinMax) ...[
        _LegendItem(
          color: Colors.green.shade600,
          label: legendLabels['max']!,
          isPoint: true,
        ),
        _LegendItem(
          color: Colors.red.shade600,
          label: legendLabels['min']!,
          isPoint: true,
        ),
      ],
    ];

    Widget legendWidget = const SizedBox.shrink();
    if (widget.showLegend && legendItems.isNotEmpty) {
      legendWidget = Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: _buildLegend(context, legendItems),
      );
    }

    return Column(
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ),
        legendWidget,
        SizedBox(
          height: widget.height ?? 200,
          width: widget.width,
          child: LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                drawVerticalLine: true,
                horizontalInterval: maxY / 5,
                verticalInterval: interval.toDouble(),
                getDrawingHorizontalLine: (value) {
                  // Highlight the average line
                  if (value.toStringAsFixed(1) == averageHours.toStringAsFixed(1)) {
                    return FlLine(
                      color: Theme.of(context).colorScheme.tertiary,
                      strokeWidth: 1,
                      dashArray: [5, 5],
                    );
                  }
                  return FlLine(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    strokeWidth: 0.8,
                  );
                },
                getDrawingVerticalLine: (value) {
                  return FlLine(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                    strokeWidth: 0.5,
                  );
                },
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  axisNameWidget: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        dateRangeText,
                        style: TextStyle(
                          fontSize: 10,
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      if (aggregationLevelText.isNotEmpty)
                        Text(
                          aggregationLevelText,
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                          ),
                        ),
                    ],
                  ),
                  axisNameSize: aggregationLevelText.isNotEmpty ? 30 : 16,
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: shouldRotateLabels ? 40 : 30,
                    interval: interval.toDouble(),
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= sortedData.length) {
                        return const Text('');
                      }

                      // Only show dates at intervals or for first/last point
                      final isFirstOrLast = value.toInt() == 0 || value.toInt() == sortedData.length - 1;
                      if (!isFirstOrLast && value.toInt() % interval != 0) {
                        return const Text('');
                      }

                      final index = value.toInt();
                      final date = sortedData[index].date;

                      // Format the date label based on screen size and data context with locale support
                      final formattedDate = DateFormatUtils.formatChartDateWithContext(
                        date,
                        widget.period,
                        context,
                        isMonthlyData: isMonthlyData,
                        isCompactView: isSmallScreen,
                        fullDateRange: dateRange,
                        dataPointIndex: index,
                        totalDataPoints: sortedData.length,
                      );

                      // For rotated labels
                      if (shouldRotateLabels) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Transform.rotate(
                            angle: math.pi / 4, // 45 degrees
                            alignment: Alignment.topRight,
                            child: Text(
                              formattedDate,
                              style: TextStyle(
                                fontSize: 9,
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ),
                        );
                      }

                      // For normal labels
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 10,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  axisNameWidget: Text(
                    'Hours',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  axisNameSize: 20,
                  sideTitles: SideTitles(
                    showTitles: true,
                    interval: maxY / 5,
                    reservedSize: 42,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        value.toStringAsFixed(1),
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      );
                    },
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: true,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    width: 1,
                  ),
                  left: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    width: 1,
                  ),
                ),
              ),
              minX: 0,
              maxX: sortedData.length - 1.0,
              minY: 0,
              maxY: maxY,
              lineBarsData: [
                // Main data line
                LineChartBarData(
                  spots: List.generate(sortedData.length, (index) {
                    return FlSpot(index.toDouble(), sortedData[index].hours);
                  }),
                  isCurved: true,
                  color: Theme.of(context).colorScheme.primary,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      // Highlight min and max points if enabled
                      if (widget.showMinMax && (index == maxIndex || index == minIndex)) {
                        return FlDotCirclePainter(
                          radius: 6,
                          color: index == maxIndex
                              ? Colors.green.shade600
                              : Colors.red.shade600,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      }

                      // Highlight touched spots
                      if (_touchedSpots.contains(index)) {
                        return FlDotCirclePainter(
                          radius: 5,
                          color: Theme.of(context).colorScheme.primary,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      }

                      // Only show dots if we have few data points or for specific points
                      if (sortedData.length < 15 || index % interval == 0 || index == sortedData.length - 1) {
                        return FlDotCirclePainter(
                          radius: 3,
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                          strokeWidth: 1,
                          strokeColor: Colors.white,
                        );
                      }

                      // Hide other dots
                      return FlDotCirclePainter(
                        radius: 0,
                        color: Colors.transparent,
                        strokeWidth: 0,
                        strokeColor: Colors.transparent,
                      );
                    },
                  ),
                  belowBarData: BarAreaData(
                    show: true,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        Theme.of(context).colorScheme.primary.withOpacity(0.05),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),

                // Trend line (if enabled)
                if (widget.showTrendline && trendSlope != null && trendIntercept != null)
                  LineChartBarData(
                    spots: [
                      FlSpot(0, trendIntercept),
                      FlSpot(
                        sortedData.length - 1,
                        trendIntercept + trendSlope * (sortedData.length - 1)
                      ),
                    ],
                    isCurved: false,
                    color: Colors.blue.shade800,
                    barWidth: 2,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                    dashArray: [5, 5],
                  ),
              ],
              lineTouchData: LineTouchData(
                touchTooltipData: LineTouchTooltipData(
                  tooltipBgColor: Theme.of(context).colorScheme.surface.withOpacity(0.9),
                  tooltipRoundedRadius: 8,
                  tooltipPadding: const EdgeInsets.all(12),
                  tooltipMargin: 8,
                  getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                    return touchedBarSpots.map((barSpot) {
                      // Only show tooltip for the main data line
                      if (barSpot.barIndex != 0) return null;

                      final index = barSpot.x.toInt();
                      final date = sortedData[index].date;
                      final hours = barSpot.y;
                      // Use async date formatting for better locale support
                      final locale = LocaleDateUtils.getCurrentLocale(context);
                      String formattedDate;

                      // Try to use custom date formatting
                      if (widget.period == AnalysisPeriod.allTime || isMonthlyData) {
                        formattedDate = LocaleDateUtils.getFullMonthYearFormat(locale).format(date);
                      } else {
                        // For tooltips, we need to use a synchronous approach
                        // Use the best available synchronous formatting
                        final weekday = LocaleDateUtils.getWeekdayFormat(locale).format(date);
                        final dateStr = LocaleDateUtils.formatDate(date, locale);
                        formattedDate = '$weekday, $dateStr';
                      }

                      // Get aggregation info if available
                      final dataPoint = sortedData[index];
                      String aggregationInfo = '';
                      if (dataPoint.isAggregated && dataPoint.dataPointCount != null) {
                        aggregationInfo = '\n${dataPoint.getAggregationDescription()}';
                        if (dataPoint.dataPointCount! > 1) {
                          aggregationInfo += '\n(${dataPoint.dataPointCount} data points)';
                        }
                      }

                      // Calculate percentage difference from average
                      final percentDiff = averageHours > 0
                          ? ((hours - averageHours) / averageHours) * 100
                          : 0;
                      final diffText = percentDiff >= 0
                          ? '+${percentDiff.toStringAsFixed(1)}%'
                          : '${percentDiff.toStringAsFixed(1)}%';

                      return LineTooltipItem(
                        '$formattedDate\n${hours.toStringAsFixed(2)} hours$aggregationInfo',
                        TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                        children: [
                          TextSpan(
                            text: '\n$diffText from average',
                            style: TextStyle(
                              color: percentDiff >= 0 ? Colors.green.shade700 : Colors.red.shade700,
                              fontSize: 12,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      );
                    }).toList();
                  },
                ),
                touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        touchResponse == null ||
                        touchResponse.lineBarSpots == null ||
                        touchResponse.lineBarSpots!.isEmpty) {
                      _touchedSpots = [];
                      return;
                    }

                    _touchedSpots = touchResponse.lineBarSpots!
                        .where((spot) => spot.barIndex == 0) // Only main data line
                        .map((spot) => spot.spotIndex)
                        .toList();
                  });
                },
              ),
              // Add average line
              extraLinesData: ExtraLinesData(
                horizontalLines: [
                  HorizontalLine(
                    y: averageHours,
                    color: Theme.of(context).colorScheme.tertiary,
                    strokeWidth: 1,
                    dashArray: [5, 5],
                    label: HorizontalLineLabel(
                      show: true,
                      alignment: Alignment.topRight,
                      padding: const EdgeInsets.only(right: 8, bottom: 4),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.tertiary,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                      labelResolver: (line) => '${_getLocalizedLegendLabels(context)['average']!}: ${averageHours.toStringAsFixed(1)}',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Show min/max indicators if enabled
        if (widget.showMinMax && sortedData.length > 1) ...[
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildIndicator(
                context,
                '${_getLocalizedLegendLabels(context)['max']!}: ${maxPoint.hours.toStringAsFixed(1)}h (${DateFormatUtils.formatChartDateWithContext(maxPoint.date, widget.period, context, isMonthlyData: isMonthlyData)})',
                Colors.green.shade600,
              ),
              const SizedBox(width: 16),
              _buildIndicator(
                context,
                '${_getLocalizedLegendLabels(context)['min']!}: ${minPoint.hours.toStringAsFixed(1)}h (${DateFormatUtils.formatChartDateWithContext(minPoint.date, widget.period, context, isMonthlyData: isMonthlyData)})',
                Colors.red.shade600,
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLegend(BuildContext context, List<_LegendItem> items) {
    final isHorizontal = widget.legendPosition == LegendPosition.top ||
                        widget.legendPosition == LegendPosition.bottom;

    return Wrap(
      direction: isHorizontal ? Axis.horizontal : Axis.vertical,
      spacing: 12,
      runSpacing: 8,
      alignment: WrapAlignment.center,
      children: items.map((item) => _buildLegendItem(context, item)).toList(),
    );
  }

  Widget _buildLegendItem(BuildContext context, _LegendItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (item.isPoint)
          Container(
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: item.color,
              border: Border.all(
                color: Colors.white,
                width: 1.5,
              ),
            ),
          )
        else
          Container(
            width: 20,
            height: 3,
            decoration: BoxDecoration(
              color: item.color,
              borderRadius: BorderRadius.circular(2),
            ),
            child: item.isDashed
                ? CustomPaint(
                    painter: DashedLinePainter(
                      color: item.color,
                      strokeWidth: 3,
                      dashWidth: 4,
                      dashSpace: 3,
                    ),
                  )
                : null,
          ),
        const SizedBox(width: 6),
        Text(
          item.label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildIndicator(BuildContext context, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
            border: Border.all(
              color: Colors.white,
              width: 1.5,
            ),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
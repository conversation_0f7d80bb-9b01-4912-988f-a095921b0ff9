import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/services/analysis_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/widgets/analysis/stats_card_widget.dart';

class RecordsWidget extends StatelessWidget {
  final AnalysisResult analysisResult;
  final AnalysisService analysisService;

  const RecordsWidget({
    super.key,
    required this.analysisResult,
    required this.analysisService,
  });

  @override
  Widget build(BuildContext context) {
    final recordStats = analysisService.getRecordStatistics(analysisResult);

    if (analysisResult.timeSeriesData.isEmpty) {
      return const Center(child: Text('No record data available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 800),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          Text(
            'Your Achievements',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildAchievementCards(context, recordStats),
          const SizedBox(height: 24),

          Text(
            'Time Streaks',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildStreaksList(context, recordStats),

          const SizedBox(height: 24),
          Text(
            'Average Hours by Weekday',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
            _buildWeekdayAverages(context, recordStats),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildAchievementCards(BuildContext context, Map<String, dynamic> stats) {
    final longestDay = stats['longestDay'] as TimeSeriesData?;
    final shortestDay = stats['shortestDay'] as TimeSeriesData?;
    final mostConsecutiveDays = stats['mostConsecutiveDays'] as int;
    final mostConsecutiveDaysStart = stats['mostConsecutiveDaysStart'] as DateTime?;
    final mostConsecutiveDaysEnd = stats['mostConsecutiveDaysEnd'] as DateTime?;
    final totalDaysLogged = stats['totalDaysLogged'] as int;

    return Column(
      children: [
        StatsCardGrid(
          cards: [
            StatsCard(
              title: 'Total Days Logged',
              value: totalDaysLogged.toString(),
              icon: Icons.calendar_month,
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              iconColor: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            if (longestDay != null)
              StatsCard(
                title: 'Most Productive Day',
                value: '${analysisService.formatHours(longestDay.hours)}',
                icon: Icons.emoji_events,
                backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
                iconColor: Theme.of(context).colorScheme.onTertiaryContainer,
                onTap: () => _showDayDetails(context, longestDay, 'Most Productive Day'),
              ),
          ],
        ),
        const SizedBox(height: 16),
        StatsCardGrid(
          cards: [
            StatsCard(
              title: 'Longest Streak',
              value: '$mostConsecutiveDays days',
              icon: Icons.local_fire_department,
              backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
              iconColor: Theme.of(context).colorScheme.onSecondaryContainer,
              onTap: mostConsecutiveDays > 1 && mostConsecutiveDaysStart != null && mostConsecutiveDaysEnd != null
                  ? () => _showStreakDetails(context, mostConsecutiveDaysStart, mostConsecutiveDaysEnd, mostConsecutiveDays)
                  : null,
            ),
            if (analysisResult.mostProductiveDay != null)
              StatsCard(
                title: 'Busiest Period',
                value: LocaleDateUtils.formatMonth(analysisResult.mostProductiveDay!.date, LocaleDateUtils.getCurrentLocale(context)),
                icon: Icons.trending_up,
                backgroundColor: Theme.of(context).colorScheme.errorContainer,
                iconColor: Theme.of(context).colorScheme.onErrorContainer,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildStreaksList(BuildContext context, Map<String, dynamic> stats) {
    final streakData = stats['streakData'] as List<Map<String, dynamic>>;

    if (streakData.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('No streaks recorded yet'),
          ),
        ),
      );
    }

    return Card(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: streakData.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final streak = streakData[index];
          final start = streak['start'] as DateTime;
          final end = streak['end'] as DateTime;
          final length = streak['length'] as int;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                '${index + 1}',
                style: TextStyle(color: Theme.of(context).colorScheme.onPrimary),
              ),
            ),
            title: Text('$length consecutive days'),
            subtitle: FutureBuilder<String>(
              future: _formatDateRange(start, end, context),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Text('Loading...');
                } else if (snapshot.hasError) {
                  return const Text('Error loading dates');
                } else {
                  return Text(snapshot.data!);
                }
              },
            ),
            trailing: const Icon(Icons.local_fire_department),
          );
        },
      ),
    );
  }

  Widget _buildWeekdayAverages(BuildContext context, Map<String, dynamic> stats) {
    final averagePerWeekday = stats['averagePerWeekday'] as Map<String, double>;

    if (averagePerWeekday.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('No weekday data available'),
          ),
        ),
      );
    }

    // Order weekdays correctly (Monday to Sunday) using locale-aware names
    final orderedWeekdays = List.generate(7, (index) {
      final date = DateTime(2023, 1, 2 + index); // Jan 2, 2023 was a Monday
      return LocaleDateUtils.getFullWeekdayFormat(LocaleDateUtils.getCurrentLocale(context)).format(date);
    });
    final sortedAverages = orderedWeekdays
        .where((day) => averagePerWeekday.containsKey(day))
        .map((day) => MapEntry(day, averagePerWeekday[day]!))
        .toList();

    // Find the max value for scaling the bars
    final maxAverage = sortedAverages.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: sortedAverages.map((entry) {
            final weekday = entry.key;
            final average = entry.value;
            final barWidth = (average / maxAverage) * (MediaQuery.of(context).size.width - 100);

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  SizedBox(
                    width: 60,
                    child: Text(
                      weekday.substring(0, 3),
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  Expanded(
                    child: Stack(
                      children: [
                        Container(
                          height: 24,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceVariant,
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        Container(
                          height: 24,
                          width: barWidth,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 60,
                    child: Text(
                      analysisService.formatHours(average),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDayDetails(BuildContext context, TimeSeriesData day, String title) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              title: const Text('Date'),
              trailing: FutureBuilder<String>(
                future: LocaleDateUtils.formatDateAsync(day.date, LocaleDateUtils.getCurrentLocale(context)),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Text('Loading...');
                  } else if (snapshot.hasError) {
                    return const Text('Error');
                  } else {
                    return Text(snapshot.data!);
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('Hours Logged'),
              trailing: Text(analysisService.formatHours(day.hours)),
            ),
            ListTile(
              title: const Text('Day of Week'),
              trailing: Text(LocaleDateUtils.getFullWeekdayFormat(LocaleDateUtils.getCurrentLocale(context)).format(day.date)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStreakDetails(BuildContext context, DateTime start, DateTime end, int length) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Streak Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              title: const Text('Duration'),
              trailing: Text('$length days'),
            ),
            ListTile(
              title: const Text('Start Date'),
              trailing: FutureBuilder<String>(
                future: LocaleDateUtils.formatDateAsync(start, LocaleDateUtils.getCurrentLocale(context)),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Text('Loading...');
                  } else if (snapshot.hasError) {
                    return const Text('Error');
                  } else {
                    return Text(snapshot.data!);
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('End Date'),
              trailing: FutureBuilder<String>(
                future: LocaleDateUtils.formatDateAsync(end, LocaleDateUtils.getCurrentLocale(context)),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Text('Loading...');
                  } else if (snapshot.hasError) {
                    return const Text('Error');
                  } else {
                    return Text(snapshot.data!);
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<String> _formatDateRange(DateTime start, DateTime end, BuildContext context) async {
    final startFormatted = await LocaleDateUtils.formatDateAsync(start, LocaleDateUtils.getCurrentLocale(context));
    final endFormatted = await LocaleDateUtils.formatDateAsync(end, LocaleDateUtils.getCurrentLocale(context));
    return '$startFormatted - $endFormatted';
  }
}
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/widgets/time_entry_item.dart';
import 'package:time_tracker_flutter/utils/ui_utils.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

class WeekEntryGroup extends StatefulWidget {
  final String weekKey;
  final String totalTime;
  final List<TimeEntry> entries;
  final bool isExpanded;
  final VoidCallback onToggle;
  final Function(TimeEntry) onEdit;
  final Function(TimeEntry) onDelete;
  final Function(List<TimeEntry>)? onDeleteWeek;
  final bool isCurrentWeek;
  final int? minimumWeeklyHours;
  final Color? projectColor;

  const WeekEntryGroup({
    super.key,
    required this.weekKey,
    required this.totalTime,
    required this.entries,
    required this.isExpanded,
    required this.onToggle,
    required this.onEdit,
    required this.onDelete,
    this.onDeleteWeek,
    this.isCurrentWeek = false,
    this.minimumWeeklyHours,
    this.projectColor,
  });

  @override
  State<WeekEntryGroup> createState() => _WeekEntryGroupState();
}

class _WeekEntryGroupState extends State<WeekEntryGroup> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isExpanded;
  }

  @override
  void didUpdateWidget(WeekEntryGroup oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isExpanded != widget.isExpanded) {
      if (_isExpanded != widget.isExpanded) {
        setState(() {
          _isExpanded = widget.isExpanded;
        });
      }
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    widget.onToggle();
  }

  Future<String> _formatWeekDisplayWithCustom(String weekKey, BuildContext context) async {
    final parts = weekKey.split(' - ');
    if (parts.length != 2) return weekKey;

    try {
      final startDate = DateTime.parse(parts[0]);
      final endDate = DateTime.parse(parts[1]);
      final locale = await LocaleDateUtils.getEffectiveLocale(context);
      return LocaleDateUtils.formatWeekDisplayAsync(startDate, endDate, locale);
    } catch (e) {
      return formatWeekDisplay(weekKey); // Fallback to default formatting
    }
  }

  Future<void> _confirmDeleteWeek(BuildContext context) async {
    final weekDisplayText = await _formatWeekDisplayWithCustom(widget.weekKey, context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Week Entries',
        content: 'Are you sure you want to delete all time entries for $weekDisplayText?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true && widget.onDeleteWeek != null) {
      await widget.onDeleteWeek!(widget.entries);
    }
  }

  @override
  Widget build(BuildContext context) {
    double? progressPercentage;
    String? progressText;
    int? progressPercent;

    if (widget.minimumWeeklyHours != null && widget.minimumWeeklyHours! > 0) {
      final totalMinutes = timeToMinutes(widget.totalTime);
      final actualHours = totalMinutes / 60;
      final requiredMinutes = widget.minimumWeeklyHours! * 60;
      if (requiredMinutes > 0) {
        progressPercent = (totalMinutes / requiredMinutes * 100).round();
        progressPercent = progressPercent > 100 ? 100 : progressPercent;
        progressPercentage = progressPercent / 100;
      } else {
        progressPercent = 0;
        progressPercentage = 0.0;
      }
      progressText = '${actualHours.toStringAsFixed(1)} / ${widget.minimumWeeklyHours!.toDouble().toStringAsFixed(1)} hours';
    }

    final Color baseColor = widget.projectColor ?? Theme.of(context).colorScheme.primary;
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    Color cardColor;
    if (widget.isCurrentWeek) {
      cardColor = isDark
          ? baseColor.withOpacity(0.12)
          : Theme.of(context).colorScheme.surfaceContainerLowest;
    } else {
      cardColor = isDark
          ? Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3)
          : Theme.of(context).cardColor;
    }

    final double elevationValue = _isExpanded ? 1.5 : 0.5;
    final double borderRadiusValue = 12.0;
    final double borderWidthValue = widget.isCurrentWeek ? 1.0 : 0.8;
    final double verticalPadding = 12.0;
    final double horizontalPadding = 10.0;
    final double iconSize = 18.0;
    final double sizedBoxWidth = 6.0;
    final double titleFontSize = 14.5;
    final double totalTimeFontSize = 12.5;
    final double deleteIconSize = 18.0;
    final double arrowIconSize = 18.0;
    final double progressTextFontSize = 11.0;
    final double linearProgressMinHeight = 7.0;

    final bool isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    final double touchAreaPadding = 3.0;

    return Column(
      children: [
        Material(
          elevation: elevationValue,
          color: cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusValue),
            side: widget.isCurrentWeek
                ? BorderSide(color: baseColor.withOpacity(isDark ? 0.5 : 0.5), width: borderWidthValue)
                : BorderSide(color: Theme.of(context).dividerColor.withOpacity(isDark ? 0.2 : 0.1), width: borderWidthValue),
          ),
          child: InkWell(
            onTap: _toggleExpanded,
            borderRadius: BorderRadius.circular(borderRadiusValue),
            child: Padding(
              padding: EdgeInsets.symmetric(
                vertical: verticalPadding,
                horizontal: horizontalPadding
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            FutureBuilder<String>(
                              future: _formatWeekDisplayWithCustom(widget.weekKey, context),
                              initialData: formatWeekDisplayWithContext(widget.weekKey, context),
                              builder: (context, snapshot) {
                                final text = snapshot.data ?? formatWeekDisplayWithContext(widget.weekKey, context);
                                final displayText = ResponsiveUtils.isSmallScreen(context)
                                    ? LocaleDateUtils.shortenYearsInText(text)
                                    : text;
                                return Text(
                                  displayText,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: titleFontSize,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                );
                              },
                            )
                          ],
                        )
                      ),
                      if (progressPercentage != null && progressText != null && progressPercent != null) ...[
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 4.0
                          ),
                          decoration: BoxDecoration(
                            color: getProgressColor(progressPercent).withOpacity(0.15),
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          child: Text(progressText.replaceAll('hours', 'h'),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: progressTextFontSize,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      ],
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.5 : 1),
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Text(
                          isSmallScreen && widget.totalTime.contains(':')
                              ? widget.totalTime.replaceAll('h ', 'h')
                              : widget.totalTime,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: totalTimeFontSize,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                      if (widget.onDeleteWeek != null && widget.entries.isNotEmpty)
                        Padding(
                          padding: EdgeInsets.only(left: 1.0),
                          child: InkWell(
                            onTap: () => _confirmDeleteWeek(context),
                            borderRadius: BorderRadius.circular(20.0),
                            child: Padding(
                              padding: EdgeInsets.all(touchAreaPadding),
                              child: Icon(
                                Icons.delete_sweep_outlined,
                                size: deleteIconSize,
                                color: Colors.red.shade400.withOpacity(0.9),
                              ),
                            ),
                          ),
                        ),
                      Padding(
                        padding: EdgeInsets.zero,
                        child: InkWell(
                          onTap: _toggleExpanded,
                          borderRadius: BorderRadius.circular(20.0),
                          child: Padding(
                            padding: EdgeInsets.all(touchAreaPadding),
                            child: Icon(
                              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                              size: arrowIconSize,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (progressPercentage != null && progressText != null && progressPercent != null) ...[
                    SizedBox(height: 10.0),
                    ClipRRect(
                        borderRadius: BorderRadius.circular(6.0),
                        child: LinearProgressIndicator(
                          value: progressPercentage,
                          backgroundColor: getProgressColor(progressPercent).withOpacity(0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(getProgressColor(progressPercent)),
                          minHeight: linearProgressMinHeight,
                        )
                    ),
                  ],
                  AnimatedSize(
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeInOutCubic,
                    alignment: Alignment.topCenter,
                    child: _isExpanded && widget.entries.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.only(top: 12.0),
                            child: Column(
                              children: List.generate(widget.entries.length, (index) {
                                final entry = widget.entries[index];
                                return Padding(
                                  padding: EdgeInsets.only(bottom: index == widget.entries.length - 1 ? 0 : 8.0),
                                  child: TimeEntryItem(
                                    key: ValueKey(entry.id),
                                    entry: entry,
                                    projectColor: widget.projectColor ?? Theme.of(context).colorScheme.primary,
                                    onEdit: widget.onEdit,
                                    onDelete: widget.onDelete,
                                  ),
                                );
                              }),
                            ),
                          )
                        : _isExpanded && widget.entries.isEmpty
                            ? Padding(
                                padding: const EdgeInsets.only(top: 12.0, bottom: 4.0),
                                child: Text(
                                  "No entries for this week.",
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    fontSize: 13.0,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              )
                            : SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }


}

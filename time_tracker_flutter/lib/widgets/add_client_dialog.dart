import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

/// Dialog for creating new clients with validation
class AddClientDialog extends StatefulWidget {
  final String? initialName;
  final Client? clientToEdit;
  final ClientService? clientService;

  const AddClientDialog({
    super.key,
    this.initialName,
    this.clientToEdit,
    this.clientService,
  });

  @override
  State<AddClientDialog> createState() => _AddClientDialogState();
}

class _AddClientDialogState extends State<AddClientDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  
  // Address fields
  final _streetController = TextEditingController();
  final _street2Controller = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _countryController = TextEditingController();
  final _taxIdController = TextEditingController();

  late final ClientService _clientService;
  bool _isLoading = false;
  bool _showAddressFields = false;
  List<Client> _potentialDuplicates = [];

  @override
  void initState() {
    super.initState();
    _clientService = widget.clientService ?? ClientService();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.clientToEdit != null) {
      final client = widget.clientToEdit!;
      _nameController.text = client.name;
      _emailController.text = client.email ?? '';
      _phoneController.text = client.phone ?? '';
      _notesController.text = client.notes ?? '';
      _taxIdController.text = client.taxId ?? '';
      
      if (client.address != null) {
        _showAddressFields = true;
        _streetController.text = client.address!.street;
        _street2Controller.text = client.address!.street2 ?? '';
        _cityController.text = client.address!.city;
        _stateController.text = client.address!.state ?? '';
        _postalCodeController.text = client.address!.postalCode;
        _countryController.text = client.address!.country;
      }
    } else if (widget.initialName != null) {
      _nameController.text = widget.initialName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _streetController.dispose();
    _street2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _countryController.dispose();
    _taxIdController.dispose();
    super.dispose();
  }

  Future<void> _checkForDuplicates() async {
    if (_nameController.text.trim().isEmpty) return;

    try {
      final tempClient = Client(
        id: widget.clientToEdit?.id,
        name: _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
      );

      final duplicates = await _clientService.findPotentialDuplicates(tempClient);
      setState(() {
        _potentialDuplicates = duplicates;
      });
    } catch (e) {
      // Ignore errors in duplicate checking
      debugPrint('Error checking for duplicates: $e');
    }
  }

  Future<void> _saveClient() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check for duplicates before saving
    await _checkForDuplicates();
    
    if (_potentialDuplicates.isNotEmpty) {
      final shouldContinue = await _showDuplicateWarning();
      if (!shouldContinue) return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      Address? address;
      if (_showAddressFields && _hasAddressData()) {
        address = Address(
          street: _streetController.text.trim(),
          street2: _street2Controller.text.trim().isEmpty ? null : _street2Controller.text.trim(),
          city: _cityController.text.trim(),
          state: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
          postalCode: _postalCodeController.text.trim(),
          country: _countryController.text.trim(),
        );
      }

      final client = widget.clientToEdit?.copyWith(
        name: _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        address: address,
        taxId: _taxIdController.text.trim().isEmpty ? null : _taxIdController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      ) ?? Client(
        name: _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        address: address,
        taxId: _taxIdController.text.trim().isEmpty ? null : _taxIdController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      await _clientService.saveClient(client);

      if (mounted) {
        Navigator.pop(context, client);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error saving client: ${e.toString()}', context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _hasAddressData() {
    return _streetController.text.trim().isNotEmpty ||
           _cityController.text.trim().isNotEmpty ||
           _postalCodeController.text.trim().isNotEmpty ||
           _countryController.text.trim().isNotEmpty;
  }

  Future<bool> _showDuplicateWarning() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Potential Duplicate'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Similar clients already exist:'),
            const SizedBox(height: 8),
            ..._potentialDuplicates.map((client) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '• ${client.name}${client.email != null ? ' (${client.email})' : ''}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            )),
            const SizedBox(height: 16),
            const Text('Do you want to continue creating this client?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Continue'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.clientToEdit != null;
    
    return AlertDialog(
      title: Text(isEditing ? 'Edit Client' : 'Add Client'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Basic Information
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Client Name *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a client name';
                    }
                    return null;
                  },
                  autofocus: !isEditing,
                  onChanged: (_) => _checkForDuplicates(),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    border: OutlineInputBorder(),
                    hintText: '<EMAIL>',
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      if (!_clientService.isValidEmail(value.trim())) {
                        return 'Please enter a valid email address';
                      }
                    }
                    return null;
                  },
                  onChanged: (_) => _checkForDuplicates(),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Phone',
                    border: OutlineInputBorder(),
                    hintText: '+****************',
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      if (!_clientService.isValidPhone(value.trim())) {
                        return 'Please enter a valid phone number';
                      }
                    }
                    return null;
                  },
                  onChanged: (_) => _checkForDuplicates(),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _taxIdController,
                  decoration: const InputDecoration(
                    labelText: 'Tax ID',
                    border: OutlineInputBorder(),
                    hintText: 'Tax identification number',
                  ),
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      if (!_clientService.isValidTaxId(value.trim())) {
                        return 'Please enter a valid tax ID';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Address Section
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Address',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    Switch(
                      value: _showAddressFields,
                      onChanged: (value) {
                        setState(() {
                          _showAddressFields = value;
                          if (!value) {
                            // Clear address fields when hiding
                            _streetController.clear();
                            _street2Controller.clear();
                            _cityController.clear();
                            _stateController.clear();
                            _postalCodeController.clear();
                            _countryController.clear();
                          }
                        });
                      },
                    ),
                  ],
                ),
                
                if (_showAddressFields) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _streetController,
                    decoration: const InputDecoration(
                      labelText: 'Street Address *',
                      border: OutlineInputBorder(),
                    ),
                    validator: _showAddressFields ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a street address';
                      }
                      return null;
                    } : null,
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _street2Controller,
                    decoration: const InputDecoration(
                      labelText: 'Street Address 2',
                      border: OutlineInputBorder(),
                      hintText: 'Apartment, suite, etc.',
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          controller: _cityController,
                          decoration: const InputDecoration(
                            labelText: 'City *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _showAddressFields ? (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a city';
                            }
                            return null;
                          } : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: _stateController,
                          decoration: const InputDecoration(
                            labelText: 'State',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _postalCodeController,
                          decoration: const InputDecoration(
                            labelText: 'Postal Code *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _showAddressFields ? (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a postal code';
                            }
                            return null;
                          } : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: _countryController,
                          decoration: const InputDecoration(
                            labelText: 'Country *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _showAddressFields ? (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a country';
                            }
                            return null;
                          } : null,
                        ),
                      ),
                    ],
                  ),
                ],
                
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                    hintText: 'Additional notes about this client',
                  ),
                  maxLines: 3,
                ),

                // Duplicate warning
                if (_potentialDuplicates.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.warning,
                              color: Theme.of(context).colorScheme.onErrorContainer,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Similar clients found',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        ..._potentialDuplicates.take(3).map((client) => Text(
                          '• ${client.name}${client.email != null ? ' (${client.email})' : ''}',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onErrorContainer,
                            fontSize: 12,
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveClient,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }
}
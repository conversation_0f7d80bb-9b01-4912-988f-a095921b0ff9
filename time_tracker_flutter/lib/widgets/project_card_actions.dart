import 'package:flutter/material.dart';

class ProjectCardActionButton extends StatelessWidget {
  final IconData icon;
  final String tooltip;
  final Color color;
  final VoidCallback onPressed;

  const ProjectCardActionButton({
    super.key,
    required this.icon,
    required this.tooltip,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(icon, size: 20.0, color: color.withOpacity(0.9)),
      tooltip: tooltip,
      onPressed: onPressed,
      splashRadius: 20.0,
      padding: EdgeInsets.all(6.0),
      constraints: const BoxConstraints(),
    );
  }
}

// Add this alias for backward compatibility with previous refactor
typedef ProjectCardAction = ProjectCardActionButton;

class ProjectCardDropdownAction extends StatelessWidget {
  final IconData icon;
  final String tooltip;
  final Color color;
  final VoidCallback onPressed;

  const ProjectCardDropdownAction({
    super.key,
    required this.icon,
    required this.tooltip,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(icon, size: 24.0, color: color.withOpacity(0.9)),
      tooltip: tooltip,
      onPressed: onPressed,
      splashRadius: 24.0,
      padding: EdgeInsets.all(4.0),
    );
  }
}

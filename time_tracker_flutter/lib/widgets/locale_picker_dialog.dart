import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class LocalePickerDialog extends StatefulWidget {
  final String? initialLocale;

  const LocalePickerDialog({super.key, this.initialLocale});

  @override
  State<LocalePickerDialog> createState() => _LocalePickerDialogState();
}

class _LocalePickerDialogState extends State<LocalePickerDialog> {
  String? _selectedLocale;
  final DateTime _sampleDate = DateTime(2025, 6, 5, 14, 30);

  // Common locales with their display names
  final List<Map<String, String>> _availableLocales = [
    {'code': 'en_US', 'name': 'English (United States)', 'example': 'Jun 5, 2025'},
    {'code': 'en_GB', 'name': 'English (United Kingdom)', 'example': '5 Jun 2025'},
    {'code': 'de_DE', 'name': 'Deutsch (Deutschland)', 'example': '5. Juni 2025'},
    {'code': 'fr_FR', 'name': 'Français (France)', 'example': '5 juin 2025'},
    {'code': 'es_ES', 'name': 'Español (España)', 'example': '5 jun 2025'},
    {'code': 'it_IT', 'name': 'Italiano (Italia)', 'example': '5 giu 2025'},
    {'code': 'pt_PT', 'name': 'Português (Portugal)', 'example': '5 de jun. de 2025'},
    {'code': 'pt_BR', 'name': 'Português (Brasil)', 'example': '5 de jun. de 2025'},
    {'code': 'ru_RU', 'name': 'Русский (Россия)', 'example': '5 июн. 2025 г.'},
    {'code': 'ja_JP', 'name': '日本語 (日本)', 'example': '2025年6月5日'},
    {'code': 'ko_KR', 'name': '한국어 (대한민국)', 'example': '2025. 6. 5.'},
    {'code': 'zh_CN', 'name': '中文 (中国)', 'example': '2025年6月5日'},
    {'code': 'zh_TW', 'name': '中文 (台灣)', 'example': '2025年6月5日'},
    {'code': 'ar_SA', 'name': 'العربية (السعودية)', 'example': '٥ يونيو ٢٠٢٥'},
    {'code': 'hi_IN', 'name': 'हिन्दी (भारत)', 'example': '5 जून 2025'},
    {'code': 'nl_NL', 'name': 'Nederlands (Nederland)', 'example': '5 jun. 2025'},
    {'code': 'sv_SE', 'name': 'Svenska (Sverige)', 'example': '5 juni 2025'},
    {'code': 'no_NO', 'name': 'Norsk (Norge)', 'example': '5. juni 2025'},
    {'code': 'da_DK', 'name': 'Dansk (Danmark)', 'example': '5. jun. 2025'},
    {'code': 'fi_FI', 'name': 'Suomi (Suomi)', 'example': '5. kesäk. 2025'},
    {'code': 'pl_PL', 'name': 'Polski (Polska)', 'example': '5 cze 2025'},
    {'code': 'cs_CZ', 'name': 'Čeština (Česko)', 'example': '5. 6. 2025'},
    {'code': 'hu_HU', 'name': 'Magyar (Magyarország)', 'example': '2025. jún. 5.'},
    {'code': 'el_GR', 'name': 'Ελληνικά (Ελλάδα)', 'example': '5 Ιουν 2025'},
    {'code': 'tr_TR', 'name': 'Türkçe (Türkiye)', 'example': '5 Haz 2025'},
  ];

  @override
  void initState() {
    super.initState();
    _selectedLocale = widget.initialLocale;
  }

  Locale _parseLocale(String localeCode) {
    final parts = localeCode.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    } else {
      return Locale(parts[0]);
    }
  }

  String _getActualExample(String localeCode) {
    try {
      final locale = _parseLocale(localeCode);
      return LocaleDateUtils.formatDate(_sampleDate, locale);
    } catch (e) {
      // Return the static example if formatting fails
      final localeData = _availableLocales.firstWhere(
        (l) => l['code'] == localeCode,
        orElse: () => {'example': 'Unknown format'},
      );
      return localeData['example']!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Application Locale'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Choose the locale for date and time formatting:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _availableLocales.length,
                itemBuilder: (context, index) {
                  final locale = _availableLocales[index];
                  final isSelected = _selectedLocale == locale['code'];
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 4),
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primaryContainer
                        : null,
                    child: ListTile(
                      title: Text(
                        locale['name']!,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Code: ${locale['code']}'),
                          Text(
                            'Example: ${_getActualExample(locale['code']!)}',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      trailing: isSelected 
                          ? Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        setState(() {
                          _selectedLocale = locale['code'];
                        });
                      },
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Note:',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'This will override your system locale for date formatting within the app only. Your system settings remain unchanged.',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _selectedLocale == null
              ? null
              : () => Navigator.of(context).pop(_selectedLocale),
          child: const Text('Apply'),
        ),
      ],
    );
  }
} 
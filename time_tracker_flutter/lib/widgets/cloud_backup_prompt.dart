import 'package:flutter/material.dart';

/// A widget that displays a prompt when a newer cloud backup is available
class CloudBackupPrompt extends StatelessWidget {
  /// Called when the user dismisses the prompt
  final VoidCallback onDismiss;
  
  /// Called when the user wants to restore the backup
  final VoidCallback onRestore;
  
  /// Whether a restore operation is in progress
  final bool isRestoring;
  
  /// Optional text to display in the prompt
  final String? promptText;

  const CloudBackupPrompt({
    super.key,
    required this.onDismiss,
    required this.onRestore,
    this.isRestoring = false,
    this.promptText,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 800),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.light
                ? Colors.blue.shade50
                : Colors.blue.shade900.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.light
                  ? Colors.blue.shade200
                  : Colors.blue.shade800,
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.cloud,
                    color: Theme.of(context).brightness == Brightness.light
                        ? Colors.blue.shade500
                        : Colors.blue.shade300,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      promptText ?? 'A newer backup is available.',
                      style: TextStyle(
                        color: Theme.of(context).brightness == Brightness.light
                            ? Colors.blue.shade700
                            : Colors.blue.shade300,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: onDismiss,
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).brightness == Brightness.light
                          ? Colors.blue.shade700
                          : Colors.blue.shade300,
                    ),
                    child: const Text('Dismiss'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: isRestoring ? null : onRestore,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).brightness == Brightness.light
                          ? Colors.blue.shade100
                          : Colors.blue.shade800.withOpacity(0.5),
                      foregroundColor: Theme.of(context).brightness == Brightness.light
                          ? Colors.blue.shade700
                          : Colors.blue.shade200,
                    ),
                    child: const Text('Restore'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 
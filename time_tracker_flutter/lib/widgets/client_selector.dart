import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/widgets/add_client_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

/// A widget that allows users to search and select clients with quick-add functionality
class ClientSelector extends StatefulWidget {
  final Client? selectedClient;
  final Function(Client?) onClientSelected;
  final String? hintText;
  final bool enabled;
  final bool showCreateNew;
  final ClientService? clientService;

  const ClientSelector({
    super.key,
    this.selectedClient,
    required this.onClientSelected,
    this.hintText,
    this.enabled = true,
    this.showCreateNew = true,
    this.clientService,
  });

  @override
  State<ClientSelector> createState() => _ClientSelectorState();
}

class _ClientSelectorState extends State<ClientSelector> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late final ClientService _clientService;

  List<ClientSelectionOption> _options = [];
  bool _isLoading = false;
  bool _showDropdown = false;
  String _lastSearchQuery = '';

  @override
  void initState() {
    super.initState();
    _clientService = widget.clientService ?? ClientService();
    _searchController.text = widget.selectedClient?.name ?? '';
    _loadInitialOptions();

    _searchController.addListener(_onSearchChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ClientSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedClient != oldWidget.selectedClient) {
      _searchController.text = widget.selectedClient?.name ?? '';
    }
  }

  Future<void> _loadInitialOptions() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final options = await _clientService.getClientSelectionOptions(
        includeCreateNew: false,
      );

      if (mounted) {
        setState(() {
          _options = options;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        showErrorSnackBar('Error loading clients: ${e.toString()}', context);
      }
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    if (query != _lastSearchQuery) {
      _lastSearchQuery = query;
      _searchClients(query);
    }
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      setState(() {
        _showDropdown = true;
      });
      if (_options.isEmpty) {
        _loadInitialOptions();
      }
    }
  }

  Future<void> _searchClients(String query) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final options = await _clientService.getClientSelectionOptions(
        searchQuery: query.trim().isEmpty ? null : query.trim(),
        includeCreateNew: widget.showCreateNew && query.trim().isNotEmpty,
      );

      if (mounted) {
        setState(() {
          _options = options;
          _isLoading = false;
          _showDropdown = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        showErrorSnackBar('Error searching clients: ${e.toString()}', context);
      }
    }
  }

  void _selectOption(ClientSelectionOption option) {
    if (option.isCreateNew) {
      _showCreateClientDialog(option.suggestedName);
    } else {
      _selectClient(option.client);
    }
  }

  void _selectClient(Client? client) {
    setState(() {
      _showDropdown = false;
      _searchController.text = client?.name ?? '';
    });

    widget.onClientSelected(client);
    _focusNode.unfocus();
  }

  Future<void> _showCreateClientDialog(String? suggestedName) async {
    final client = await showDialog<Client>(
      context: context,
      builder: (context) => AddClientDialog(
        initialName: suggestedName,
      ),
    );

    if (client != null) {
      _selectClient(client);
      // Refresh the options to include the new client
      _loadInitialOptions();
    }
  }

  void _clearSelection() {
    _selectClient(null);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _searchController,
          focusNode: _focusNode,
          enabled: widget.enabled,
          decoration: InputDecoration(
            labelText: 'Client',
            hintText: widget.hintText ?? 'Search or select a client',
            border: const OutlineInputBorder(),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                else if (widget.selectedClient != null)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: widget.enabled ? _clearSelection : null,
                    tooltip: 'Clear selection',
                  ),
                if (widget.showCreateNew)
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: widget.enabled ? () => _showCreateClientDialog(null) : null,
                    tooltip: 'Add new client',
                  ),
              ],
            ),
          ),
          validator: (value) {
            if (widget.selectedClient == null && (value == null || value.trim().isEmpty)) {
              return 'Please select a client';
            }
            return null;
          },
          onTap: () {
            if (widget.enabled) {
              setState(() {
                _showDropdown = true;
              });
              if (_options.isEmpty) {
                _loadInitialOptions();
              }
            }
          },
        ),
        if (_showDropdown && _options.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _options.length,
              itemBuilder: (context, index) {
                final option = _options[index];
                return ListTile(
                  dense: true,
                  leading: option.isCreateNew
                      ? Icon(
                          Icons.add_circle_outline,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : Icon(
                          Icons.person,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                  title: Text(
                    option.displayText,
                    style: TextStyle(
                      color: option.isCreateNew
                          ? Theme.of(context).colorScheme.primary
                          : null,
                      fontWeight: option.isCreateNew ? FontWeight.w500 : null,
                    ),
                  ),
                  subtitle: option.client?.email != null
                      ? Text(option.client!.email!)
                      : null,
                  onTap: () => _selectOption(option),
                );
              },
            ),
          ),
      ],
    );
  }
}
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:intl/intl.dart';

class DateFormatTestWidget extends StatefulWidget {
  const DateFormatTestWidget({super.key});

  @override
  State<DateFormatTestWidget> createState() => _DateFormatTestWidgetState();
}

class _DateFormatTestWidgetState extends State<DateFormatTestWidget> {
  final DatabaseService _databaseService = DatabaseService();
  bool _isCustomFormatEnabled = false;
  String? _customFormat;
  final DateTime _sampleDate = DateTime(2025, 6, 5, 14, 30);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final isEnabled = await _databaseService.isCustomDateFormatEnabled();
    final format = await _databaseService.getCustomDateFormat();
    
    setState(() {
      _isCustomFormatEnabled = isEnabled;
      _customFormat = format;
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentLocale = LocaleDateUtils.getCurrentLocale(context);
    
    // Different locale examples to show variety
    final localeExamples = [
      const Locale('en', 'US'), // US: Jun 5, 2025
      const Locale('en', 'GB'), // UK: 5 Jun 2025  
      const Locale('de', 'DE'), // German: 5. Juni 2025
      const Locale('fr', 'FR'), // French: 5 juin 2025
      const Locale('es', 'ES'), // Spanish: 5 jun 2025
      const Locale('ja', 'JP'), // Japanese: 2025年6月5日
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Date Format Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current system info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Settings',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('System Locale: ${currentLocale.toString()}'),
                    FutureBuilder<bool>(
                      future: _databaseService.isCustomLocaleEnabled(),
                      builder: (context, snapshot) {
                        final isCustomLocaleEnabled = snapshot.data ?? false;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Custom Locale Enabled: ${isCustomLocaleEnabled ? "Yes" : "No"}'),
                            if (isCustomLocaleEnabled)
                              FutureBuilder<String?>(
                                future: _databaseService.getCustomLocale(),
                                builder: (context, localeSnapshot) {
                                  final customLocale = localeSnapshot.data;
                                  return Text('Custom Locale: ${customLocale ?? "None"}');
                                },
                              ),
                          ],
                        );
                      },
                    ),
                    Text('Custom Format Enabled: ${_isCustomFormatEnabled ? "Yes" : "No"}'),
                    if (_isCustomFormatEnabled && _customFormat != null)
                      Text('Custom Format: $_customFormat'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Sample date formatting
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sample Date: ${_sampleDate.toString().split(' ')[0]}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    // Current system formatting
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Your Current Format:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                          const SizedBox(height: 4),
                          FutureBuilder<String>(
                            future: LocaleDateUtils.formatDateWithAllCustom(_sampleDate, context),
                            builder: (context, snapshot) {
                              return Text(
                                snapshot.data ?? 'Loading...',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Different locale examples
            Text(
              'How This Date Appears in Different Locales:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Expanded(
              child: ListView.builder(
                itemCount: localeExamples.length,
                itemBuilder: (context, index) {
                  final locale = localeExamples[index];
                  final isCurrentLocale = locale.toString() == currentLocale.toString();
                  
                  return Card(
                    color: isCurrentLocale 
                        ? Theme.of(context).colorScheme.secondaryContainer
                        : null,
                    child: ListTile(
                      title: Text(
                        '${locale.languageCode.toUpperCase()} (${locale.countryCode})',
                        style: TextStyle(
                          fontWeight: isCurrentLocale ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Date: ${LocaleDateUtils.formatDate(_sampleDate, locale)}'),
                          Text('Short: ${LocaleDateUtils.formatShortDate(_sampleDate, locale)}'),
                          Text('Time: ${LocaleDateUtils.formatTime(_sampleDate, locale)}'),
                        ],
                      ),
                      trailing: isCurrentLocale 
                          ? Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                    ),
                  );
                },
              ),
            ),
            
            // Instructions
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How to Test Custom Settings:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text('1. Go to Settings → Date Format'),
                  const Text('2. Enable "Override System Locale" to choose a different locale'),
                  const Text('3. Enable "Override System Date Format" for custom patterns'),
                  const Text('4. Return here to see the difference!'),
                  const SizedBox(height: 8),
                  Text(
                    'Note: Custom locale affects all date formatting, while custom format only affects the date pattern.',
                    style: TextStyle(
                      fontSize: 11,
                      fontStyle: FontStyle.italic,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 
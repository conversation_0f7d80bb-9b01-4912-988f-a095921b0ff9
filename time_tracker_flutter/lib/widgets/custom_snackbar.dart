import 'dart:async';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';

/// A custom snackbar implementation that can be displayed as an overlay
class CustomSnackbar extends StatefulWidget {
  final String message;
  final bool isError;
  final Duration duration;
  final VoidCallback? onDismiss;
  final Widget? action;
  final IconData? icon;

  const CustomSnackbar({
    Key? key,
    required this.message,
    this.isError = false,
    this.duration = const Duration(seconds: 4),
    this.onDismiss,
    this.action,
    this.icon,
  }) : super(key: key);

  @override
  State<CustomSnackbar> createState() => _CustomSnackbarState();
}

class _CustomSnackbarState extends State<CustomSnackbar> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  late Timer _timer;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.forward();

    _timer = Timer(widget.duration, () {
      _dismiss();
    });
  }

  void _dismiss() {
    if (!mounted) return;

    _controller.reverse().then((_) {
      if (widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SlideTransition(
      position: _offsetAnimation,
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: ConstrainedWidthContainer(
          child: Material(
            elevation: 3,
            color: widget.isError
                ? colorScheme.error
                : colorScheme.surfaceVariant,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical:16
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    widget.icon ??
                      (widget.isError ? Icons.error_outline : Icons.info_outline),
                    color: widget.isError
                        ? colorScheme.onError
                        : colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: textTheme.bodyMedium?.copyWith(
                        color: widget.isError
                            ? colorScheme.onError
                            : colorScheme.onSurfaceVariant,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                  if (widget.action != null) ...[
                    const SizedBox(width: 8),
                    widget.action!,
                  ] else ...[
                    const SizedBox(width: 8),
                    InkWell(
                      onTap: _dismiss,
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Icon(
                          Icons.close,
                          color: widget.isError
                              ? colorScheme.onError
                              : colorScheme.onSurfaceVariant,
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// SnackbarOverlay handles the display and queuing of custom snackbars
class SnackbarOverlay {
  static final SnackbarOverlay _instance = SnackbarOverlay._internal();
  static SnackbarOverlay get instance => _instance;

  factory SnackbarOverlay() => _instance;

  SnackbarOverlay._internal();

  OverlayEntry? _currentEntry;
  final List<_SnackbarInfo> _queue = [];
  bool _isDisplaying = false;

  void show({
    required BuildContext context,
    required String message,
    bool isError = false,
    Duration duration = const Duration(seconds: 4),
    Widget? action,
    IconData? icon,
  }) {
    final info = _SnackbarInfo(
      context: context,
      message: message,
      isError: isError,
      duration: duration,
      action: action,
      icon: icon,
    );

    _queue.add(info);
    if (!_isDisplaying) {
      _displayNext();
    }
  }

  void _displayNext() {
    if (_queue.isEmpty) {
      _isDisplaying = false;
      return;
    }

    _isDisplaying = true;
    final info = _queue.removeAt(0);

    _currentEntry?.remove();


    _currentEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: 0,
        right: 0,
        bottom: 33,
        child: CustomSnackbar(
          message: info.message,
          isError: info.isError,
          duration: info.duration,
          action: info.action,
          icon: info.icon,
          onDismiss: () {
            _currentEntry?.remove();
            _currentEntry = null;
            _displayNext();
          },
        ),
      ),
    );

    Overlay.of(info.context).insert(_currentEntry!);
  }

  void clearAll() {
    _queue.clear();
    _currentEntry?.remove();
    _currentEntry = null;
    _isDisplaying = false;
  }
}

class _SnackbarInfo {
  final BuildContext context;
  final String message;
  final bool isError;
  final Duration duration;
  final Widget? action;
  final IconData? icon;

  _SnackbarInfo({
    required this.context,
    required this.message,
    required this.isError,
    required this.duration,
    this.action,
    this.icon,
  });
}
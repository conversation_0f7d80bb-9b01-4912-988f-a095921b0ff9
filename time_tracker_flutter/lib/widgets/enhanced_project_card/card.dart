import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/content.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/dialogs.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/header.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/mobile_menu.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/tracking_badge.dart';
import 'package:time_tracker_flutter/widgets/project_card_notifiers.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';
import 'package:time_tracker_flutter/widgets/location_area_selector_dialog.dart';

/// EnhancedProjectCard is the main project card widget shown in the project list.
class EnhancedProjectCard extends StatefulWidget {
  final Project project;
  final Function(Project) onEdit;
  final Function(Project) onDelete;
  final Function(Project) onAddTimeEntry;
  final ProjectCardNotifiers notifiers;
  final int projectIndex;
  final Function(int) onReorderStart;

  const EnhancedProjectCard({
    super.key,
    required this.project,
    required this.onEdit,
    required this.onDelete,
    required this.onAddTimeEntry,
    required this.notifiers,
    required this.projectIndex,
    required this.onReorderStart,
  });

  @override
  State<EnhancedProjectCard> createState() => _EnhancedProjectCardState();
}

class _EnhancedProjectCardState extends State<EnhancedProjectCard> with SingleTickerProviderStateMixin {
  final DatabaseService _databaseService = DatabaseService();
  final LocationService _locationService = LocationService();
  List<TimeEntry>? _cachedEntries;
  bool _isLoading = true;
  String? _error;
  late final Function() _dataChangeListener;
  final ValueNotifier<bool> _isSettingsMenuOpen = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    widget.notifiers.isCollapsed.addListener(_onCollapseChanged);
    _dataChangeListener = () {
      if (mounted) _refreshTimeEntries();
    };
    _databaseService.addDataChangeListener(_dataChangeListener);
    _loadTimeEntries();
  }

  Future<void> _loadTimeEntries() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final entries = await _databaseService.getTimeEntries(projectId: widget.project.id);
      if (!mounted) return;

      setState(() {
        _cachedEntries = entries;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshTimeEntries() async {
    await _loadTimeEntries();
  }

  @override
  void dispose() {
    widget.notifiers.isCollapsed.removeListener(_onCollapseChanged);
    _databaseService.removeDataChangeListener(_dataChangeListener);
    _isSettingsMenuOpen.dispose();
    super.dispose();
  }

  void _onCollapseChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  void _showAllWeeksDialog(BuildContext context, List<TimeEntry> entries, Color projectColor) {
    EnhancedProjectCardDialogs.showAllWeeksDialog(
      context,
      entries,
      projectColor,
      widget.project.name,
      widget.project.minimumWeeklyHours,
      _refreshTimeEntries,
      _databaseService,
    );
  }

  void _showLocationConfig(Project project) {
    LocationAreaSelectorDialog.show(context, project);
  }

  @override
  Widget build(BuildContext context) {
    final Color projectColor = widget.project.color ?? Theme.of(context).colorScheme.primary;
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    final bool isLoadingEntries = _isLoading;
    final bool hasErrorLoadingEntries = _error != null;
    final List<TimeEntry> entries = _cachedEntries ?? [];
    final String totalTime = entries.isNotEmpty ? calculateTotalTime(entries) : "00:00";

    // Define responsive values for the main card structure
    final double cardHorizontalPadding = isMobile ? 12.0 : 20.0;
    final double cardVerticalPadding = isMobile ? 8.0 : 16.0;
    final double cardElevation = isDark ? 2.0 : 4.0;
    final double cardBorderRadius = 24.0;
    final double cardBorderWidth = isDark ? 1.0 : 1.5;

    return ConstrainedWidthContainer(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: cardHorizontalPadding,
          vertical: cardVerticalPadding
        ),
        child: AnimatedSize(
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeInOutCubicEmphasized,
          alignment: Alignment.topCenter,
          child: Stack(
            children: [
              Card(
                margin: EdgeInsets.zero,
                clipBehavior: Clip.none,
                elevation: cardElevation,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(cardBorderRadius),
                  side: BorderSide(
                    color: projectColor.withOpacity(isDark ? 0.6 : 0.35),
                    width: cardBorderWidth,
                  ),
                ),
                color: isDark
                    ? Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5)
                    : Theme.of(context).colorScheme.surface,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Card Header
                    EnhancedProjectCardHeader(
                      project: widget.project,
                      projectColor: projectColor,
                      isDark: isDark,
                      isMobile: isMobile,
                      isLoadingEntries: isLoadingEntries,
                      hasErrorLoadingEntries: hasErrorLoadingEntries,
                      entries: entries,
                      totalTime: totalTime,
                      isSettingsMenuOpen: _isSettingsMenuOpen,
                      onAddTimeEntry: widget.onAddTimeEntry,
                      onEdit: widget.onEdit,
                      onDelete: widget.onDelete,
                      showAllWeeksDialog: _showAllWeeksDialog,
                      projectIndex: widget.projectIndex,
                      onReorderStart: widget.onReorderStart,
                      cardBorderRadius: cardBorderRadius,
                      isCollapsed: widget.notifiers.isCollapsed,
                      onLocationConfig: _showLocationConfig,
                    ),

                    // Mobile Settings Menu (only visible on mobile)
                    if (isMobile)
                      EnhancedProjectCardMobileMenu(
                        isSettingsMenuOpen: _isSettingsMenuOpen,
                        project: widget.project,
                        entries: entries,
                        projectColor: projectColor,
                        isDark: isDark,
                        onAddTimeEntry: widget.onAddTimeEntry,
                        showAllWeeksDialog: _showAllWeeksDialog,
                        onEdit: widget.onEdit,
                        onDelete: widget.onDelete,
                        onLocationConfig: _showLocationConfig,
                      ),

                    // Card Content (expandable area)
                    EnhancedProjectCardContent(
                      project: widget.project,
                      isLoadingEntries: isLoadingEntries,
                      hasErrorLoadingEntries: hasErrorLoadingEntries,
                      entries: entries,
                      projectColor: projectColor,
                      error: _error,
                      onAddTimeEntry: (project) => widget.onAddTimeEntry(project),
                      refreshTimeEntries: _refreshTimeEntries,
                      databaseService: _databaseService,
                      notifiers: widget.notifiers,
                    ),
                  ],
                ),
              ),
              // Tracking badge positioned at the top-right edge of the card
              Positioned(
                top: 0.0,
                right: 1.0,
                child: TrackingBadge(
                  projectId: widget.project.id,
                  projectColor: projectColor,
                  isDark: isDark,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

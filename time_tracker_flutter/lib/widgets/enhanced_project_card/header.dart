import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/widgets/project_card_actions.dart';
import 'package:time_tracker_flutter/widgets/project_card_progress.dart';
import 'package:time_tracker_flutter/widgets/location_area_selector_dialog.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/tracking_badge.dart';

class EnhancedProjectCardHeader extends StatelessWidget {
  final Project project;
  final Color projectColor;
  final bool isDark;
  final bool isMobile;
  final bool isLoadingEntries;
  final bool hasErrorLoadingEntries;
  final List<TimeEntry> entries;
  final String totalTime;
  final ValueNotifier<bool> isSettingsMenuOpen;
  final Function(Project) onAddTimeEntry;
  final Function(Project) onEdit;
  final Function(Project) onDelete;
  final Function(BuildContext, List<TimeEntry>, Color) showAllWeeksDialog;
  final int projectIndex;
  final Function(int) onReorderStart;
  final double cardBorderRadius;
  final ValueNotifier<bool> isCollapsed;
  final Function(Project)? onLocationConfig;

  const EnhancedProjectCardHeader({
    super.key,
    required this.project,
    required this.projectColor,
    required this.isDark,
    required this.isMobile,
    required this.isLoadingEntries,
    required this.hasErrorLoadingEntries,
    required this.entries,
    required this.totalTime,
    required this.isSettingsMenuOpen,
    required this.onAddTimeEntry,
    required this.onEdit,
    required this.onDelete,
    required this.showAllWeeksDialog,
    required this.projectIndex,
    required this.onReorderStart,
    required this.cardBorderRadius,
    required this.isCollapsed,
    this.onLocationConfig,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final double headerDragHandlePadding = isMobile ? 8.0 : 12.0;
    final double headerDragHandleIconSize = 22.0;
    final double headerProjectIconSize = isMobile ? 18.0 : 20.0;
    final double headerSizedBoxWidth = 12.0;
    final double headerProjectNameFontSize = isMobile ? 17.0 : 19.0;
    final double headerTotalTimeFontSize = isMobile ? 12.0 : 14.0;
    final double headerProgressIndicatorSize = 20.0;
    final double headerNoEntriesFontSize = 12.0;

    return Material(
      color: isDark
          ? theme.colorScheme.surfaceContainerHigh // M3 color
          : theme.colorScheme.surfaceContainerLow, // M3 color
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(cardBorderRadius),
          topRight: Radius.circular(cardBorderRadius),
          // No bottom radius if content is visible and card is expanded
          bottomLeft: Radius.circular(isCollapsed.value ? cardBorderRadius : 0),
          bottomRight: Radius.circular(isCollapsed.value ? cardBorderRadius : 0),
        ),
      ),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          onTap: () => isCollapsed.value = !isCollapsed.value,
          borderRadius: BorderRadius.only( // Ensure InkWell matches shape
            topLeft: Radius.circular(cardBorderRadius),
            topRight: Radius.circular(cardBorderRadius),
            bottomLeft: Radius.circular(isCollapsed.value ? cardBorderRadius : 0),
            bottomRight: Radius.circular(isCollapsed.value ? cardBorderRadius : 0),
          ),
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              isMobile ? 2.0 : 4.0, // Reduced left padding for drag handle
              isMobile ? 6.0 : 8.0,
              isMobile ? 6.0 : 8.0,
              isMobile ? 6.0 : 8.0,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ReorderableDragStartListener(
                  index: projectIndex,
                  child: GestureDetector(
                    onTapDown: (_) {
                      onReorderStart(projectIndex);
                    },
                    child: Container(
                      // Make drag handle area slightly larger for touch
                      padding: EdgeInsets.all(headerDragHandlePadding),
                      child: Icon(
                        Icons.drag_indicator_rounded, // Updated Icon
                        size: headerDragHandleIconSize,
                        color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(project.iconData ?? Icons.folder_special_outlined, size: headerProjectIconSize, color: projectColor),
                          SizedBox(width: headerSizedBoxWidth),
                          Expanded(
                            child: Text(
                              project.name,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: headerProjectNameFontSize,
                                fontWeight: FontWeight.w600,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                          ),
                          SizedBox(width: headerSizedBoxWidth),
                          AnimatedRotation(
                            turns: isCollapsed.value ? 0.0 : 0.5,
                            duration: const Duration(milliseconds: 200),
                            child: Icon(
                              Icons.expand_more,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          SizedBox(width: isMobile ? 4.0 : 8.0),
                          if (isLoadingEntries)
                            SizedBox(height: headerProgressIndicatorSize, width: headerProgressIndicatorSize, child: CircularProgressIndicator(strokeWidth: 2.0, color: projectColor))
                          else if (hasErrorLoadingEntries)
                            Icon(Icons.error_outline, color: theme.colorScheme.error, size: headerProgressIndicatorSize)
                          else if (entries.isNotEmpty)
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                              decoration: BoxDecoration(
                                color: projectColor.withOpacity(isDark ? 0.35 : 0.2),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Text(
                                totalTime,
                                style: TextStyle(
                                  fontSize: headerTotalTimeFontSize,
                                  fontWeight: FontWeight.w500,
                                  color: projectColor.lighterShadeForContrast(isDark),
                                ),
                              ),
                            )
                          else
                            SizedBox(height: headerProgressIndicatorSize, width: headerProgressIndicatorSize),
                        ],
                      ),
                      if (!isLoadingEntries && !hasErrorLoadingEntries && entries.isNotEmpty && project.minimumWeeklyHours != null) ...[
                        Padding(
                          padding: EdgeInsets.only(left: headerProjectIconSize + headerSizedBoxWidth, right: 0, top: 2, bottom: 2),
                          child: ProjectCardProgressBar(
                            entries: entries,
                            targetHours: project.minimumWeeklyHours!,
                            projectColor: projectColor,
                            indicatorMinHeight: 5.0
                          ),
                        )
                      ] else if (project.minimumWeeklyHours != null && (isLoadingEntries || (entries.isEmpty && !hasErrorLoadingEntries))) ...[
                         Padding(
                           padding: EdgeInsets.only(left: headerProjectIconSize + headerSizedBoxWidth, right: 0, top: 4.0, bottom: 2.0),
                           child: isLoadingEntries
                               ? SizedBox.shrink()
                               : Text("No entries for progress", style: TextStyle(fontSize: headerNoEntriesFontSize, color: theme.colorScheme.onSurfaceVariant)),
                         )
                      ] else if (project.minimumWeeklyHours == null && !isLoadingEntries && !hasErrorLoadingEntries && entries.isEmpty) ...[
                        Padding(
                          padding: EdgeInsets.only(left: headerProjectIconSize + headerSizedBoxWidth, top: 4.0, bottom: 2.0),
                          child: Text("No time entries", style: TextStyle(fontSize: headerNoEntriesFontSize, color: theme.colorScheme.onSurfaceVariant)),
                        )
                      ],
                    ],
                  ),
                ),
                if (isMobile)
                  _buildMobileSettingsButton(context)
                else
                  _buildDesktopActionButtons(context),
                SizedBox(width: isMobile ? 2.0 : 8.0), // Reduce right padding for mobile
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileSettingsButton(BuildContext context) {
    // Simplified mobile menu button
    return IconButton(
      icon: Icon(Icons.more_vert_rounded),
      color: Theme.of(context).colorScheme.onSurfaceVariant,
      tooltip: 'More options',
      iconSize: 22.0, // Adjusted size
      padding: EdgeInsets.all(isMobile ? 8.0 : 12.0), // Consistent padding
      constraints: BoxConstraints(), // Remove default constraints if needed
      onPressed: () {
        isSettingsMenuOpen.value = !isSettingsMenuOpen.value;
      },
    );
  }

  Widget _buildDesktopActionButtons(BuildContext context) {
    final Color iconColor = Theme.of(context).colorScheme.onSurfaceVariant;
    return Row(
      mainAxisSize: MainAxisSize.min, // Ensure row takes minimum space
      children: [
        _buildAnimatedIconButton(
          context: context,
          icon: Icons.add_circle_outline_rounded,
          tooltip: 'Add Time Entry',
          color: Colors.green.shade600,
          onPressed: () => onAddTimeEntry(project),
        ),
        if (onLocationConfig != null)
          _buildAnimatedIconButton(
            context: context,
            icon: Icons.location_on_outlined,
            tooltip: 'Location Tracking',
            color: Colors.deepPurple.shade400,
            onPressed: () => onLocationConfig!(project),
          ),
        _buildAnimatedIconButton(
          context: context,
          icon: Icons.calendar_view_week_outlined,
          tooltip: 'View All Weeks',
          color: Colors.amber.shade700,
          onPressed: () => showAllWeeksDialog(context, entries, projectColor),
        ),
        _buildAnimatedIconButton(
          context: context,
          icon: Icons.edit_rounded,
          tooltip: 'Edit Project',
          color: Colors.blueAccent.shade400,
          onPressed: () => onEdit(project),
        ),
        _buildAnimatedIconButton(
          context: context,
          icon: Icons.delete_outline_rounded,
          tooltip: 'Delete Project',
          color: Colors.red.shade400,
          onPressed: () => onDelete(project),
        ),
      ],
    );
  }

  Widget _buildAnimatedIconButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: StatefulBuilder(
        builder: (context, setState) {
          bool _isHovered = false;
          return AnimatedContainer(
            duration: const Duration(milliseconds: 100),
            transform: Matrix4.identity()..scale(_isHovered ? 1.1 : 1.0),
            child: IconButton(
              icon: Icon(icon),
              tooltip: tooltip,
              color: color,
              iconSize: 20,
              mouseCursor: SystemMouseCursors.click,
              onPressed: () {
                // Add a small press animation
                setState(() => _isHovered = true);
                Future.delayed(const Duration(milliseconds: 100), () {
                  if (context.mounted) {
                    setState(() => _isHovered = false);
                  }
                });
                onPressed();
              },
              onHover: (hover) {
                setState(() => _isHovered = hover);
              },
            ),
          );
        },
      ),
    );
  }
}

// Helper extension for color contrast (can be moved to a utils file)
extension ColorBrightness on Color {
  Color lighterShadeForContrast(bool isDarkBackground) {
    if (isDarkBackground) {
      // If background is dark, text should be lighter
      return HSLColor.fromColor(this).withLightness((HSLColor.fromColor(this).lightness + 0.3).clamp(0.0, 1.0)).toColor();
    } else {
      // If background is light, text can be darker or a more saturated version of project color
      // For now, let's try a slightly darker shade if the project color is very light
      final hslColor = HSLColor.fromColor(this);
      if (hslColor.lightness > 0.7) {
        return hslColor.withLightness((hslColor.lightness - 0.2).clamp(0.0, 1.0)).toColor();
      }
      return this; // Or return a fixed dark color like Colors.black87
    }
  }
}

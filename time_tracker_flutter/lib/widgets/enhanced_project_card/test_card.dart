import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/index.dart';
import 'package:time_tracker_flutter/widgets/project_card_notifiers.dart';

void main() {
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Enhanced Project Card Test',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
      ),
      home: const TestScreen(),
    );
  }
}

class TestScreen extends StatefulWidget {
  const TestScreen({super.key});

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  final ProjectCardNotifiers _notifiers = ProjectCardNotifiers(
    isCollapsed: ValueNotifier<bool>(true),
    showAllWeeks: ValueNotifier<bool>(false),
  );

  @override
  void dispose() {
    _notifiers.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Project testProject = Project(
      id: 'test-project-1',
      name: 'Test Project',
      colorHex: '#2196F3',
      iconDataCodePoint: Icons.work_outline.codePoint,
      order: 0,
      minimumWeeklyHours: 10,
      maxWeeksInOverview: 3,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Project Card Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: EnhancedProjectCard(
          project: testProject,
          onEdit: (project) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Edit ${project.name}')),
            );
          },
          onDelete: (project) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Delete ${project.name}')),
            );
          },
          onAddTimeEntry: (project) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Add time entry to ${project.name}')),
            );
          },
          notifiers: _notifiers,
          projectIndex: 0,
          onReorderStart: (index) {},
        ),
      ),
    );
  }
}

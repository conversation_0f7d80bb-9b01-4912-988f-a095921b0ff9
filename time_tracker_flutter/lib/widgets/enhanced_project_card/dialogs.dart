import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/widgets/all_weeks_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

class EnhancedProjectCardDialogs {
  static void showAllWeeksDialog(
    BuildContext context,
    List<TimeEntry> entries,
    Color projectColor,
    String projectName,
    int? minimumWeeklyHours,
    Function() refreshTimeEntries,
    DatabaseService databaseService,
  ) {
    // Group entries by week
    final Map<String, Map<String, dynamic>> weeklyEntries = groupEntriesByWeek(entries);
    final Map<String, bool> expandedWeeks = {}; // Initialize expansion state if needed
    final String currentWeekKey = getCurrentWeekKey();
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AllWeeksDialog(
          projectName: projectName,
          weeklyEntries: weeklyEntries, // Pass grouped map
          expandedWeeks: expandedWeeks, // Pass expansion state map
          onToggleWeek: (weekKey) {
            debugPrint("Toggle week: $weekKey in AllWeeksDialog");
          },
          onEditEntry: (entry) {
            Navigator.pop(dialogContext); // Close the AllWeeksDialog
            showDialog(
              context: context, // Use original context for new dialog
              builder: (editDialogContext) => AddTimeEntryDialog(
                projectId: entry.projectId,
                initialEntry: entry,
                onTimeEntryAdded: (updatedEntry) {
                  refreshTimeEntries();
                  // No need to pop editDialogContext, AddTimeEntryDialog likely handles it
                },
              ),
            );
          },
          onDeleteEntry: (entry) async {
            final confirmed = await showDialog<bool>(
              context: context, // Use original context
              builder: (confirmDialogContext) => AlertDialog(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
                title: Text('Delete Time Entry', style: textTheme.titleLarge?.copyWith(fontSize: 18)),
                content: Text('Are you sure you want to delete this time entry?', style: textTheme.bodyMedium),
                actionsPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(confirmDialogContext, false),
                    child: Text('Cancel', style: textTheme.labelLarge?.copyWith(color: theme.colorScheme.primary)),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(confirmDialogContext, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.error,
                      foregroundColor: theme.colorScheme.onError,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
                      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    ),
                    child: Text('Delete', style: textTheme.labelLarge?.copyWith(color: theme.colorScheme.onError)),
                  ),
                ],
              ),
            );
            if (confirmed == true) {
              try {
                await databaseService.deleteTimeEntry(entry.id);
                refreshTimeEntries();
                // Potentially close AllWeeksDialog if an entry affecting its view is deleted
                // Navigator.pop(dialogContext); 
              } catch (e) {
                if (context.mounted) {
                  showErrorSnackBar('Error deleting time entry: ${e.toString()}', context);
                }
              }
            }
          },
          onDeleteWeek: (weekEntries) async {
            final confirmed = await showDialog<bool>(
              context: context, // Use original context
              builder: (confirmDialogContext) => AlertDialog(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
                title: Text('Delete Week Entries', style: textTheme.titleLarge?.copyWith(fontSize: 18)),
                content: Text('Delete all ${weekEntries.length} entries for this week?', style: textTheme.bodyMedium),
                actionsPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(confirmDialogContext, false),
                    child: Text('Cancel', style: textTheme.labelLarge?.copyWith(color: theme.colorScheme.primary)),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(confirmDialogContext, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.error,
                      foregroundColor: theme.colorScheme.onError,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
                      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    ),
                    child: Text('Delete All', style: textTheme.labelLarge?.copyWith(color: theme.colorScheme.onError)), // Changed to Delete All
                  ),
                ],
              ),
            );
            if (confirmed == true) {
              try {
                for (final entry in weekEntries) {
                  await databaseService.deleteTimeEntry(entry.id);
                }
                refreshTimeEntries();
                // Potentially close AllWeeksDialog
                // Navigator.pop(dialogContext);
              } catch (e) {
                if (context.mounted) {
                  showErrorSnackBar('Error deleting week entries: ${e.toString()}', context);
                }
              }
            }
          },
          currentWeekKey: currentWeekKey,
          projectColor: projectColor,
          minimumWeeklyHours: minimumWeeklyHours,
        );
      },
    );
  }
}
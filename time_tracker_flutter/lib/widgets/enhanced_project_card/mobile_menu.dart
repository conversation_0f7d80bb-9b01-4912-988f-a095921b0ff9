import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/widgets/project_card_actions.dart';

class EnhancedProjectCardMobileMenu extends StatelessWidget {
  final ValueNotifier<bool> isSettingsMenuOpen;
  final Project project;
  final List<TimeEntry> entries;
  final Color projectColor;
  final bool isDark;
  final Function(Project) onAddTimeEntry;
  final Function(BuildContext, List<TimeEntry>, Color) showAllWeeksDialog;
  final Function(Project) onEdit;
  final Function(Project) onDelete;
  final Function(Project) onLocationConfig;

  const EnhancedProjectCardMobileMenu({
    super.key,
    required this.isSettingsMenuOpen,
    required this.project,
    required this.entries,
    required this.projectColor,
    required this.isDark,
    required this.onAddTimeEntry,
    required this.showAllWeeksDialog,
    required this.onEdit,
    required this.onDelete,
    required this.onLocationConfig,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool isDarkTheme = theme.brightness == Brightness.dark;

    return ValueListenableBuilder<bool>(
      valueListenable: isSettingsMenuOpen,
      builder: (context, isOpen, child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 250),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: SizeTransition(
                sizeFactor: CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
                axisAlignment: -1.0, // Ensures it expands downwards from the header
                child: child,
              ),
            );
          },
          child: isOpen
              ? Container(
                  key: const ValueKey('mobile_menu_open'),
                  decoration: BoxDecoration(
                    color: isDarkTheme
                        ? theme.colorScheme.surfaceContainer // M3 color for dark theme
                        : theme.colorScheme.surfaceContainerLow, // M3 color for light theme
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(24.0),
                      bottomRight: Radius.circular(24.0),
                    ),
                    boxShadow: isDarkTheme ? [] : [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withOpacity(0.15),
                        blurRadius: 12.0,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildMenuAction(
                        icon: Icons.add_circle_outline_rounded,
                        tooltip: 'Add Time Entry',
                        color: Colors.green.shade600,
                        context: context,
                        onPressed: () => onAddTimeEntry(project),
                      ),
                      _buildMenuAction(
                        icon: Icons.location_on_outlined,
                        tooltip: 'Location Tracking',
                        color: Colors.deepPurple.shade400,
                        context: context,
                        onPressed: () => onLocationConfig(project),
                      ),
                      _buildMenuAction(
                        icon: Icons.calendar_view_week_outlined,
                        tooltip: 'View All Weeks',
                        color: Colors.amber.shade700,
                        context: context,
                        onPressed: () => showAllWeeksDialog(context, entries, projectColor),
                      ),
                      _buildMenuAction(
                        icon: Icons.edit_rounded,
                        tooltip: 'Edit Project',
                        color: Colors.blueAccent.shade400,
                        context: context,
                        onPressed: () => onEdit(project),
                      ),
                      _buildMenuAction(
                        icon: Icons.delete_outline_rounded,
                        tooltip: 'Delete Project',
                        color: Colors.red.shade400,
                        context: context,
                        onPressed: () => onDelete(project),
                      ),
                    ],
                  ),
                )
              : SizedBox.shrink(key: const ValueKey('mobile_menu_closed')),
        );
      },
    );
  }

  // Helper to build menu actions, ensuring menu closes on tap
  Widget _buildMenuAction({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required Color color,
    required VoidCallback onPressed
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: ProjectCardDropdownAction(
        icon: icon,
        tooltip: tooltip,
        color: color,
        onPressed: () {
          onPressed();
          isSettingsMenuOpen.value = false; // Close menu after action
        },
      ),
    );
  }
}

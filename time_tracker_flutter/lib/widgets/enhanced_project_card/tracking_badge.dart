import 'dart:async';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/services/location/location_events.dart';

class TrackingBadge extends StatefulWidget {
  final String projectId;
  final Color projectColor;
  final bool isDark;

  const TrackingBadge({
    super.key,
    required this.projectId,
    required this.projectColor,
    required this.isDark,
  });

  @override
  State<TrackingBadge> createState() => _TrackingBadgeState();
}

class _TrackingBadgeState extends State<TrackingBadge> with TickerProviderStateMixin {
  final LocationService _locationService = LocationService();

  // Event-based subscriptions
  StreamSubscription<LocationEvent>? _eventSubscription;
  StreamSubscription<TrackingStatus>? _statusSubscription;

  // State
  bool _isTracking = false;

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _checkInitialState();
    _subscribeToLocationEvents();
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _statusSubscription?.cancel();
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _initializeAnimation() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  /// Check the initial tracking state for this project
  Future<void> _checkInitialState() async {
    try {
      final isTracking = await _locationService.isProjectBeingTracked(widget.projectId);

      if (isTracking && mounted) {
        setState(() {
          _isTracking = true;
        });
        _showBadge();
      }
    } catch (e) {
      debugPrint('Error checking initial tracking state: $e');
    }
  }

  /// Subscribe to real-time location events and status changes
  void _subscribeToLocationEvents() {
    _eventSubscription = _locationService.eventStream
        .where((event) => event.projectId == widget.projectId)
        .listen(_handleLocationEvent);

    _statusSubscription = _locationService.statusStream.listen(_handleStatusUpdate);
  }

  /// Handle location events specific to this project
  void _handleLocationEvent(LocationEvent event) {
    if (!mounted) return;

    switch (event.type) {
      case LocationEventType.areaEntered:
        setState(() {
          _isTracking = true;
        });
        _showBadge();
        break;

      case LocationEventType.areaExited:
        _hideBadge();
        break;

      case LocationEventType.timeEntryCreated:
        if (!_isTracking) {
          setState(() {
            _isTracking = true;
          });
          _checkInitialState();
        }
        break;

      case LocationEventType.timeEntryResumed:
        setState(() {
          _isTracking = true;
        });
        _checkInitialState();
        break;

      case LocationEventType.timeEntryUpdated:
        _checkIfTrackingEnded();
        break;

      default:
        break;
    }
  }

  /// Handle overall tracking status changes
  void _handleStatusUpdate(TrackingStatus status) {
    if (!mounted) return;

    // Check if this specific project is still being tracked
    _checkIfProjectIsActive(status);
  }

  /// Check if this specific project is active in the current tracking status
  Future<void> _checkIfProjectIsActive(TrackingStatus status) async {
    try {
      final isTracking = await _locationService.isProjectBeingTracked(widget.projectId);

      if (isTracking && !_isTracking && mounted) {
        // Project is being tracked but we weren't showing it
        setState(() {
          _isTracking = true;
        });
        _showBadge();
      } else if (!isTracking && _isTracking && mounted) {
        // Project is no longer being tracked but we were showing it
        _hideBadge();
      }
    } catch (e) {
      debugPrint('Error checking if project is active: $e');
    }
  }

  /// Check if tracking has ended for this project
  Future<void> _checkIfTrackingEnded() async {
    if (!_isTracking) return;

    try {
      final isTracking = await _locationService.isProjectBeingTracked(widget.projectId);

      if (!isTracking && mounted) {
        _hideBadge();
      }
    } catch (e) {
      debugPrint('Error checking if tracking ended: $e');
    }
  }

  void _showBadge() {
    _fadeController.forward();
  }

  void _hideBadge() {
    _fadeController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isTracking = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isTracking) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_fadeAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _pulseAnimation.value,
            child: Container(
              width: 14.0,
              height: 14.0,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.green,
                border: Border.all(
                  color: widget.isDark ? Colors.grey[800]! : Colors.white,
                  width: 2.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.4),
                    blurRadius: 6.0,
                    spreadRadius: 1.5,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

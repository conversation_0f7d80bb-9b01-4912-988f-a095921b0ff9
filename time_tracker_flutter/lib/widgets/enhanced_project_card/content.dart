import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/widgets/project_card_error.dart';
import 'package:time_tracker_flutter/widgets/project_card_notifiers.dart';
import 'package:time_tracker_flutter/widgets/project_card_week.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

class EnhancedProjectCardContent extends StatefulWidget {
  final Project project;
  final bool isLoadingEntries;
  final bool hasErrorLoadingEntries;
  final List<TimeEntry> entries;
  final Color projectColor;
  final String? error;
  final Function(Project) onAddTimeEntry;
  final Function() refreshTimeEntries;
  final DatabaseService databaseService;
  final ProjectCardNotifiers notifiers;

  const EnhancedProjectCardContent({
    super.key,
    required this.project,
    required this.isLoadingEntries,
    required this.hasErrorLoadingEntries,
    required this.entries,
    required this.projectColor,
    this.error,
    required this.onAddTimeEntry,
    required this.refreshTimeEntries,
    required this.databaseService,
    required this.notifiers,
  });

  @override
  State<EnhancedProjectCardContent> createState() => _EnhancedProjectCardContentState();
}

class _EnhancedProjectCardContentState extends State<EnhancedProjectCardContent> {
  Map<String, bool> _weekExpansionState = {};

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bool isDark = theme.brightness == Brightness.dark;

    return ValueListenableBuilder<bool>(
      valueListenable: widget.notifiers.isCollapsed,
      builder: (context, isContentCollapsed, child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: SizeTransition(
                sizeFactor: CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
                axisAlignment: -1.0,
                child: child,
              ),
            );
          },
          child: isContentCollapsed
              ? SizedBox.shrink(key: const ValueKey('content_collapsed'))
              : Container(
                  key: const ValueKey('content_expanded'),
                  decoration: BoxDecoration(
                    color: isDark
                        ? theme.colorScheme.surfaceContainer
                        : theme.colorScheme.surfaceContainerLowest,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(24.0),
                      bottomRight: Radius.circular(24.0),
                    ),
                    boxShadow: isDark ? [] : [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withOpacity(0.1),
                        blurRadius: 8.0,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.fromLTRB(20.0, 12.0, 20.0, 20.0),
                  child: _buildExpandedContent(context),
                ),
        );
      },
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    final theme = Theme.of(context);
    final double loadingIndicatorSize = 24.0;
    final double errorPadding = 16.0;
    final double emptyStateVerticalPadding = 32.0;
    final double emptyStateIconSize = 40.0;
    final double emptyStateTextFontSize = 15.0;
    final double weekListTopPadding = 8.0;

    if (widget.isLoadingEntries) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(loadingIndicatorSize),
          child: SizedBox(
            width: loadingIndicatorSize,
            height: loadingIndicatorSize,
            child: CircularProgressIndicator(strokeWidth: 2.5, color: widget.projectColor)
          ),
        ),
      );
    }

    if (widget.hasErrorLoadingEntries) {
      return Padding(
        padding: EdgeInsets.all(errorPadding),
        child: ProjectCardError(errorMessage: 'Error loading entries: ${widget.error}'),
      );
    }

    if (widget.entries.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: emptyStateVerticalPadding, horizontal: 8.0),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.project.iconData ?? Icons.history_toggle_off_rounded,
                size: emptyStateIconSize,
                color: widget.projectColor.withOpacity(0.7),
              ),
              SizedBox(height: 12.0),
              Text(
                'No time entries yet',
                style: TextStyle(
                  fontSize: emptyStateTextFontSize,
                  color: theme.colorScheme.onSurfaceVariant
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.0),
              ElevatedButton.icon(
                onPressed: () => widget.onAddTimeEntry(widget.project),
                icon: Icon(Icons.add_rounded, size: 20.0),
                label: Text('Add First Entry', style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.projectColor,
                  foregroundColor: theme.colorScheme.onPrimaryContainer.withAlpha(230),
                  padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  elevation: 1.0,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final weeklyEntries = groupEntriesByWeek(widget.entries);
    final weekKeys = weeklyEntries.keys.toList();
    final String currentWeekKey = getCurrentWeekKey();

    for (final key in weekKeys) {
      if (!_weekExpansionState.containsKey(key)) {
        final isCurrent = key == currentWeekKey;
        _weekExpansionState[key] = widget.notifiers.showAllWeeks.value || isCurrent;
      }
    }

    weekKeys.sort((a, b) {
      try {
        final dateA = DateTime.parse(a.split(' - ')[0]);
        final dateB = DateTime.parse(b.split(' - ')[0]);
        return dateB.compareTo(dateA);
      } catch (e) {
        return 0;
      }
    });

    List<String> weeksToShow = weekKeys;

    if (!widget.notifiers.showAllWeeks.value && weekKeys.length > widget.project.maxWeeksInOverview) {
      weeksToShow = weekKeys.take(widget.project.maxWeeksInOverview).toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (weeksToShow.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(top: weekListTopPadding),
            child: ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: weeksToShow.length,
              itemBuilder: (context, index) {
                final weekKey = weeksToShow[index];
                final weekData = weeklyEntries[weekKey]!;
                final isCurrentWeek = weekKey == currentWeekKey;
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  switchInCurve: Curves.easeOutCubic,
                  switchOutCurve: Curves.easeInCubic,
                  child: Padding(
                    key: ValueKey(weekKey),
                    padding: EdgeInsets.only(bottom: 8.0),
                    child: ProjectCardWeek(
                      weekKey: weekKey,
                      totalTime: weekData['totalTime'] as String,
                      entries: weekData['entries'] as List<TimeEntry>,
                      isCurrentWeek: isCurrentWeek,
                      minimumWeeklyHours: widget.project.minimumWeeklyHours,
                      projectColor: widget.projectColor,
                      weekExpansionState: _weekExpansionState,
                      onToggle: (key) {
                        setState(() {
                          _weekExpansionState[key] = !_weekExpansionState[key]!;
                        });
                      },
                      onEdit: (entry) {
                        showDialog(
                          context: context,
                          builder: (context) => AddTimeEntryDialog(
                            projectId: entry.projectId,
                            initialEntry: entry,
                            onTimeEntryAdded: (updatedEntry) async {
                              await widget.refreshTimeEntries();
                            },
                          ),
                        );
                      },
                      onDelete: _deleteTimeEntry,
                      onDeleteWeek: _deleteWeekEntries,
                      databaseService: widget.databaseService,
                      refreshTimeEntries: widget.refreshTimeEntries,
                    ),
                  ),
                );
              }
            ),
          ),
        Padding(
          padding: EdgeInsets.only(bottom: 4.0),
          child: ElevatedButton.icon(
            onPressed: () => widget.onAddTimeEntry(widget.project),
            icon: Icon(Icons.add_task_rounded, size: 18.0),
            label: Text('Add New Entry', style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500)),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.projectColor.withOpacity(0.9),
              foregroundColor: theme.colorScheme.onPrimaryContainer.withAlpha(240),
              padding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.0),
              ),
              elevation: 1.0,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _deleteTimeEntry(TimeEntry entry) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Time Entry',
        content: 'Are you sure you want to delete this time entry?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      try {
        await widget.databaseService.deleteTimeEntry(entry.id);
        await widget.refreshTimeEntries();
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error deleting time entry: ${e.toString()}', context);
        }
      }
    }
  }

  Future<void> _deleteWeekEntries(List<TimeEntry> weekEntries) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Week Entries',
        content: 'Are you sure you want to delete all ${weekEntries.length} entries for this week?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      try {
        for (final entry in weekEntries) {
          await widget.databaseService.deleteTimeEntry(entry.id);
        }
        await widget.refreshTimeEntries();
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error deleting week entries: ${e.toString()}', context);
        }
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/widgets/responsive_dialog.dart';

class AddTimeEntryDialog extends StatefulWidget {
  final String projectId;
  final TimeEntry? initialEntry;
  final Function(TimeEntry) onTimeEntryAdded;

  const AddTimeEntryDialog({
    super.key,
    required this.projectId,
    this.initialEntry,
    required this.onTimeEntryAdded,
  });

  @override
  State<AddTimeEntryDialog> createState() => _AddTimeEntryDialogState();
}

class _AddTimeEntryDialogState extends State<AddTimeEntryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();
  final _durationController = TextEditingController();

  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = false;
  bool _showTimeRange = true;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();

    // Load saved preference for entry type
    _showTimeRange = _getSavedEntryTypePreference();

    // Initialize with initial entry if provided
    if (widget.initialEntry != null) {
      final entry = widget.initialEntry!;

      // Set date
      _selectedDate = DateTime.parse(entry.date);

      // Set time values
      if (entry.isTimeRange) {
        _showTimeRange = true;
        _startTimeController.text = entry.start!;

        // If this is an active entry (start == end), update end time to current time
        if (entry.isActive) {
          _endTimeController.text = LocaleDateUtils.getTimeFormat().format(DateTime.now());
        } else {
          _endTimeController.text = entry.end!;
        }
      } else {
        _showTimeRange = false;
        _durationController.text = entry.duration!;
      }
    }
  }

  bool _getSavedEntryTypePreference() {
    // This would normally be loaded from shared preferences or Hive
    // For now, we'll default to time range
    return true;
  }

  void _saveEntryTypePreference(bool showTimeRange) {
    // This would normally save to shared preferences or Hive
  }

  @override
  void dispose() {
    _startTimeController.dispose();
    _endTimeController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      setState(() {
        controller.text = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  String _formatTimeInputValue(String value) {
    if (RegExp(r'^\d{4}$').hasMatch(value)) {
      return '${value.substring(0, 2)}:${value.substring(2)}';
    }
    return value;
  }

  Future<void> _saveTimeEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      TimeEntry entry;

      if (_showTimeRange) {
        entry = TimeEntry(
          id: widget.initialEntry?.id,
          projectId: widget.projectId,
          date: DateFormat('yyyy-MM-dd').format(_selectedDate),
          start: _startTimeController.text,
          end: _endTimeController.text,
        );
      } else {
        entry = TimeEntry(
          id: widget.initialEntry?.id,
          projectId: widget.projectId,
          date: DateFormat('yyyy-MM-dd').format(_selectedDate),
          duration: _durationController.text,
        );
      }

      // Save the entry
      await _databaseService.saveTimeEntry(entry);

      // Notify the parent
      widget.onTimeEntryAdded(entry);

      // Close the dialog
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error saving time entry: ${e.toString()}', context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveDialog(
      title: Text(widget.initialEntry == null ? 'Add Time Entry' : 'Edit Time Entry'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Entry type toggle
              SegmentedButton<bool>(
                segments: const [
                  ButtonSegment<bool>(
                    value: false,
                    label: Text('Duration'),
                    icon: Icon(Icons.timer),
                  ),
                  ButtonSegment<bool>(
                    value: true,
                    label: Text('Time Range'),
                    icon: Icon(Icons.access_time),
                  ),
                ],
                selected: {_showTimeRange},
                onSelectionChanged: (Set<bool> newSelection) {
                  setState(() {
                    _showTimeRange = newSelection.first;
                    _saveEntryTypePreference(_showTimeRange);
                  });
                },
              ),
              const SizedBox(height: 16),

              // Date picker
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: FutureBuilder<String>(
                    future: LocaleDateUtils.formatDateWithCustomAndContext(_selectedDate, context),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Text(formatDateWithContext(_selectedDate, context)); // Fallback while loading
                      } else if (snapshot.hasError) {
                        return Text(formatDateWithContext(_selectedDate, context)); // Fallback on error
                      } else {
                        return Text(snapshot.data!);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Time inputs
              if (_showTimeRange) ...[
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _startTimeController,
                        decoration: InputDecoration(
                          labelText: 'Start Time',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.access_time),
                            onPressed: () => _selectTime(_startTimeController),
                          ),
                        ),
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Required';
                          }
                          if (!RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$').hasMatch(value)) {
                            return 'Invalid format';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          final formatted = _formatTimeInputValue(value);
                          if (formatted != value) {
                            _startTimeController.text = formatted;
                            _startTimeController.selection = TextSelection.fromPosition(
                              TextPosition(offset: formatted.length),
                            );
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextFormField(
                        controller: _endTimeController,
                        decoration: InputDecoration(
                          labelText: 'End Time',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.access_time),
                            onPressed: () => _selectTime(_endTimeController),
                          ),
                        ),
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Required';
                          }
                          if (!RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$').hasMatch(value)) {
                            return 'Invalid format';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          final formatted = _formatTimeInputValue(value);
                          if (formatted != value) {
                            _endTimeController.text = formatted;
                            _endTimeController.selection = TextSelection.fromPosition(
                              TextPosition(offset: formatted.length),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ] else ...[
                TextFormField(
                  controller: _durationController,
                  decoration: const InputDecoration(
                    labelText: 'Duration (HH:MM)',
                    border: OutlineInputBorder(),
                    hintText: '00:00',
                  ),
                  keyboardType: TextInputType.text,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a duration';
                    }
                    if (!RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$').hasMatch(value)) {
                      return 'Invalid format (use HH:MM)';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    final formatted = _formatTimeInputValue(value);
                    if (formatted != value) {
                      _durationController.text = formatted;
                      _durationController.selection = TextSelection.fromPosition(
                        TextPosition(offset: formatted.length),
                      );
                    }
                  },
                ),
              ],
            ],
          ),
        ),
      ),
      scrollable: true,
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveTimeEntry,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save'),
        ),
      ],
    );
  }
}

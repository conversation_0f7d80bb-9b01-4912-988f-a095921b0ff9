import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Demo widget to showcase locale-aware date formatting
class LocaleDemoWidget extends StatelessWidget {
  const LocaleDemoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final currentLocale = LocaleDateUtils.getCurrentLocale(context);
    
    // Sample locales to demonstrate
    final locales = [
      const Locale('en', 'US'), // English (US)
      const Locale('en', 'GB'), // English (UK)
      const Locale('de', 'DE'), // German
      const Locale('fr', 'FR'), // French
      const Locale('es', 'ES'), // Spanish
      const Locale('ja', 'JP'), // Japanese
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Locale-Aware Date Formatting Demo'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current System Locale: ${currentLocale.toString()}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'Current Date/Time Formatted:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Date: ${LocaleDateUtils.formatDate(now, currentLocale)}'),
                    Text('Short Date: ${LocaleDateUtils.formatShortDate(now, currentLocale)}'),
                    Text('Time: ${LocaleDateUtils.formatTime(now, currentLocale)}'),
                    Text('Weekday: ${LocaleDateUtils.formatWeekday(now, currentLocale)}'),
                    Text('Month: ${LocaleDateUtils.formatMonth(now, currentLocale)}'),
                    Text('Month-Year: ${LocaleDateUtils.formatMonthYear(now, currentLocale)}'),
                    Text('Date & Time: ${LocaleDateUtils.formatDateTime(now, currentLocale)}'),
                    Text('Relative: ${LocaleDateUtils.formatRelativeDate(now, currentLocale)}'),
                    Text('Compact: ${LocaleDateUtils.formatCompactDate(now, currentLocale)}'),
                    Text('Ordinal: ${LocaleDateUtils.formatOrdinalDate(now, currentLocale)}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Different Locale Examples:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: locales.length,
                itemBuilder: (context, index) {
                  final locale = locales[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${locale.languageCode.toUpperCase()} (${locale.countryCode})',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text('Date: ${LocaleDateUtils.formatDate(now, locale)}'),
                          Text('Short: ${LocaleDateUtils.formatShortDate(now, locale)}'),
                          Text('Time: ${LocaleDateUtils.formatTime(now, locale)}'),
                          Text('Weekday: ${LocaleDateUtils.formatWeekday(now, locale)}'),
                          Text('24h Time: ${LocaleDateUtils.uses24HourTime(locale) ? "Yes" : "No"}'),
                          Text('Relative: ${LocaleDateUtils.formatRelativeDate(now, locale)}'),
                          Text('Compact: ${LocaleDateUtils.formatCompactDate(now, locale)}'),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
} 
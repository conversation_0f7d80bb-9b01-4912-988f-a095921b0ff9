import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateFormatPickerDialog extends StatefulWidget {
  final String? initialFormat;

  const DateFormatPickerDialog({super.key, this.initialFormat});

  @override
  State<DateFormatPickerDialog> createState() => _DateFormatPickerDialogState();
}

class _DateFormatPickerDialogState extends State<DateFormatPickerDialog> {
  late TextEditingController _customFormatController;
  String? _selectedPresetFormat;
  String _previewText = '';
  final DateTime _sampleDate = DateTime(2024, 3, 15, 14, 30);

  // Common date format presets
  final List<Map<String, String>> _presetFormats = [
    {'name': 'US Format (MM/dd/yyyy)', 'format': 'MM/dd/yyyy'},
    {'name': 'European Format (dd/MM/yyyy)', 'format': 'dd/MM/yyyy'},
    {'name': 'ISO Format (yyyy-MM-dd)', 'format': 'yyyy-MM-dd'},
    {'name': 'Long Format (MMMM d, yyyy)', 'format': 'MMMM d, yyyy'},
    {'name': 'Medium Format (MMM d, yyyy)', 'format': 'MMM d, yyyy'},
    {'name': 'Short Format (M/d/yy)', 'format': 'M/d/yy'},
    {'name': 'Day First (d MMM yyyy)', 'format': 'd MMM yyyy'},
    {'name': 'Weekday Format (EEEE, MMMM d, yyyy)', 'format': 'EEEE, MMMM d, yyyy'},
    {'name': 'Compact Format (yyyyMMdd)', 'format': 'yyyyMMdd'},
    {'name': 'Dot Separated (dd.MM.yyyy)', 'format': 'dd.MM.yyyy'},
  ];

  @override
  void initState() {
    super.initState();
    _customFormatController = TextEditingController(text: widget.initialFormat ?? '');
    
    // Check if initial format matches any preset
    if (widget.initialFormat != null) {
      for (final preset in _presetFormats) {
        if (preset['format'] == widget.initialFormat) {
          _selectedPresetFormat = preset['format'];
          break;
        }
      }
    }
    
    _updatePreview();
  }

  @override
  void dispose() {
    _customFormatController.dispose();
    super.dispose();
  }

  void _updatePreview() {
    final format = _customFormatController.text.trim();
    if (format.isEmpty) {
      setState(() {
        _previewText = 'Enter a format pattern';
      });
      return;
    }

    try {
      final dateFormat = DateFormat(format);
      setState(() {
        _previewText = dateFormat.format(_sampleDate);
      });
    } catch (e) {
      setState(() {
        _previewText = 'Invalid format pattern';
      });
    }
  }

  void _selectPresetFormat(String format) {
    setState(() {
      _selectedPresetFormat = format;
      _customFormatController.text = format;
    });
    _updatePreview();
  }

  void _showFormatHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Date Format Patterns'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: const [
              Text('Common patterns:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('y - Year (2024)'),
              Text('yy - Year (24)'),
              Text('M - Month (3)'),
              Text('MM - Month (03)'),
              Text('MMM - Month (Mar)'),
              Text('MMMM - Month (March)'),
              Text('d - Day (5)'),
              Text('dd - Day (05)'),
              Text('E - Weekday (Fri)'),
              Text('EEEE - Weekday (Friday)'),
              SizedBox(height: 12),
              Text('Examples:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('MM/dd/yyyy → 03/15/2024'),
              Text('dd/MM/yyyy → 15/03/2024'),
              Text('yyyy-MM-dd → 2024-03-15'),
              Text('MMMM d, yyyy → March 15, 2024'),
              Text('E, MMM d → Fri, Mar 15'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Custom Date Format'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Choose a preset format:'),
            const SizedBox(height: 8),
            SizedBox(
              height: 200,
              child: ListView.builder(
                itemCount: _presetFormats.length,
                itemBuilder: (context, index) {
                  final preset = _presetFormats[index];
                  final isSelected = _selectedPresetFormat == preset['format'];
                  
                  return ListTile(
                    title: Text(preset['name']!),
                    subtitle: Text(preset['format']!),
                    selected: isSelected,
                    onTap: () => _selectPresetFormat(preset['format']!),
                    trailing: isSelected ? const Icon(Icons.check) : null,
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            const Text('Or enter a custom format:'),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _customFormatController,
                    decoration: const InputDecoration(
                      hintText: 'e.g., MM/dd/yyyy',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (_) {
                      setState(() {
                        _selectedPresetFormat = null;
                      });
                      _updatePreview();
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _showFormatHelp,
                  icon: const Icon(Icons.help_outline),
                  tooltip: 'Format Help',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Preview:',
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _previewText,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _customFormatController.text.trim().isEmpty || _previewText == 'Invalid format pattern'
              ? null
              : () => Navigator.of(context).pop(_customFormatController.text.trim()),
          child: const Text('Apply'),
        ),
      ],
    );
  }
} 
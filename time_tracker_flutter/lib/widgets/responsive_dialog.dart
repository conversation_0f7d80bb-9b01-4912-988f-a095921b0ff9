import 'package:flutter/material.dart';

/// A responsive dialog that optimizes screen space usage
/// on different device sizes, particularly on mobile.
class ResponsiveDialog extends StatelessWidget {
  final Widget title;
  final Widget content;
  final List<Widget> actions;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? actionsPadding;
  final bool scrollable;
  final double maxWidth;

  const ResponsiveDialog({
    super.key,
    required this.title,
    required this.content,
    required this.actions,
    this.contentPadding,
    this.actionsPadding,
    this.maxWidth = 444,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    // Use full width of screen with minimal side margins for small screens
    final dialogWidth = isSmallScreen ? size.width * 0.95 : null;

    return Dialog(
      // Reduce inset padding to utilize more horizontal space
      insetPadding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 10 : 40,
        vertical: 24,
      ),
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxWidth: maxWidth,
          maxHeight: size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 8),
              child: DefaultTextStyle(
                style: Theme.of(context).textTheme.titleLarge ?? const TextStyle(),
                child: title,
              ),
            ),
            if (scrollable)
              Flexible(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: size.height * 0.6,
                  ),
                  child: SingleChildScrollView(
                    padding: contentPadding ?? const EdgeInsets.fromLTRB(24, 8, 24, 16),
                    child: content,
                  ),
                ),
              )
            else
              Padding(
                padding: contentPadding ?? const EdgeInsets.fromLTRB(24, 8, 24, 16),
                child: content,
              ),
            Padding(
              padding: actionsPadding ?? const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
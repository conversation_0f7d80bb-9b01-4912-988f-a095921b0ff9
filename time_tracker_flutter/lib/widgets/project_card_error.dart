import 'package:flutter/material.dart';
// import 'package:time_tracker_flutter/utils/responsive_utils.dart';

class ProjectCardError extends StatelessWidget {
  final String errorMessage;
  const ProjectCardError({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.red.shade50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.red.shade200, width: 1.0),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade700, size: 24.0),
            SizedBox(width: 12.0),
            Expanded(
              child: Text(
                errorMessage,
                style: TextStyle(color: Colors.red.shade700, fontSize: 14.0),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

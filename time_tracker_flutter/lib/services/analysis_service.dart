import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/date_format_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Helper class for aggregating time series data
class _AggregationData {
  final DateTime date;
  final double hours;
  final String? projectId;
  final DateTime rangeStart;
  final DateTime rangeEnd;
  final List<TimeSeriesData> dataPoints;

  _AggregationData({
    required this.date,
    required this.hours,
    this.projectId,
    required this.rangeStart,
    required this.rangeEnd,
    required this.dataPoints,
  });
}

class AnalysisService {
  final DatabaseService _databaseService = DatabaseService();

  // Helper method to convert time string to minutes
  int _timeToMinutes(String time) {
    if (time.isEmpty) return 0;
    if (time.contains('-')) {
      try {
        final parts = time.split('-');
        final start = parts[0].trim();
        final end = parts[1].trim();

        final startParts = start.split(':').map(int.parse).toList();
        final endParts = end.split(':').map(int.parse).toList();

        final startMinutes = startParts[0] * 60 + startParts[1];
        final endMinutes = endParts[0] * 60 + endParts[1];

        return endMinutes - startMinutes;
      } catch (e) {
        debugPrint("Error parsing time range $time: $e");
        return 0;
      }
    } else {
      try {
        final parts = time.split(':').map(int.parse).toList();
        return parts[0] * 60 + parts[1];
      } catch (e) {
        debugPrint("Error parsing duration $time: $e");
        return 0;
      }
    }
  }

  // Convert minutes to hours as a double
  double _minutesToHours(int minutes) {
    return minutes / 60.0;
  }

  // Get entry duration in minutes
  int _getEntryDurationMinutes(TimeEntry entry) {
    if (entry.start != null && entry.end != null) {
      return _timeToMinutes('${entry.start}-${entry.end}');
    } else if (entry.duration != null) {
      return _timeToMinutes(entry.duration!);
    }
    return 0;
  }

  // Get locale-aware weekday name from date
  String _getWeekdayName(DateTime date, [Locale? locale]) {
    return LocaleDateUtils.getFullWeekdayFormat(locale).format(date);
  }

  // Generate analysis for a given date range and optional project filter
  Future<AnalysisResult> generateAnalysis({
    required DateTimeRange dateRange,
    String? projectId,
    AnalysisGroupBy groupBy = AnalysisGroupBy.weekday,
  }) async {
    await _databaseService.initialize();

    // Get all projects and time entries within the date range
    final List<Project> allProjects = await _databaseService.getProjects();
    final List<TimeEntry> allEntries = await _databaseService.getTimeEntries(
      projectId: projectId,
      startDate: dateRange.start,
      endDate: dateRange.end,
    );

    if (allEntries.isEmpty) {
      // Return empty analysis result if no entries found
      return AnalysisResult(
        totalHours: 0,
        totalEntries: 0,
        averageHoursPerEntry: 0,
        dateRange: dateRange,
        projectAnalysis: [],
        weekdayAnalysis: [],
        timeSeriesData: [],
      );
    }

    // Create a map for quick project lookup
    final Map<String, Project> projectMap = {for (var p in allProjects) p.id: p};

    // Calculate total hours and entries
    int totalMinutes = 0;
    for (final entry in allEntries) {
      totalMinutes += _getEntryDurationMinutes(entry);
    }

    final double totalHours = _minutesToHours(totalMinutes);
    final int totalEntries = allEntries.length;
    final double averageHoursPerEntry = totalEntries > 0 ? totalHours / totalEntries : 0;

    // Group entries by project
    final Map<String, List<TimeEntry>> entriesByProject = {};
    for (final entry in allEntries) {
      if (!entriesByProject.containsKey(entry.projectId)) {
        entriesByProject[entry.projectId] = [];
      }
      entriesByProject[entry.projectId]!.add(entry);
    }

    // Generate project analysis
    final List<ProjectAnalysisData> projectAnalysis = [];
    for (final projectId in entriesByProject.keys) {
      final project = projectMap[projectId];
      if (project != null) {
        final entries = entriesByProject[projectId]!;
        int projectMinutes = 0;
        for (final entry in entries) {
          projectMinutes += _getEntryDurationMinutes(entry);
        }

        final double projectHours = _minutesToHours(projectMinutes);
        final double percentageOfTotal = totalHours > 0 ? (projectHours / totalHours) * 100 : 0;
        final double avgHoursPerEntry = entries.isNotEmpty ? projectHours / entries.length : 0;

        projectAnalysis.add(ProjectAnalysisData(
          project: project,
          totalHours: projectHours,
          averageHoursPerEntry: avgHoursPerEntry,
          percentageOfTotal: percentageOfTotal,
          entryCount: entries.length,
        ));
      }
    }

    // Sort project analysis by total hours (descending)
    projectAnalysis.sort((a, b) => b.totalHours.compareTo(a.totalHours));

    // Group entries by weekday
    final Map<int, List<TimeEntry>> entriesByWeekday = {};
    for (final entry in allEntries) {
      try {
        final date = DateTime.parse(entry.date);
        final weekday = date.weekday; // 1-7 where 1 is Monday

        if (!entriesByWeekday.containsKey(weekday)) {
          entriesByWeekday[weekday] = [];
        }
        entriesByWeekday[weekday]!.add(entry);
      } catch (e) {
        debugPrint("Error parsing date: ${entry.date}");
      }
    }

    // Generate weekday analysis
    final List<WeekdayAnalysisData> weekdayAnalysis = [];
    for (int weekday = 1; weekday <= 7; weekday++) {
      final entries = entriesByWeekday[weekday] ?? [];
      int weekdayMinutes = 0;
      for (final entry in entries) {
        weekdayMinutes += _getEntryDurationMinutes(entry);
      }

      final double weekdayHours = _minutesToHours(weekdayMinutes);
      final double percentageOfTotal = totalHours > 0 ? (weekdayHours / totalHours) * 100 : 0;

      weekdayAnalysis.add(WeekdayAnalysisData(
        weekday: _getWeekdayName(DateTime(2023, 1, 2 + weekday - 1)), // Jan 2, 2023 was a Monday
        totalHours: weekdayHours,
        percentageOfTotal: percentageOfTotal,
        entryCount: entries.length,
      ));
    }

    // Generate time series data (for trends over time)
    final Map<String, double> hoursByDate = {};
    final Map<String, String?> projectByDate = {};

    for (final entry in allEntries) {
      try {
        final date = entry.date;
        final entryHours = _minutesToHours(_getEntryDurationMinutes(entry));

        hoursByDate[date] = (hoursByDate[date] ?? 0) + entryHours;
        // For single project analysis, store the project ID
        if (projectId != null) {
          projectByDate[date] = projectId;
        }
      } catch (e) {
        debugPrint("Error processing entry for time series: $e");
      }
    }

    final List<TimeSeriesData> timeSeriesData = hoursByDate.entries.map((entry) {
      return TimeSeriesData(
        date: DateTime.parse(entry.key),
        hours: entry.value,
        projectId: projectByDate[entry.key],
      );
    }).toList();

    // Sort time series data by date
    timeSeriesData.sort((a, b) => a.date.compareTo(b.date));

    // Find min and max hours for a day (outlier detection)
    double? minHours;
    double? maxHours;
    TimeSeriesData? mostProductiveDay;

    if (timeSeriesData.isNotEmpty) {
      minHours = timeSeriesData.map((e) => e.hours).reduce((a, b) => a < b ? a : b);
      maxHours = timeSeriesData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);
      mostProductiveDay = timeSeriesData.firstWhere((e) => e.hours == maxHours);
    }

    return AnalysisResult(
      totalHours: totalHours,
      totalEntries: totalEntries,
      averageHoursPerEntry: averageHoursPerEntry,
      dateRange: dateRange,
      projectAnalysis: projectAnalysis,
      weekdayAnalysis: weekdayAnalysis,
      timeSeriesData: timeSeriesData,
      minHours: minHours,
      maxHours: maxHours,
      mostProductiveDay: mostProductiveDay,
    );
  }

  // Get distribution data for pie charts
  List<TimeDistributionData> getWeekdayDistribution(AnalysisResult analysis) {
    final weekdayColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.amber,
    ];

    return analysis.weekdayAnalysis.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      return TimeDistributionData(
        label: data.weekday,
        hours: data.totalHours,
        percentage: data.percentageOfTotal,
        color: weekdayColors[index % weekdayColors.length],
      );
    }).toList();
  }

  List<TimeDistributionData> getProjectDistribution(AnalysisResult analysis) {
    return analysis.projectAnalysis.map((data) {
      return TimeDistributionData(
        label: data.project.name,
        hours: data.totalHours,
        percentage: data.percentageOfTotal,
        color: data.project.color,
      );
    }).toList();
  }

  // Helper method to format hours for display
  String formatHours(double hours) {
    final int wholeHours = hours.floor();
    final int minutes = ((hours - wholeHours) * 60).round();
    return '$wholeHours:${minutes.toString().padLeft(2, '0')}';
  }

  // Aggregate time series data to prevent overcrowding in charts
  List<TimeSeriesData> aggregateTimeSeriesData(
    List<TimeSeriesData> data,
    DateTimeRange dateRange,
    {int? maxPoints}
  ) {
    // If we have a small dataset, return as is
    if (data.length <= 20) {
      return data;
    }

    // Determine appropriate aggregation level based on date range
    final aggregationLevel = DateFormatUtils.determineAggregationLevel(dateRange);

    switch (aggregationLevel) {
      case DataAggregationLevel.quarter:
        if (maxPoints != null && data.length > maxPoints) {
          return _aggregateByMonth(data);
        }
        return _aggregateByQuarter(data);
      case DataAggregationLevel.month:
        return _aggregateByMonth(data);
      case DataAggregationLevel.week:
        if (maxPoints != null && data.length > maxPoints) {
          return _aggregateByDayGroups(data, (data.length / maxPoints).ceil());
        }
        return _aggregateByWeek(data);
      case DataAggregationLevel.twoDays:
        return _aggregateByDayGroups(data, 2);
      case DataAggregationLevel.day:
      default:
        return data;
    }
  }

  List<TimeSeriesData> _aggregateByQuarter(List<TimeSeriesData> data) {
    final Map<String, _AggregationData> quarterlyData = {};

    for (final entry in data) {
      final date = entry.date;
      final quarter = ((date.month - 1) ~/ 3) + 1;
      final quarterKey = '${date.year}-Q$quarter';

      // First day of the quarter
      final quarterDate = DateTime(date.year, (quarter - 1) * 3 + 1, 1);

      // Calculate quarter range
      final quarterStart = DateTime(date.year, (quarter - 1) * 3 + 1, 1);
      final quarterEnd = DateTime(
        date.year + (quarter == 4 ? 1 : 0),
        quarter == 4 ? 1 : quarter * 3 + 1,
        0
      ); // Last day of the quarter

      if (!quarterlyData.containsKey(quarterKey)) {
        // Create a new entry for this quarter
        quarterlyData[quarterKey] = _AggregationData(
          date: quarterDate,
          hours: entry.hours,
          projectId: entry.projectId,
          rangeStart: quarterStart,
          rangeEnd: quarterEnd,
          dataPoints: [entry],
        );
      } else {
        // Add hours to existing quarter
        final existing = quarterlyData[quarterKey]!;
        quarterlyData[quarterKey] = _AggregationData(
          date: existing.date,
          hours: existing.hours + entry.hours,
          projectId: entry.projectId,
          rangeStart: existing.rangeStart,
          rangeEnd: existing.rangeEnd,
          dataPoints: [...existing.dataPoints, entry],
        );
      }
    }

    // Convert to TimeSeriesData with aggregation info
    final result = quarterlyData.entries.map((entry) {
      final aggregationData = entry.value;
      return TimeSeriesData(
        date: aggregationData.date,
        hours: aggregationData.hours,
        projectId: aggregationData.projectId,
        aggregationLevel: DataAggregationLevel.quarter,
        rangeStart: aggregationData.rangeStart,
        rangeEnd: aggregationData.rangeEnd,
        dataPointCount: aggregationData.dataPoints.length,
      );
    }).toList();

    result.sort((a, b) => a.date.compareTo(b.date));
    return result;
  }

  List<TimeSeriesData> _aggregateByMonth(List<TimeSeriesData> data) {
    final Map<String, _AggregationData> monthlyData = {};

    for (final entry in data) {
      final date = entry.date;
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';

      // Calculate month range
      final monthStart = DateTime(date.year, date.month, 1);
      final monthEnd = DateTime(
        date.year + (date.month == 12 ? 1 : 0),
        date.month == 12 ? 1 : date.month + 1,
        0
      ); // Last day of the month

      if (!monthlyData.containsKey(monthKey)) {
        // Create a new entry for this month (use 1st day of month as date)
        monthlyData[monthKey] = _AggregationData(
          date: monthStart,
          hours: entry.hours,
          projectId: entry.projectId,
          rangeStart: monthStart,
          rangeEnd: monthEnd,
          dataPoints: [entry],
        );
      } else {
        // Add hours to existing month
        final existing = monthlyData[monthKey]!;
        monthlyData[monthKey] = _AggregationData(
          date: existing.date,
          hours: existing.hours + entry.hours,
          projectId: entry.projectId,
          rangeStart: existing.rangeStart,
          rangeEnd: existing.rangeEnd,
          dataPoints: [...existing.dataPoints, entry],
        );
      }
    }

    // Convert to TimeSeriesData with aggregation info
    final result = monthlyData.entries.map((entry) {
      final aggregationData = entry.value;
      return TimeSeriesData(
        date: aggregationData.date,
        hours: aggregationData.hours,
        projectId: aggregationData.projectId,
        aggregationLevel: DataAggregationLevel.month,
        rangeStart: aggregationData.rangeStart,
        rangeEnd: aggregationData.rangeEnd,
        dataPointCount: aggregationData.dataPoints.length,
      );
    }).toList();

    result.sort((a, b) => a.date.compareTo(b.date));
    return result;
  }

  List<TimeSeriesData> _aggregateByWeek(List<TimeSeriesData> data) {
    final Map<String, _AggregationData> weeklyData = {};

    for (final entry in data) {
      final date = entry.date;
      // Get the Monday of the week
      final monday = date.subtract(Duration(days: date.weekday - 1));
      final weekKey = '${monday.year}-${monday.month.toString().padLeft(2, '0')}-${monday.day.toString().padLeft(2, '0')}';

      // Calculate week range (Monday to Sunday)
      final weekStart = monday;
      final weekEnd = monday.add(const Duration(days: 6));

      if (!weeklyData.containsKey(weekKey)) {
        // Create a new entry for this week (use Monday as date)
        weeklyData[weekKey] = _AggregationData(
          date: monday,
          hours: entry.hours,
          projectId: entry.projectId,
          rangeStart: weekStart,
          rangeEnd: weekEnd,
          dataPoints: [entry],
        );
      } else {
        // Add hours to existing week
        final existing = weeklyData[weekKey]!;
        weeklyData[weekKey] = _AggregationData(
          date: existing.date,
          hours: existing.hours + entry.hours,
          projectId: entry.projectId,
          rangeStart: existing.rangeStart,
          rangeEnd: existing.rangeEnd,
          dataPoints: [...existing.dataPoints, entry],
        );
      }
    }

    // Convert to TimeSeriesData with aggregation info
    final result = weeklyData.entries.map((entry) {
      final aggregationData = entry.value;
      return TimeSeriesData(
        date: aggregationData.date,
        hours: aggregationData.hours,
        projectId: aggregationData.projectId,
        aggregationLevel: DataAggregationLevel.week,
        rangeStart: aggregationData.rangeStart,
        rangeEnd: aggregationData.rangeEnd,
        dataPointCount: aggregationData.dataPoints.length,
      );
    }).toList();

    result.sort((a, b) => a.date.compareTo(b.date));
    return result;
  }

  List<TimeSeriesData> _aggregateByDayGroups(List<TimeSeriesData> data, int groupSize) {
    if (data.isEmpty) return [];

    // Sort data by date first
    data.sort((a, b) => a.date.compareTo(b.date));

    final List<TimeSeriesData> result = [];
    final Map<String, _AggregationData> groupedData = {};

    // Use the first date as reference
    DateTime referenceDate = data.first.date;

    for (final entry in data) {
      final dayDiff = entry.date.difference(referenceDate).inDays;
      final groupIndex = dayDiff ~/ groupSize;
      final groupKey = 'group_$groupIndex';

      // Calculate the date range for this group
      final groupStartDay = referenceDate.add(Duration(days: groupIndex * groupSize));
      final groupEndDay = referenceDate.add(Duration(days: (groupIndex + 1) * groupSize - 1));

      if (!groupedData.containsKey(groupKey)) {
        groupedData[groupKey] = _AggregationData(
          date: entry.date, // Use the first date in the group
          hours: entry.hours,
          projectId: entry.projectId,
          rangeStart: groupStartDay,
          rangeEnd: groupEndDay,
          dataPoints: [entry],
        );
      } else {
        final existing = groupedData[groupKey]!;
        groupedData[groupKey] = _AggregationData(
          date: existing.date,
          hours: existing.hours + entry.hours,
          projectId: entry.projectId,
          rangeStart: existing.rangeStart,
          rangeEnd: existing.rangeEnd,
          dataPoints: [...existing.dataPoints, entry],
        );
      }
    }

    // Convert to TimeSeriesData with aggregation info
    result.addAll(groupedData.entries.map((entry) {
      final aggregationData = entry.value;
      return TimeSeriesData(
        date: aggregationData.date,
        hours: aggregationData.hours,
        projectId: aggregationData.projectId,
        aggregationLevel: DataAggregationLevel.twoDays,
        rangeStart: aggregationData.rangeStart,
        rangeEnd: aggregationData.rangeEnd,
        dataPointCount: aggregationData.dataPoints.length,
      );
    }));

    result.sort((a, b) => a.date.compareTo(b.date));
    return result;
  }

  // Get record statistics
  Map<String, dynamic> getRecordStatistics(AnalysisResult analysis) {
    if (analysis.timeSeriesData.isEmpty) {
      return {
        'longestDay': null,
        'shortestDay': null,
        'mostConsecutiveDays': 0,
        'averagePerWeekday': <String, double>{},
        'totalDaysLogged': 0,
        'streakData': <Map<String, dynamic>>[],
      };
    }

    // Find longest and shortest days
    final sortedByHours = List<TimeSeriesData>.from(analysis.timeSeriesData)
      ..sort((a, b) => b.hours.compareTo(a.hours));

    final longestDay = sortedByHours.first;
    final shortestDay = sortedByHours.last;

    // Calculate average hours per weekday
    final Map<int, List<double>> hoursByWeekday = {};
    for (final entry in analysis.timeSeriesData) {
      final weekday = entry.date.weekday;
      if (!hoursByWeekday.containsKey(weekday)) {
        hoursByWeekday[weekday] = [];
      }
      hoursByWeekday[weekday]!.add(entry.hours);
    }

    final Map<String, double> averagePerWeekday = {};
    hoursByWeekday.forEach((weekday, hours) {
      final total = hours.reduce((a, b) => a + b);
      averagePerWeekday[_getWeekdayName(DateTime(2023, 1, 2 + weekday - 1))] = total / hours.length;
    });

    // Find consecutive day streaks
    final sortedByDate = List<TimeSeriesData>.from(analysis.timeSeriesData)
      ..sort((a, b) => a.date.compareTo(b.date));

    int currentStreak = 1;
    int maxStreak = 1;
    DateTime? streakStart = sortedByDate.isNotEmpty ? sortedByDate.first.date : null;
    DateTime? maxStreakStart;
    DateTime? maxStreakEnd;

    final List<Map<String, dynamic>> streakData = [];

    for (int i = 1; i < sortedByDate.length; i++) {
      final current = sortedByDate[i].date;
      final previous = sortedByDate[i - 1].date;

      if (current.difference(previous).inDays == 1) {
        // Consecutive day
        currentStreak++;
        if (currentStreak > maxStreak) {
          maxStreak = currentStreak;
          maxStreakStart = streakStart;
          maxStreakEnd = current;
        }
      } else {
        // Streak broken
        if (currentStreak > 1) {
          streakData.add({
            'start': streakStart,
            'end': previous,
            'length': currentStreak,
          });
        }
        currentStreak = 1;
        streakStart = current;
      }
    }

    // Add the last streak
    if (currentStreak > 1 && streakStart != null) {
      streakData.add({
        'start': streakStart,
        'end': sortedByDate.last.date,
        'length': currentStreak,
      });
    }

    // Sort streaks by length (descending)
    streakData.sort((a, b) => (b['length'] as int).compareTo(a['length'] as int));

    return {
      'longestDay': longestDay,
      'shortestDay': shortestDay,
      'mostConsecutiveDays': maxStreak,
      'mostConsecutiveDaysStart': maxStreakStart,
      'mostConsecutiveDaysEnd': maxStreakEnd,
      'averagePerWeekday': averagePerWeekday,
      'totalDaysLogged': analysis.timeSeriesData.length,
      'streakData': streakData.take(5).toList(), // Top 5 streaks
    };
  }
}
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';

import 'location/location_utils.dart';
import 'location/task_position_tracker.dart';
import 'location/task_area_processor.dart';
import 'location/task_time_entry_manager.dart';

@pragma('vm:entry-point')
void startLocationTaskCallback() {
  FlutterForegroundTask.setTaskHandler(LocationTaskHandler());
}

class LocationTaskHandler extends TaskHandler {
  late TaskTimeEntryManager _timeEntryManager;
  late TaskAreaProcessor _areaProcessor;
  late TaskPositionTracker _positionTracker;

  bool _isTrackingActive = false;
  DateTime? _lastNotificationUpdate;
  DateTime? _lastStatusUpdate;

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    debugPrint('LocationTaskHandler: onStart called at $timestamp');

    try {
      await Future.delayed(const Duration(milliseconds: 300));

      _isTrackingActive = true;

      _initializeComponents();
      await _startLocationTracking();
      await _restoreActiveTimeEntries();

      _sendStatusToMain();

      debugPrint('LocationTaskHandler: Initialization completed successfully');
    } catch (e) {
      debugPrint('Error in LocationTaskHandler.onStart: $e');
      _isTrackingActive = true;
      _sendStatusToMain();
    }
  }

  void _initializeComponents() {
    _timeEntryManager = TaskTimeEntryManager();
    _areaProcessor = TaskAreaProcessor(_timeEntryManager);
    _positionTracker = TaskPositionTracker(_areaProcessor);
  }

  Future<void> _startLocationTracking() async {
    await _positionTracker.start();
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    debugPrint('LocationTaskHandler: onRepeatEvent - isTracking: $_isTrackingActive, currentProjectName: ${_timeEntryManager.currentProjectName}, trackingStartTime: ${_timeEntryManager.trackingStartTime}');

    _updateNotificationIfNeeded();

    final now = DateTime.now();
    if (_lastStatusUpdate == null || now.difference(_lastStatusUpdate!).inSeconds >= 3) {
      _sendStatusToMain();
      _lastStatusUpdate = now;
    }
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    debugPrint('LocationTaskHandler: onDestroy (isTimeout: $isTimeout)');

    await _cleanupTracking();
    _isTrackingActive = false;

    _sendStatusToMain();
  }

  @override
  void onReceiveData(Object data) {
    debugPrint('LocationTaskHandler: onReceiveData: $data');

    if (data is Map<String, dynamic>) {
      final command = data['command'] as String?;

      switch (command) {
        case 'startTracking':
          final areaJson = data['area'] as String?;
          if (areaJson != null) {
            final area = LocationArea.fromJson(jsonDecode(areaJson));
            debugPrint('LocationTaskHandler: Starting tracking for area: ${area.name}');
            _areaProcessor.enableAreaTracking(area);
          }
          break;

        case 'stopTracking':
          final areaJson = data['area'] as String?;
          if (areaJson != null) {
            final area = LocationArea.fromJson(jsonDecode(areaJson));
            debugPrint('LocationTaskHandler: Stopping tracking for area: ${area.name}');
            _areaProcessor.stopAreaTracking(area);
          } else {
            debugPrint('LocationTaskHandler: Stopping all tracking');
            _areaProcessor.stopAllTracking();
          }
          break;

        case 'checkStatus':
          final now = DateTime.now();
          if (_lastStatusUpdate == null || now.difference(_lastStatusUpdate!).inMilliseconds >= 500) {
            _sendStatusToMain();
            _lastStatusUpdate = now;
          }
          break;

        case 'updateData':
          debugPrint('LocationTaskHandler: Updating cached data');
          _updateCachedData(data);
          break;

        case 'updateArea':
          final areaJson = data['area'] as String?;
          if (areaJson != null) {
            final area = LocationArea.fromJson(jsonDecode(areaJson));
            debugPrint('LocationTaskHandler: Updating area: ${area.name}');
            _areaProcessor.updateArea(area);
          }
          break;

        case 'stopTimeEntryCallback':
          if (data.containsKey('timeEntry')) {
            final timeEntryJson = data['timeEntry'] as Map<String, dynamic>;
            final timeEntry = TimeEntry.fromJson(timeEntryJson);
            debugPrint('LocationTaskHandler: Completing stop time entry: ${timeEntry.id}');
            _timeEntryManager.completeStopTimeEntry(timeEntry);

            // Reset the foreground notification to the idle state after tracking stops
            _updateNotification(
              title: 'Location Tracking Active',
              body: 'Waiting to enter a tracking area...',
            );

            // Ensure we do not attempt to refresh the old duration
            _lastNotificationUpdate = DateTime.now();
          }
          break;

        case 'activeTimeEntries':
          if (data.containsKey('entries')) {
            final entries = data['entries'] as List<dynamic>;
            debugPrint('LocationTaskHandler: Processing ${entries.length} active time entries');
            _processActiveTimeEntries(entries);
          }
          break;

        case 'timeEntriesStatus':
          if (data.containsKey('inactiveIds')) {
            final inactiveIds = data['inactiveIds'] as List<dynamic>;
            debugPrint('LocationTaskHandler: Processing ${inactiveIds.length} inactive time entries');
            _areaProcessor.removeInactiveTimeEntries(inactiveIds.cast<String>());
          }
          break;

        case 'activeTimeEntryForProject':
          if (data.containsKey('projectId') && data.containsKey('activeEntry')) {
            final projectId = data['projectId'] as String;
            final activeEntryData = data['activeEntry'] as Map<String, dynamic>?;
            debugPrint('LocationTaskHandler: Handling active time entry response for project $projectId');
            final entryId = _timeEntryManager.handleActiveTimeEntryResponse(projectId, activeEntryData);
            debugPrint('LocationTaskHandler: Time entry result: $entryId');
          }
          break;
      }
    }
  }

  void _updateCachedData(Map<String, dynamic> data) {
    try {
      List<Project>? projects;
      List<LocationArea>? areas;

      if (data.containsKey('projects')) {
        final projectsJson = data['projects'] as List<dynamic>;
        projects = projectsJson.map((json) => Project.fromJson(json)).toList();
        debugPrint('LocationTaskHandler: Updated cache with ${projects.length} projects');
      }

      if (data.containsKey('areas')) {
        final areasJson = data['areas'] as List<dynamic>;
        areas = areasJson.map((json) => LocationArea.fromJson(json)).toList();
        debugPrint('LocationTaskHandler: Updated cache with ${areas.length} areas');
      }

      _areaProcessor.updateCache(areas: areas, projects: projects);
    } catch (e) {
      debugPrint('Error updating cached data: $e');
    }
  }

  @override
  void onNotificationButtonPressed(String id) {
    debugPrint('LocationTaskHandler: onNotificationButtonPressed: $id');

    if (id == 'stopTracking') {
      _areaProcessor.stopAllTracking();
    }
  }

  Future<void> _restoreActiveTimeEntries() async {
    try {
      FlutterForegroundTask.sendDataToMain({
        'command': 'getActiveTimeEntries',
      });

      _updateNotification(
        title: 'Location Tracking Active',
        body: 'Waiting to enter a tracking area...',
      );
    } catch (e) {
      debugPrint('Error restoring active time entries: $e');
    }
  }

  void _processActiveTimeEntries(List<dynamic> activeEntriesJson) {
    try {
      // Process active entries in TimeEntryManager
      _timeEntryManager.processActiveTimeEntries(activeEntriesJson, _areaProcessor.projectsCache);

      // Update notification if we have tracking info
      if (_timeEntryManager.currentProjectName != null && _timeEntryManager.trackingStartTime != null) {
        debugPrint('LocationTaskHandler: Found active tracking for ${_timeEntryManager.currentProjectName}');
        final duration = DateTime.now().difference(_timeEntryManager.trackingStartTime!);
        final durationText = LocationUtils.formatDuration(duration);

        _updateNotification(
          title: 'Time Tracking Active',
          body: 'Tracking ${_timeEntryManager.currentProjectName} for $durationText',
        );
      } else {
        debugPrint('LocationTaskHandler: No active tracking found');
      }
    } catch (e) {
      debugPrint('Error processing active time entries: $e');
    }
  }

  void _updateNotificationIfNeeded() {
    if (_timeEntryManager.currentProjectName == null || _timeEntryManager.trackingStartTime == null) {
      return;
    }

    final now = DateTime.now();
    if (_lastNotificationUpdate != null &&
        now.difference(_lastNotificationUpdate!).inSeconds < 10) {
      return;
    }

    final duration = now.difference(_timeEntryManager.trackingStartTime!);
    final durationText = LocationUtils.formatDuration(duration);

    debugPrint('LocationTaskHandler: Updating notification - ${_timeEntryManager.currentProjectName} for $durationText');

    _updateNotification(
      title: 'Time Tracking Active',
      body: 'Tracking ${_timeEntryManager.currentProjectName} for $durationText',
    );

    _lastNotificationUpdate = now;
  }

  void _updateNotification({required String title, required String body}) {
    FlutterForegroundTask.updateService(
      notificationTitle: title,
      notificationText: body,
    );
  }

  Future<void> _cleanupTracking() async {
    await _positionTracker.stop();
  }

  void _sendStatusToMain() {
    if (!_isTrackingActive) {
      FlutterForegroundTask.sendDataToMain({
        'isTracking': false,
      });
      return;
    }

    final activeTimeEntriesCopy = Map<String, String>.from(_areaProcessor.activeTimeEntries);

    debugPrint('LocationTaskHandler: Sending status - isTracking: true, activeTimeEntries: ${activeTimeEntriesCopy.length}, currentProjectName: ${_timeEntryManager.currentProjectName}');

    FlutterForegroundTask.sendDataToMain({
      'isTracking': true,
      'activeTimeEntries': activeTimeEntriesCopy,
      'currentProjectName': _timeEntryManager.currentProjectName,
      'trackingStartTime': _timeEntryManager.trackingStartTime?.millisecondsSinceEpoch,
    });
  }
}

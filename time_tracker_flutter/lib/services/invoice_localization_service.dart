import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Service for handling invoice localization and text translations
class InvoiceLocalizationService {
  // Supported languages for invoices
  static const Map<String, Map<String, String>> _translations = {
    'en_US': {
      'invoice': 'INVOICE',
      'bill_to': 'Bill To:',
      'description': 'Description',
      'hours': 'Hours',
      'rate': 'Rate',
      'amount': 'Amount',
      'subtotal': 'Subtotal',
      'tax': 'Tax',
      'total': 'Total',
      'notes': 'Notes:',
      'date': 'Date:',
      'due': 'Due:',
      'invoice_number': 'Invoice #',
      'time_tracking': 'Time tracking',
      'additional_items': 'Additional Items',
      'discount': 'Discount',
      'shipping': 'Shipping',
      'handling': 'Handling',
      'other': 'Other',
      'paid': 'PAID',
      'overdue': 'OVERDUE',
      'draft': 'DRAFT',
      'sent': 'SENT',
      'cancelled': 'CANCELLED',
      'payment_terms': 'Payment Terms',
      'net_30': 'Net 30',
      'net_15': 'Net 15',
      'due_on_receipt': 'Due on Receipt',
      'thank_you': 'Thank you for your business!',
      'please_pay': 'Please pay within the specified terms.',
      'questions': 'If you have any questions, please contact us.',
      'currency_symbol': '\$',
      'decimal_separator': '.',
      'thousands_separator': ',',
      'invoice_details': 'Invoice Details',
      'issue_date': 'Issue Date',
      'due_date': 'Due Date',
      'status': 'Status',
      'currency': 'Currency',
      'service_period': 'Service Period',
      'enable_service_period': 'Enable Service Period',
      'from': 'From',
      'to': 'To',
      'both_dates_required': 'Both start and end dates are required when service period is enabled',
      'end_date_must_be_after_start': 'End date must be after or equal to start date',
      'select_date': 'Select date',
    },
    'de_DE': {
      'invoice': 'RECHNUNG',
      'bill_to': 'Rechnung an:',
      'description': 'Beschreibung',
      'hours': 'Stunden',
      'rate': 'Satz',
      'amount': 'Betrag',
      'subtotal': 'Zwischensumme',
      'tax': 'Steuer',
      'total': 'Gesamtbetrag',
      'notes': 'Anmerkungen:',
      'date': 'Datum:',
      'due': 'Fällig:',
      'invoice_number': 'Rechnung: ',
      'time_tracking': 'Zeiterfassung',
      'additional_items': 'Zusätzliche Artikel',
      'discount': 'Rabatt',
      'shipping': 'Versand',
      'handling': 'Bearbeitung',
      'other': 'Sonstiges',
      'paid': 'BEZAHLT',
      'overdue': 'ÜBERFÄLLIG',
      'draft': 'ENTWURF',
      'sent': 'GESENDET',
      'cancelled': 'STORNIERT',
      'payment_terms': 'Zahlungsbedingungen',
      'net_30': 'Netto 30',
      'net_15': 'Netto 15',
      'due_on_receipt': 'Bei Erhalt fällig',
      'thank_you': 'Vielen Dank für Ihr Vertrauen!',
      'please_pay': 'Bitte zahlen Sie innerhalb der angegebenen Frist.',
      'questions': 'Bei Fragen kontaktieren Sie uns bitte.',
      'currency_symbol': '€',
      'decimal_separator': ',',
      'thousands_separator': '.',
      'invoice_details': 'Rechnungsdetails',
      'issue_date': 'Rechnungsdatum',
      'due_date': 'Fälligkeitsdatum',
      'status': 'Status',
      'currency': 'Währung',
      'service_period': 'Leistungszeitraum',
      'enable_service_period': 'Leistungszeitraum aktivieren',
      'from': 'Von',
      'to': 'Bis',
      'both_dates_required': 'Beide Daten (Start und Ende) sind erforderlich, wenn der Leistungszeitraum aktiviert ist',
      'end_date_must_be_after_start': 'Das Enddatum muss nach oder gleich dem Startdatum sein',
      'select_date': 'Datum auswählen',
    },
    'fr_FR': {
      'invoice': 'FACTURE',
      'bill_to': 'Facturer à:',
      'description': 'Description',
      'hours': 'Heures',
      'rate': 'Taux',
      'amount': 'Montant',
      'subtotal': 'Sous-total',
      'tax': 'Taxe',
      'total': 'Total',
      'notes': 'Notes:',
      'date': 'Date:',
      'due': 'Échéance:',
      'invoice_number': 'Facture n°',
      'time_tracking': 'Suivi du temps',
      'additional_items': 'Articles supplémentaires',
      'discount': 'Remise',
      'shipping': 'Expédition',
      'handling': 'Manutention',
      'other': 'Autre',
      'paid': 'PAYÉ',
      'overdue': 'EN RETARD',
      'draft': 'BROUILLON',
      'sent': 'ENVOYÉ',
      'cancelled': 'ANNULÉ',
      'payment_terms': 'Conditions de paiement',
      'net_30': 'Net 30',
      'net_15': 'Net 15',
      'due_on_receipt': 'Payable à réception',
      'thank_you': 'Merci pour votre confiance !',
      'please_pay': 'Veuillez payer dans les délais spécifiés.',
      'questions': 'Si vous avez des questions, contactez-nous.',
      'currency_symbol': '€',
      'decimal_separator': ',',
      'thousands_separator': ' ',
      'invoice_details': 'Détails de la facture',
      'issue_date': 'Date de facture',
      'due_date': 'Date d’échéance',
      'status': 'Statut',
      'currency': 'Devise',
    },
    'es_ES': {
      'invoice': 'FACTURA',
      'bill_to': 'Facturar a:',
      'description': 'Descripción',
      'hours': 'Horas',
      'rate': 'Tarifa',
      'amount': 'Importe',
      'subtotal': 'Subtotal',
      'tax': 'Impuesto',
      'total': 'Total',
      'notes': 'Notas:',
      'date': 'Fecha:',
      'due': 'Vencimiento:',
      'invoice_number': 'Factura n°',
      'time_tracking': 'Seguimiento de tiempo',
      'additional_items': 'Artículos adicionales',
      'discount': 'Descuento',
      'shipping': 'Envío',
      'handling': 'Manejo',
      'other': 'Otro',
      'paid': 'PAGADO',
      'overdue': 'VENCIDO',
      'draft': 'BORRADOR',
      'sent': 'ENVIADO',
      'cancelled': 'CANCELADO',
      'payment_terms': 'Términos de pago',
      'net_30': 'Neto 30',
      'net_15': 'Neto 15',
      'due_on_receipt': 'Pagadero al recibir',
      'thank_you': '¡Gracias por su confianza!',
      'please_pay': 'Por favor pague dentro de los términos especificados.',
      'questions': 'Si tiene alguna pregunta, contáctenos.',
      'currency_symbol': '€',
      'decimal_separator': ',',
      'thousands_separator': '.',
      'invoice_details': 'Detalles de la factura',
      'issue_date': 'Fecha de factura',
      'due_date': 'Fecha de vencimiento',
      'status': 'Estado',
      'currency': 'Moneda',
    },
  };

  /// Get supported languages for invoices
  static List<String> getSupportedLanguages() {
    return _translations.keys.toList();
  }

  /// Get language display names
  static Map<String, String> getLanguageDisplayNames() {
    return {
      'en_US': 'English (US)',
      'de_DE': 'Deutsch (Deutschland)',
      'fr_FR': 'Français (France)',
      'es_ES': 'Español (España)',
    };
  }

  /// Get translation for a specific key and language
  static String getTranslation(String key, String language) {
    final translations = _translations[language];
    if (translations == null) {
      // Fallback to English if language not supported
      return _translations['en_US']?[key] ?? key;
    }
    return translations[key] ?? _translations['en_US']?[key] ?? key;
  }

  /// Get all translations for a specific language
  static Map<String, String> getTranslationsForLanguage(String language) {
    return _translations[language] ?? _translations['en_US']!;
  }

  /// Check if a language is supported
  static bool isLanguageSupported(String language) {
    return _translations.containsKey(language);
  }

  /// Get locale from language string
  static Locale getLocaleFromLanguage(String language) {
    final parts = language.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    } else if (parts.length == 1) {
      return Locale(parts[0]);
    }
    return const Locale('en', 'US');
  }

  /// Get language string from locale
  static String getLanguageFromLocale(Locale locale) {
    if (locale.countryCode != null) {
      return '${locale.languageCode}_${locale.countryCode}';
    }
    return locale.languageCode;
  }

  /// Format currency amount with language-specific formatting
  static String formatCurrencyForLanguage(
    double amount,
    String currencyCode,
    String language,
  ) {
    final locale = getLocaleFromLanguage(language);
    final translations = getTranslationsForLanguage(language);

    try {
      // Get currency symbol and formatting preferences
      final currencySymbol = translations['currency_symbol'] ?? '\$';
      final decimalSeparator = translations['decimal_separator'] ?? '.';
      final thousandsSeparator = translations['thousands_separator'] ?? ',';

      // Create NumberFormat for the specific locale
      final formatter = NumberFormat.currency(
        locale: locale.toString(),
        symbol: currencySymbol,
        decimalDigits: 2,
      );

      return formatter.format(amount);
    } catch (e) {
      // Fallback formatting
      final currencySymbol = translations['currency_symbol'] ?? '\$';
      return '$currencySymbol${amount.toStringAsFixed(2)}';
    }
  }

  /// Get invoice status translation
  static String getInvoiceStatusTranslation(String status, String language) {
    final statusKey = status.toLowerCase();
    return getTranslation(statusKey, language);
  }

  /// Get payment terms translation
  static String getPaymentTermsTranslation(String terms, String language) {
    switch (terms.toLowerCase()) {
      case 'net_30':
        return getTranslation('net_30', language);
      case 'net_15':
        return getTranslation('net_15', language);
      case 'due_on_receipt':
        return getTranslation('due_on_receipt', language);
      default:
        return terms;
    }
  }

  /// Get common invoice phrases
  static Map<String, String> getInvoicePhrases(String language) {
    return {
      'thank_you': getTranslation('thank_you', language),
      'please_pay': getTranslation('please_pay', language),
      'questions': getTranslation('questions', language),
    };
  }

  /// Get column headers for invoice tables
  static Map<String, String> getInvoiceHeaders(String language) {
    return {
      'description': getTranslation('description', language),
      'hours': getTranslation('hours', language),
      'rate': getTranslation('rate', language),
      'amount': getTranslation('amount', language),
    };
  }

  /// Get summary labels
  static Map<String, String> getSummaryLabels(String language) {
    return {
      'subtotal': getTranslation('subtotal', language),
      'tax': getTranslation('tax', language),
      'total': getTranslation('total', language),
    };
  }

  /// Validate if a language has complete translations
  static bool hasCompleteTranslations(String language) {
    final translations = _translations[language];
    if (translations == null) return false;

    // Check for essential translations
    final essentialKeys = [
      'invoice', 'bill_to', 'description', 'hours', 'rate', 'amount',
      'subtotal', 'tax', 'total', 'notes', 'date', 'due'
    ];

    for (final key in essentialKeys) {
      if (!translations.containsKey(key) || translations[key]!.isEmpty) {
        return false;
      }
    }

    return true;
  }

  /// Get language information for display
  static Map<String, dynamic> getLanguageInfo(String language) {
    final displayNames = getLanguageDisplayNames();
    final hasComplete = hasCompleteTranslations(language);

    return {
      'code': language,
      'displayName': displayNames[language] ?? language,
      'hasCompleteTranslations': hasComplete,
      'locale': getLocaleFromLanguage(language),
    };
  }

  /// Get all supported languages with their information
  static List<Map<String, dynamic>> getAllLanguageInfo() {
    final languages = getSupportedLanguages();
    return languages.map((lang) => getLanguageInfo(lang)).toList();
  }
}
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'location_utils.dart';
import 'task_area_processor.dart';

enum TrackingMode {
  normal,      // Regular adaptive tracking
  stationary,  // Reduced tracking when user is stationary
  sleep,       // Minimal tracking during inactive hours
}

class AdaptiveLocationTracker {
  StreamSubscription<Position>? _positionStream;
  final TaskAreaProcessor _areaProcessor;
  bool _isActive = false;
  int _restartAttempts = 0;
  static const int _maxRestartAttempts = 3;

  // Adaptive tracking parameters
  LatLng? _lastPosition;
  DateTime? _lastUpdate;
  LocationSettings? _currentSettings;
  TrackingMode _trackingMode = TrackingMode.normal;

  // Distance-based thresholds (in meters)
  static const double _veryCloseThreshold = 50.0;    // High accuracy, frequent updates
  static const double _closeThreshold = 200.0;       // Medium accuracy, moderate updates
  static const double _nearThreshold = 500.0;        // Lower accuracy, less frequent updates
  static const double _farThreshold = 1000.0;        // Minimal updates, basic accuracy

  AdaptiveLocationTracker(this._areaProcessor);

  bool get isActive => _isActive;

  void updateTrackingMode(TrackingMode mode) {
    if (_trackingMode != mode) {
      debugPrint('AdaptiveLocationTracker: Tracking mode changed from ${_trackingMode} to $mode');
      _trackingMode = mode;

      // Restart stream with new settings if currently active
      if (_isActive && _lastPosition != null) {
        _adaptLocationSettings(_lastPosition!);
      }
    }
  }

  Future<void> start() async {
    if (_positionStream != null) {
      await stop();
    }

    _isActive = true;
    _restartAttempts = 0;
    await _startPositionStream();
  }

  Future<void> _startPositionStream() async {
    try {
      // Start with medium settings for initial positioning
      final initialSettings = _getLocationSettingsForDistance(_closeThreshold);
      _currentSettings = initialSettings;

      debugPrint('AdaptiveLocationTracker: Starting with initial settings - accuracy: ${initialSettings.accuracy}, distanceFilter: ${initialSettings.distanceFilter}');

      _positionStream = Geolocator.getPositionStream(locationSettings: initialSettings)
          .listen(_handlePositionUpdate, onError: _handleStreamError);

      debugPrint('AdaptiveLocationTracker: Started adaptive position tracking');
      await _getCurrentPosition();
    } catch (e) {
      debugPrint('AdaptiveLocationTracker: Error starting position stream: $e');
      _isActive = false;
    }
  }

  LocationSettings _getLocationSettingsForDistance(double minDistanceToArea) {
    // Base timeouts based on distance
    final baseTimeouts = switch (minDistanceToArea) {
      <= _veryCloseThreshold => 15,
      <= _closeThreshold => 20,
      <= _nearThreshold => 30,
      <= _farThreshold => 45,
      _ => 60,
    };

    // Adjust timeout based on tracking mode
    final adjustedTimeout = switch (_trackingMode) {
      TrackingMode.normal => baseTimeouts,
      TrackingMode.stationary => math.max(baseTimeouts, 45), // At least 45 seconds
      TrackingMode.sleep => math.max(baseTimeouts, 60),      // At least 60 seconds
    };

    return switch (minDistanceToArea) {
      <= _veryCloseThreshold =>
        // Very close to an area - maximum accuracy and frequent updates
        LocationSettings(
          accuracy: LocationAccuracy.best,
          distanceFilter: 5,
          timeLimit: Duration(seconds: adjustedTimeout),
        ),
      <= _closeThreshold =>
        // Close to an area - high accuracy, moderate updates
        LocationSettings(
          accuracy: LocationAccuracy.best,
          distanceFilter: 10,
          timeLimit: Duration(seconds: adjustedTimeout),
        ),
      <= _nearThreshold =>
        // Near an area - medium accuracy, less frequent updates
        LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 25,
          timeLimit: Duration(seconds: adjustedTimeout),
        ),
      <= _farThreshold =>
        // Far from areas - lower accuracy, infrequent updates
        LocationSettings(
          accuracy: LocationAccuracy.medium,
          distanceFilter: 50,
          timeLimit: Duration(seconds: adjustedTimeout),
        ),
      _ =>
        // Very far from any area - minimal updates to save battery
        LocationSettings(
          accuracy: LocationAccuracy.low,
          distanceFilter: 100,
          timeLimit: Duration(seconds: adjustedTimeout),
        ),
    };
  }

  double _getMinDistanceToActiveAreas(LatLng userLocation) {
    final activeAreas = _areaProcessor.areasCache.where((area) => area.isActive);

    if (activeAreas.isEmpty) {
      return double.infinity;
    }

    double minDistance = double.infinity;

    for (final area in activeAreas) {
      final areaCenter = LatLng(area.centerLatitude, area.centerLongitude);
      final distance = LocationUtils.calculateDistance(userLocation, areaCenter);
      // Subtract the area radius to get distance to the area boundary
      final distanceToAreaBoundary = math.max(0.0, distance - area.radius);

      if (distanceToAreaBoundary < minDistance) {
        minDistance = distanceToAreaBoundary;
      }
    }

    return minDistance;
  }

  Future<void> _adaptLocationSettings(LatLng userLocation) async {
    final minDistance = _getMinDistanceToActiveAreas(userLocation);
    final newSettings = _getLocationSettingsForDistance(minDistance);

    // Only restart the stream if settings actually changed
    if (_currentSettings?.accuracy != newSettings.accuracy ||
        _currentSettings?.distanceFilter != newSettings.distanceFilter) {

      debugPrint('AdaptiveLocationTracker: Adapting settings for distance ${minDistance.toStringAsFixed(0)}m - '
                'accuracy: ${newSettings.accuracy}, distanceFilter: ${newSettings.distanceFilter}');

      _currentSettings = newSettings;

      // Cancel current stream and start new one with updated settings
      await _positionStream?.cancel();

      _positionStream = Geolocator.getPositionStream(locationSettings: newSettings)
          .listen(_handlePositionUpdate, onError: _handleStreamError);
    }
  }

  Future<void> _getCurrentPosition() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      debugPrint('AdaptiveLocationTracker: Got immediate position - Lat: ${position.latitude}, Lng: ${position.longitude}');
      await _handlePositionUpdate(position);
    } catch (e) {
      debugPrint('AdaptiveLocationTracker: Error getting immediate position: $e');
    }
  }

  void _handleStreamError(dynamic error) {
    debugPrint('AdaptiveLocationTracker: Position stream error: $error');

    if (_isActive && _restartAttempts < _maxRestartAttempts) {
      _restartAttempts++;
      debugPrint('AdaptiveLocationTracker: Attempting restart ${_restartAttempts}/$_maxRestartAttempts...');

      Future.delayed(Duration(seconds: 5 * _restartAttempts), () {
        if (_isActive) {
          _startPositionStream();
        }
      });
    } else {
      debugPrint('AdaptiveLocationTracker: Max restart attempts reached or tracker inactive');
      _isActive = false;
    }
  }

  Future<void> stop() async {
    _isActive = false;
    _restartAttempts = 0;

    if (_positionStream != null) {
      await _positionStream!.cancel().catchError((e) {
        debugPrint('Error cancelling position stream: $e');
      });
      _positionStream = null;
    }

    debugPrint('AdaptiveLocationTracker: Stopped adaptive position tracking');
  }

  Future<void> _handlePositionUpdate(Position position) async {
    if (!_isActive) return;

    try {
      final userLocation = LatLng(position.latitude, position.longitude);

      debugPrint('AdaptiveLocationTracker: Position update - Lat: ${position.latitude}, Lng: ${position.longitude}, Accuracy: ${position.accuracy}m');

      // Check areas for tracking
      await _areaProcessor.checkAllAreas(userLocation);

      // Adapt location settings based on new position
      await _adaptLocationSettings(userLocation);

      _lastPosition = userLocation;
      _lastUpdate = DateTime.now();

      // Reset restart attempts on successful position update
      _restartAttempts = 0;
    } catch (e) {
      debugPrint('Error handling position update: $e');
    }
  }

  void dispose() {
    stop();
  }
}
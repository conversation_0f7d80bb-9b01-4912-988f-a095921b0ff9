import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:battery_plus/battery_plus.dart';

enum BatteryMode {
  performance,  // High battery (>60%) - Maximum accuracy
  balanced,     // Medium battery (30-60%) - Good balance
  conservation, // Low battery (10-30%) - Battery saving
  emergency,    // Critical battery (<10%) - Minimal tracking
}

class BatteryAwareConfig {
  static final BatteryAwareConfig _instance = BatteryAwareConfig._internal();
  factory BatteryAwareConfig() => _instance;
  BatteryAwareConfig._internal();

  final Battery _battery = Battery();
  StreamSubscription<BatteryState>? _batteryStateSubscription;
  Timer? _batteryCheckTimer;

  BatteryMode _currentMode = BatteryMode.balanced;
  int _currentBatteryLevel = 100;
  BatteryState _currentBatteryState = BatteryState.unknown;

  Function(BatteryMode mode)? _onBatteryModeChanged;

  BatteryMode get currentMode => _currentMode;
  int get currentBatteryLevel => _currentBatteryLevel;
  bool get isCharging => _currentBatteryState == BatteryState.charging;

  Future<void> initialize({Function(BatteryMode mode)? onBatteryModeChanged}) async {
    if (!Platform.isAndroid && !Platform.isIOS) {
      debugPrint('BatteryAwareConfig: Battery monitoring not supported on this platform');
      return;
    }

    _onBatteryModeChanged = onBatteryModeChanged;

    try {
      // Get initial battery level
      _currentBatteryLevel = await _battery.batteryLevel;
      _currentBatteryState = await _battery.batteryState;
      _updateBatteryMode();

      // Subscribe to battery state changes
      _batteryStateSubscription = _battery.onBatteryStateChanged.listen(_onBatteryStateChanged);

      // Periodic battery level checks (every 5 minutes)
      _batteryCheckTimer = Timer.periodic(const Duration(minutes: 5), (_) async {
        await _updateBatteryLevel();
      });

      debugPrint('BatteryAwareConfig: Initialized with level ${_currentBatteryLevel}%, state: ${_currentBatteryState}, mode: ${_currentMode}');
    } catch (e) {
      debugPrint('BatteryAwareConfig: Error initializing battery monitoring: $e');
    }
  }

  void _onBatteryStateChanged(BatteryState state) {
    debugPrint('BatteryAwareConfig: Battery state changed to $state');
    _currentBatteryState = state;
    _updateBatteryMode();
  }

  Future<void> _updateBatteryLevel() async {
    try {
      final newLevel = await _battery.batteryLevel;
      if (newLevel != _currentBatteryLevel) {
        final oldLevel = _currentBatteryLevel;
        _currentBatteryLevel = newLevel;
        debugPrint('BatteryAwareConfig: Battery level changed from $oldLevel% to $_currentBatteryLevel%');
        _updateBatteryMode();
      }
    } catch (e) {
      debugPrint('BatteryAwareConfig: Error updating battery level: $e');
    }
  }

  void _updateBatteryMode() {
    final oldMode = _currentMode;

    // Determine new mode based on battery level and charging state
    if (_currentBatteryState == BatteryState.charging) {
      // When charging, we can be more aggressive with tracking
      if (_currentBatteryLevel >= 30) {
        _currentMode = BatteryMode.performance;
      } else {
        _currentMode = BatteryMode.balanced;
      }
    } else {
      _currentMode = switch (_currentBatteryLevel) {
        >= 60 => BatteryMode.performance,
        >= 30 => BatteryMode.balanced,
        >= 10 => BatteryMode.conservation,
        _ => BatteryMode.emergency
      };
    }

    if (_currentMode != oldMode) {
      debugPrint('BatteryAwareConfig: Battery mode changed from $oldMode to $_currentMode');
      _onBatteryModeChanged?.call(_currentMode);
    }
  }

  /// Get location settings optimized for current battery mode
  Map<String, dynamic> getOptimizedLocationSettings() =>
    switch (_currentMode) {
      BatteryMode.performance =>
        {
          'accuracyMultiplier': 1.0,
          'updateIntervalMultiplier': 1.0,
          'distanceFilterMultiplier': 1.0,
          'useMotionDetection': true,
          'useAdaptiveTracking': true,
        },

      BatteryMode.balanced =>
        {
          'accuracyMultiplier': 0.9,
          'updateIntervalMultiplier': 1.2,
          'distanceFilterMultiplier': 1.5,
          'useMotionDetection': true,
          'useAdaptiveTracking': true,
        },

      BatteryMode.conservation =>
        {
          'accuracyMultiplier': 0.7,
          'updateIntervalMultiplier': 2.0,
          'distanceFilterMultiplier': 2.0,
          'useMotionDetection': true,
          'useAdaptiveTracking': false,  // Disable adaptive tracking to save CPU
        },

      BatteryMode.emergency =>
        {
          'accuracyMultiplier': 0.5,
          'updateIntervalMultiplier': 4.0,
          'distanceFilterMultiplier': 3.0,
          'useMotionDetection': false,  // Disable motion detection to save battery
          'useAdaptiveTracking': false,
        },
    };

  /// Get recommended foreground service update interval
  Duration getForegroundServiceInterval() =>
    switch (_currentMode) {
      BatteryMode.performance =>
        const Duration(seconds: 3),
      BatteryMode.balanced =>
        const Duration(seconds: 5),
      BatteryMode.conservation =>
        const Duration(seconds: 10),
      BatteryMode.emergency =>
        const Duration(seconds: 30),
    };


  /// Check if we should use high-power location features
  bool shouldUseHighPowerFeatures() {
    return _currentMode == BatteryMode.performance ||
           (_currentMode == BatteryMode.balanced && isCharging);
  }

  /// Get user-friendly description of current battery optimization
  String getBatteryModeDescription() =>
    switch (_currentMode)   {
      BatteryMode.performance =>
        isCharging
            ? 'Performance mode - Device is charging'
            : 'Performance mode - High battery level (${_currentBatteryLevel}%)',
      BatteryMode.balanced =>
        'Balanced mode - Medium battery level (${_currentBatteryLevel}%)',
      BatteryMode.conservation =>
        'Battery saving mode - Low battery level (${_currentBatteryLevel}%)',
      BatteryMode.emergency =>
        'Emergency mode - Critical battery level (${_currentBatteryLevel}%)',
    };

  void dispose() {
    _batteryStateSubscription?.cancel();
    _batteryCheckTimer?.cancel();
  }
}
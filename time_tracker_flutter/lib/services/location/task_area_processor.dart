import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:latlong2/latlong.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'location_utils.dart';
import 'task_time_entry_manager.dart';

class TaskAreaProcessor {
  final TaskTimeEntryManager _timeEntryManager;
  final Map<String, DateTime> _lastAreaTimes = {};
  final Map<String, String> _activeTimeEntries = {};

  List<LocationArea> _areasCache = [];
  List<Project> _projectsCache = [];

  TaskAreaProcessor(this._timeEntryManager) {
    _timeEntryManager.setTimeEntryCompletedCallback(removeCompletedTimeEntry);
    _timeEntryManager.setTimeEntryIdUpdatedCallback(updateTimeEntryId);
  }

  Map<String, String> get activeTimeEntries => Map.unmodifiable(_activeTimeEntries);
  List<Project> get projectsCache => List.unmodifiable(_projectsCache);
  List<LocationArea> get areasCache => List.unmodifiable(_areasCache);

  void updateCache({List<LocationArea>? areas, List<Project>? projects}) {
    if (areas != null) {
      _areasCache = areas;
      debugPrint('Updated areas cache with ${_areasCache.length} areas');
    }
    if (projects != null) {
      _projectsCache = projects;
      _timeEntryManager.updateProjectsCache(projects);
      debugPrint('Updated projects cache with ${_projectsCache.length} projects');
    }
  }

  Future<void> checkAllAreas(LatLng userLocation) async {
    try {
      debugPrint('TaskAreaProcessor: Checking areas for location: ${userLocation.latitude}, ${userLocation.longitude}');

      await _timeEntryManager.updateActiveTimeEntries();

      // If areas cache is empty, request data update but don't wait
      if (_areasCache.isEmpty) {
        debugPrint('TaskAreaProcessor: Areas cache is empty, requesting data update');
        FlutterForegroundTask.sendDataToMain({'command': 'requestData'});
        // Continue processing with empty cache instead of waiting
        return;
      }

      final activeAreas = _areasCache.where((area) => area.isActive);
      debugPrint('TaskAreaProcessor: Found ${activeAreas.length} active areas to check out of ${_areasCache.length} total areas');

      final projectsMap = {for (var p in _projectsCache) p.id: p};
      debugPrint('TaskAreaProcessor: Projects cache has ${projectsMap.length} projects');

      if (activeAreas.isEmpty) {
        debugPrint('TaskAreaProcessor: No active areas found for tracking');
        return;
      }

      for (final area in activeAreas) {
        debugPrint('TaskAreaProcessor: Checking area ${area.name} (radius: ${area.radius}m)');
        await _processArea(area, userLocation, projectsMap);
      }
    } catch (e) {
      debugPrint('Error checking all areas: $e');
    }
  }

  Future<void> _processArea(
    LocationArea area,
    LatLng userLocation,
    Map<String, Project> projectsMap
  ) async {
    final project = projectsMap[area.projectId];
    if (project == null) {
      debugPrint('Project not found for area: ${area.name}');
      return;
    }

    final isProjectTracked = await _isProjectBeingTracked(project.id);
    if (isProjectTracked && !_activeTimeEntries.containsKey(area.id)) {
      debugPrint('Project ${project.name} is already being tracked in another area');
      return;
    }

    final areaCenter = LatLng(area.centerLatitude, area.centerLongitude);
    final distance = LocationUtils.calculateDistance(userLocation, areaCenter);

    debugPrint('TaskAreaProcessor: Area ${area.name} - Distance: ${distance.toStringAsFixed(1)}m, Radius: ${area.radius}m, Currently tracking: ${_activeTimeEntries.containsKey(area.id)}');

    if (distance <= area.radius) {
      debugPrint('TaskAreaProcessor: User is inside area ${area.name}');
      await _handleAreaEntry(area, project);
    } else if (_activeTimeEntries.containsKey(area.id)) {
      debugPrint('TaskAreaProcessor: User left area ${area.name}');
      await _handleAreaExit(area, project);
    } else {
      debugPrint('TaskAreaProcessor: User is outside area ${area.name} and not currently tracking');
    }
  }

  Future<void> _handleAreaEntry(LocationArea area, Project project) async {
    _lastAreaTimes[area.id] = DateTime.now();

    if (!_activeTimeEntries.containsKey(area.id)) {
      debugPrint('TaskAreaProcessor: Entering area: ${area.name} for project: ${project.name}');
      final entryId = await _timeEntryManager.startTimeEntry(project);
      _activeTimeEntries[area.id] = entryId;
      debugPrint('TaskAreaProcessor: Started time entry ${entryId} for area ${area.name}');

      _updateNotification(
        title: 'Time Tracking Active',
        body: 'Tracking time for ${project.name} in ${area.name}',
      );

      FlutterForegroundTask.sendDataToMain({
        'command': 'areaEntered',
        'projectId': project.id,
        'projectName': project.name,
        'areaId': area.id,
        'areaName': area.name,
        'timeEntryId': entryId,
        'trackingStartTime': _timeEntryManager.trackingStartTime?.millisecondsSinceEpoch,
      });
    } else {
      debugPrint('TaskAreaProcessor: Already tracking in area ${area.name}');
    }
  }

  Future<void> _handleAreaExit(LocationArea area, Project project) async {
    if (LocationUtils.isInCooldown(area.id, area.cooldownTime, _lastAreaTimes)) {
      return;
    }

    debugPrint('Exiting area: ${area.name}');

    final entryId = _activeTimeEntries[area.id];

    if (entryId != null) {
      await _timeEntryManager.stopTimeEntry(entryId);

      FlutterForegroundTask.sendDataToMain({
        'command': 'areaExited',
        'projectId': project.id,
        'projectName': project.name,
        'areaId': area.id,
        'areaName': area.name,
        'timeEntryId': entryId,
      });
    }

    _activeTimeEntries.remove(area.id);
    _lastAreaTimes[area.id] = DateTime.now();

    if (_activeTimeEntries.isEmpty) {
      _updateNotification(
        title: 'Location Tracking Active',
        body: 'Waiting to enter a tracking area...',
      );
    }
  }

  Future<bool> _isProjectBeingTracked(String projectId) async {
    if (_activeTimeEntries.isEmpty) return false;

    try {
      for (final entry in _activeTimeEntries.entries) {
        final areaId = entry.key;
        final area = _areasCache.firstWhere(
          (a) => a.id == areaId,
          orElse: () => throw StateError('Area not found'),
        );

        if (area.projectId == projectId) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if project is being tracked: $e');
      return false;
    }
  }

  void enableAreaTracking(LocationArea area) {
    final updatedArea = area.copyWith(isActive: true);
    _updateAreaInCache(updatedArea);

    FlutterForegroundTask.sendDataToMain({
      'command': 'saveArea',
      'area': updatedArea.toJson(),
    });
  }

  void stopAreaTracking(LocationArea area) {
    final updatedArea = area.copyWith(isActive: false);
    _updateAreaInCache(updatedArea);

    FlutterForegroundTask.sendDataToMain({
      'command': 'saveArea',
      'area': updatedArea.toJson(),
    });

    if (_activeTimeEntries.containsKey(area.id)) {
      final entryId = _activeTimeEntries[area.id];
      if (entryId != null) {
        _timeEntryManager.stopTimeEntry(entryId);
      }
      _activeTimeEntries.remove(area.id);
    }
  }

  void updateArea(LocationArea area) {
    debugPrint('TaskAreaProcessor: Updating area ${area.name} in cache');
    _updateAreaInCache(area);
  }

  Future<void> stopAllTracking() async {
    for (final area in _areasCache) {
      if (area.isActive) {
        final updatedArea = area.copyWith(isActive: false);
        _updateAreaInCache(updatedArea);

        FlutterForegroundTask.sendDataToMain({
          'command': 'saveArea',
          'area': updatedArea.toJson(),
        });
      }
    }

    final activeEntryIds = List<String>.from(_activeTimeEntries.values);
    for (final entryId in activeEntryIds) {
      await _timeEntryManager.stopTimeEntry(entryId);
    }
    _activeTimeEntries.clear();
  }

  void _updateAreaInCache(LocationArea area) {
    final index = _areasCache.indexWhere((a) => a.id == area.id);
    if (index >= 0) {
      _areasCache[index] = area;
    } else {
      _areasCache.add(area);
    }
  }

  void _updateNotification({required String title, required String body}) {
    FlutterForegroundTask.updateService(
      notificationTitle: title,
      notificationText: body,
    );
  }

  void removeInactiveTimeEntries(List<String> inactiveIds) {
    if (inactiveIds.isEmpty) return;

    for (final id in inactiveIds) {
      final areaId = _findAreaIdByTimeEntryId(id);
      if (areaId != null) {
        _activeTimeEntries.remove(areaId);
        debugPrint('Removed inactive time entry $id from area $areaId');
      }
    }

    if (_activeTimeEntries.isEmpty) {
      _updateNotification(
        title: 'Location Tracking Active',
        body: 'Waiting to enter a tracking area...',
      );
    }
  }

  void removeCompletedTimeEntry(String timeEntryId) {
    final areaId = _findAreaIdByTimeEntryId(timeEntryId);
    if (areaId != null) {
      _activeTimeEntries.remove(areaId);
      debugPrint('Removed completed time entry $timeEntryId from area $areaId');

      if (_activeTimeEntries.isEmpty) {
        _updateNotification(
          title: 'Location Tracking Active',
          body: 'Waiting to enter a tracking area...',
        );
      }
    }
  }

  void updateTimeEntryId(String tempId, String realId) {
    final areaId = _findAreaIdByTimeEntryId(tempId);
    if (areaId != null) {
      _activeTimeEntries[areaId] = realId;
      debugPrint('Updated time entry ID from $tempId to $realId for area $areaId');
    }
  }

  String? _findAreaIdByTimeEntryId(String timeEntryId) {
    try {
      final areaId = _activeTimeEntries.entries
          .firstWhere((e) => e.value == timeEntryId, orElse: () => const MapEntry('', ''))
          .key;
      return areaId.isEmpty ? null : areaId;
    } catch (e) {
      debugPrint('Error finding area ID for time entry $timeEntryId: $e');
      return null;
    }
  }
}
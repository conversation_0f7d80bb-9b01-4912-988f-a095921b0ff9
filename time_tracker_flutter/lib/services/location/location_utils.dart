import 'package:latlong2/latlong.dart';
import 'package:flutter/foundation.dart';

class LocationUtils {
  static const double defaultRadius = 100.0;
  static const int defaultCooldown = 60;
  static const Duration statusCheckThrottle = Duration(milliseconds: 1000);

  /// Calculate distance between two points in meters
  static double calculateDistance(LatLng point1, LatLng point2) {
    const Distance distance = Distance();
    return distance.as(LengthUnit.Meter, point1, point2);
  }

  /// Format date to string (YYYY-MM-DD)
  static String formatDate(DateTime dateTime) =>
    '${dateTime.year}-${_twoDigits(dateTime.month)}-${_twoDigits(dateTime.day)}';

  /// Format time to string (HH:MM)
  static String formatTime(DateTime dateTime) =>
    '${_twoDigits(dateTime.hour)}:${_twoDigits(dateTime.minute)}';

  /// Format duration for display (HH:MM)
  static String formatDuration(Duration duration) {
    final hours = _twoDigits(duration.inHours);
    final minutes = _twoDigits(duration.inMinutes.remainder(60));
    return '$hours:$minutes';
  }

  /// Format tracking duration with appropriate units
  static String formatTrackingDuration(DateTime startTime) {
    final difference = DateTime.now().difference(startTime);
    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;
    final seconds = difference.inSeconds % 60;

    if (hours > 0) {
      return '$hours h ${minutes.toString().padLeft(2, '0')} min';
    } else if (minutes > 0) {
      return '${minutes.toString().padLeft(2, '0')} min ${seconds.toString().padLeft(2, '0')} sec';
    } else {
      return '${seconds.toString().padLeft(2, '0')} sec';
    }
  }

  /// Parse date and time strings to DateTime
  static DateTime parseDateTime(String dateStr, String? timeStr) {
    final dateParts = dateStr.split('-').map(int.parse).toList();

    if (timeStr == null) {
      return DateTime(dateParts[0], dateParts[1], dateParts[2]);
    }

    final timeParts = timeStr.split(':').map(int.parse).toList();
    return DateTime(
      dateParts[0], dateParts[1], dateParts[2],
      timeParts[0], timeParts[1]
    );
  }

  /// Check if an area is in cooldown period
  static bool isInCooldown(String areaId, int cooldownTime, Map<String, DateTime> lastAreaTimes) {
    if (lastAreaTimes[areaId] == null) {
      lastAreaTimes[areaId] = DateTime.now();
    }

    final cooldownEnd = lastAreaTimes[areaId]!.add(Duration(seconds: cooldownTime));

    debugPrint('TaskAreaProcessor: In cooldown period for ${areaId} until ${cooldownEnd}');
    return DateTime.now().isBefore(cooldownEnd);
  }

  static String _twoDigits(int n) => n.toString().padLeft(2, '0');
}
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:vibration/vibration.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'location_events.dart';

class BackgroundCommunication {
  final DatabaseService _databaseService = DatabaseService();
  final StreamController<LocationEvent> _eventController;
  final StreamController<TrackingStatus> _statusController;

  TrackingStatus _currentStatus = TrackingStatus(
    isActive: false,
    activeTimeEntries: {},
  );

  late final Map<String, Function(Map<String, dynamic>)> _commandHandlers;

  BackgroundCommunication(this._eventController, this._statusController) {
    _commandHandlers = {
      'requestData': (_) => _sendDataToBackground(),
      'saveArea': _handleSaveArea,
      'saveTimeEntry': _handleSaveTimeEntry,
      'getTimeEntry': _handleGetTimeEntry,
      'getActiveTimeEntries': (_) => _handleGetActiveTimeEntries(),
      'checkTimeEntries': _handleCheckTimeEntries,
      'getActiveTimeEntryForProject': _handleGetActiveTimeEntryForProject,
      'areaEntered': _handleAreaEntered,
      'areaExited': _handleAreaExited,
      'timeEntryCreated': _handleTimeEntryCreated,
      'timeEntryResumed': _handleTimeEntryResumed,
      'timeEntryUpdated': _handleTimeEntryUpdated,
    };
  }

  TrackingStatus get currentStatus => _currentStatus;

  void initialize() {
    FlutterForegroundTask.addTaskDataCallback((data) {
      if (data is Map<String, dynamic>) {
        _handleBackgroundEvent(data);
      }
    });
  }

  void _handleBackgroundEvent(Map<String, dynamic> data) {
    debugPrint('BackgroundCommunication: Received event: ${data.keys}');

    if (data.containsKey('isTracking')) {
      _updateTrackingStatus(data);
    }

    final command = data['command'] as String?;
    if (command != null) {
      final handler = _commandHandlers[command];
      if (handler != null) {
        handler(data);
      } else {
        debugPrint('Unknown command: $command');
      }
    }
  }

  void _updateTrackingStatus(Map<String, dynamic> data) {
    final wasActive = _currentStatus.isActive;
    final isActive = data['isTracking'] as bool? ?? false;
    final activeEntries = data.containsKey('activeTimeEntries')
        ? Map<String, String>.from(data['activeTimeEntries'] as Map<dynamic, dynamic>? ?? {})
        : <String, String>{};

    _currentStatus = TrackingStatus(
      isActive: isActive,
      activeTimeEntries: activeEntries,
      currentProjectName: data['currentProjectName'] as String?,
      trackingStartTime: _extractTrackingStartTime(data),
    );

    _statusController.add(_currentStatus);

    if (isActive != wasActive) {
      final eventType = isActive ? LocationEventType.trackingStarted : LocationEventType.trackingStopped;
      _eventController.add(LocationEvent(
        type: eventType,
        additionalData: data,
      ));
    }
  }

  DateTime? _extractTrackingStartTime(Map<String, dynamic> data) {
    final trackingStartTime = data['trackingStartTime'];
    if (trackingStartTime != null && trackingStartTime is int) {
      return DateTime.fromMillisecondsSinceEpoch(trackingStartTime);
    }
    return _currentStatus.trackingStartTime;
  }

  Future<void> _handleSaveArea(Map<String, dynamic> data) async {
    if (data.containsKey('area')) {
      final areaJson = data['area'] as Map<String, dynamic>;
      final area = LocationArea.fromJson(areaJson);
      await _databaseService.saveLocationArea(area);
      debugPrint('Saved area from background: ${area.name}');
    }
  }

  Future<void> _handleSaveTimeEntry(Map<String, dynamic> data) async {
    if (data.containsKey('timeEntry')) {
      final timeEntryJson = data['timeEntry'] as Map<String, dynamic>;
      final timeEntry = TimeEntry.fromJson(timeEntryJson);
      await _databaseService.saveTimeEntry(timeEntry);
      debugPrint('Saved time entry from background: ${timeEntry.id}');
    }
  }

  Future<void> _handleGetTimeEntry(Map<String, dynamic> data) async {
    if (data.containsKey('entryId') && data.containsKey('callback')) {
      final entryId = data['entryId'] as String;
      final callback = data['callback'] as String;
      final entry = await _databaseService.getTimeEntry(entryId);

      if (entry != null) {
        FlutterForegroundTask.sendDataToTask({
          'command': callback,
          'timeEntry': entry.toJson(),
        });
      }
    }
  }

  Future<void> _handleGetActiveTimeEntries() async {
    final allEntries = await _databaseService.getTimeEntries();
    final activeEntries = allEntries.where((entry) => entry.inProgress).toList();

    FlutterForegroundTask.sendDataToTask({
      'command': 'activeTimeEntries',
      'entries': activeEntries.map((e) => e.toJson()).toList(),
    });
  }

  Future<void> _handleCheckTimeEntries(Map<String, dynamic> data) async {
    if (data.containsKey('entryIds')) {
      final entryIds = (data['entryIds'] as List<dynamic>).cast<String>();
      final inactiveIds = <String>[];

      for (final id in entryIds) {
        final entry = await _databaseService.getTimeEntry(id);
        if (entry == null || !entry.inProgress) {
          inactiveIds.add(id);
        }
      }

      FlutterForegroundTask.sendDataToTask({
        'command': 'timeEntriesStatus',
        'inactiveIds': inactiveIds,
      });
    }
  }

  Future<void> _handleGetActiveTimeEntryForProject(Map<String, dynamic> data) async {
    if (data.containsKey('projectId')) {
      final projectId = data['projectId'] as String;
      final activeEntry = await _databaseService.getActiveTimeEntryForProject(projectId);

      FlutterForegroundTask.sendDataToTask({
        'command': 'activeTimeEntryForProject',
        'projectId': projectId,
        'activeEntry': activeEntry?.toJson(),
      });
    }
  }

  void _handleAreaEntered(Map<String, dynamic> data) {
    _eventController.add(LocationEvent(
      type: LocationEventType.areaEntered,
      projectId: data['projectId'] as String?,
      projectName: data['projectName'] as String?,
      areaId: data['areaId'] as String?,
      areaName: data['areaName'] as String?,
      trackingStartTime: _extractTrackingStartTime(data),
      additionalData: data,
    ));

    _triggerVibration();
  }

  void _handleAreaExited(Map<String, dynamic> data) {
    _eventController.add(LocationEvent(
      type: LocationEventType.areaExited,
      projectId: data['projectId'] as String?,
      projectName: data['projectName'] as String?,
      areaId: data['areaId'] as String?,
      areaName: data['areaName'] as String?,
      trackingStartTime: _currentStatus.trackingStartTime,
      additionalData: data,
    ));

    _triggerVibration();
  }

  void _handleTimeEntryCreated(Map<String, dynamic> data) =>
    _eventController.add(LocationEvent(
      type: LocationEventType.timeEntryCreated,
      projectId: data['projectId'] as String?,
      projectName: data['projectName'] as String?,
      timeEntryId: data['timeEntryId'] as String?,
      trackingStartTime: _extractTrackingStartTime(data),
      additionalData: data,
    ));

  void _handleTimeEntryResumed(Map<String, dynamic> data) =>
    _eventController.add(LocationEvent(
      type: LocationEventType.timeEntryResumed,
      projectId: data['projectId'] as String?,
      projectName: data['projectName'] as String?,
      timeEntryId: data['timeEntryId'] as String?,
      trackingStartTime: _extractTrackingStartTime(data),
      additionalData: data,
    ));

  void _handleTimeEntryUpdated(Map<String, dynamic> data) =>
    _eventController.add(LocationEvent(
      type: LocationEventType.timeEntryUpdated,
      projectId: data['projectId'] as String?,
      projectName: data['projectName'] as String?,
      timeEntryId: data['timeEntryId'] as String?,
      trackingStartTime: _currentStatus.trackingStartTime,
      additionalData: data,
    ));

  Future<void> _sendDataToBackground() async {
    try {
      final projects = await _databaseService.getProjects();
      final areas = await _databaseService.getLocationAreas();

      FlutterForegroundTask.sendDataToTask({
        'command': 'updateData',
        'projects': projects.map((p) => p.toJson()).toList(),
        'areas': areas.map((a) => a.toJson()).toList(),
      });

      debugPrint('Sent ${projects.length} projects and ${areas.length} areas to background');
    } catch (e) {
      debugPrint('Error sending data to background: $e');
    }
  }

  void dispose() {
    // Clean up if needed
  }

  void _triggerVibration() {
    Vibration.hasVibrator().then((hasVibrator) {
      if (hasVibrator) {
        Vibration.vibrate(duration: 150);
      }
    });
  }
}
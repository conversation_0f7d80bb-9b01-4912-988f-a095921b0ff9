import 'package:flutter/foundation.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'location_utils.dart';

class TaskTimeEntryManager {
  String? _currentProjectName;
  DateTime? _trackingStartTime;
  Function(String)? _onTimeEntryCompleted;
  Function(String, String)? _onTimeEntryIdUpdated; // tempId, realId

  final Map<String, String> _tempIdMapping = {}; // projectId -> tempId
  List<Project> _projectsCache = [];

  String? get currentProjectName => _currentProjectName;
  DateTime? get trackingStartTime => _trackingStartTime;

  void updateProjectsCache(List<Project> projects) =>
    _projectsCache = projects;

  void setTimeEntryCompletedCallback(Function(String) callback) =>
    _onTimeEntryCompleted = callback;

  void setTimeEntryIdUpdatedCallback(Function(String, String) callback) =>
    _onTimeEntryIdUpdated = callback;

  Future<String> startTimeEntry(Project project) async {
    debugPrint('TaskTimeEntryManager: Starting time entry for project ${project.name}');

    // Request the active time entry for this project from the database
    FlutterForegroundTask.sendDataToMain({
      'command': 'getActiveTimeEntryForProject',
      'projectId': project.id,
    });

    // Return a synchronous result for now - this will be corrected when response arrives
    // Store a temporary entry ID that will be replaced
    final tempId = 'temp-${project.id}-${DateTime.now().millisecondsSinceEpoch}';
    _tempIdMapping[project.id] = tempId;

    final now = DateTime.now();
    _trackingStartTime = now;
    _currentProjectName = project.name;

    return tempId;
  }

  String handleActiveTimeEntryResponse(String projectId, Map<String, dynamic>? activeEntryData) {
    final project = _projectsCache.firstWhere(
      (p) => p.id == projectId,
      orElse: () => Project(id: projectId, name: 'Unknown Project'),
    );

    final tempId = _tempIdMapping[projectId];

    if (activeEntryData != null) {
      return _resumeExistingEntry(project, activeEntryData, tempId);
    } else {
      return _createNewEntry(project, tempId);
    }
  }

  String _resumeExistingEntry(Project project, Map<String, dynamic> activeEntryData, String? tempId) {
    final timeEntry = TimeEntry.fromJson(activeEntryData);
    debugPrint('TaskTimeEntryManager: Resuming existing time entry ${timeEntry.id} for project ${project.name}');

    _trackingStartTime = LocationUtils.parseDateTime(timeEntry.date, timeEntry.start);
    _currentProjectName = project.name;

    _sendTrackingEvent('timeEntryResumed', project, timeEntry.id);
    _updateTempId(tempId, timeEntry.id);

    return timeEntry.id;
  }

  String _createNewEntry(Project project, String? tempId) {
    final now = DateTime.now();
    final timeEntry = TimeEntry(
      projectId: project.id,
      date: LocationUtils.formatDate(now),
      start: LocationUtils.formatTime(now),
      end: LocationUtils.formatTime(now),
      inProgress: true,
    );

    debugPrint('TaskTimeEntryManager: Creating new time entry ${timeEntry.id} for project ${project.name}');

    FlutterForegroundTask.sendDataToMain({
      'command': 'saveTimeEntry',
      'timeEntry': timeEntry.toJson(),
      'requestId': timeEntry.id,
    });

    _trackingStartTime = now;
    _currentProjectName = project.name;

    _sendTrackingEvent('timeEntryCreated', project, timeEntry.id);
    _updateTempId(tempId, timeEntry.id);

    return timeEntry.id;
  }

  void _sendTrackingEvent(String command, Project project, String timeEntryId) =>
    FlutterForegroundTask.sendDataToMain({
      'command': command,
      'projectId': project.id,
      'projectName': project.name,
      'timeEntryId': timeEntryId,
      'trackingStartTime': _trackingStartTime?.millisecondsSinceEpoch,
    });

  void _updateTempId(String? tempId, String realId) {
    if (tempId != null && _onTimeEntryIdUpdated != null) {
      _onTimeEntryIdUpdated!(tempId, realId);
    }
    _tempIdMapping.remove(_findProjectIdByTempId(tempId));
  }

  String? _findProjectIdByTempId(String? tempId) {
    if (tempId == null) return null;
    return _tempIdMapping.entries
        .where((entry) => entry.value == tempId)
        .map((entry) => entry.key)
        .firstOrNull;
  }

  Future<void> stopTimeEntry(String entryId) async {
    try {
      FlutterForegroundTask.sendDataToMain({
        'command': 'getTimeEntry',
        'entryId': entryId,
        'callback': 'stopTimeEntryCallback',
      });
    } catch (e) {
      debugPrint('Error stopping time entry: $e');
    }
  }

  void completeStopTimeEntry(TimeEntry entry) {
    try {
      final now = DateTime.now();
      final endTimeStr = LocationUtils.formatTime(now);

      final updatedEntry = entry.copyWith(
        end: endTimeStr,
        inProgress: false,
      );

      FlutterForegroundTask.sendDataToMain({
        'command': 'saveTimeEntry',
        'timeEntry': updatedEntry.toJson(),
      });

      FlutterForegroundTask.sendDataToMain({
        'command': 'timeEntryUpdated',
        'projectId': entry.projectId,
        'timeEntryId': entry.id,
      });

      // Always reset tracking state after a time entry has been stopped
      _currentProjectName = null;
      _trackingStartTime = null;
      // Notify area processor to remove this time entry from active tracking
      _onTimeEntryCompleted?.call(entry.id);
    } catch (e) {
      debugPrint('Error completing stop time entry: $e');
    }
  }

  String? _getProjectIdFromName(String projectName) {
    final project = _projectsCache.firstWhere(
      (p) => p.name == projectName,
      orElse: () => Project(id: '', name: ''),
    );
    return project.id.isNotEmpty ? project.id : null;
  }

  Future<void> updateActiveTimeEntries() async {
    try {
      // Request updates for active time entries from main isolate
      FlutterForegroundTask.sendDataToMain({
        'command': 'getActiveTimeEntries',
      });
    } catch (e) {
      debugPrint('Error updating active time entries: $e');
    }
  }

  void processActiveTimeEntries(List<dynamic> activeEntriesJson, List<Project> projectsCache) {
    try {
      if (activeEntriesJson.isEmpty) return;

      final activeEntries = activeEntriesJson
          .map((json) => TimeEntry.fromJson(json))
          .toList();

      final projectsMap = {for (var p in projectsCache) p.id: p};

      for (final entry in activeEntries) {
        final project = projectsMap[entry.projectId];
        if (project == null) {
          debugPrint('Project not found for time entry: ${entry.id}');
          continue;
        }

        if (_trackingStartTime == null || _currentProjectName == null) {
          _currentProjectName = project.name;
          _trackingStartTime = LocationUtils.parseDateTime(entry.date, entry.start);
        }
      }
    } catch (e) {
      debugPrint('Error processing active time entries: $e');
    }
  }

  void checkTimeEntries(List<String> entryIds) {
    if (entryIds.isEmpty) return;

    try {
      FlutterForegroundTask.sendDataToMain({
        'command': 'checkTimeEntries',
        'entryIds': entryIds,
      });
    } catch (e) {
      debugPrint('Error checking time entries: $e');
    }
  }

  void resetTracking() {
    _currentProjectName = null;
    _trackingStartTime = null;
  }
}
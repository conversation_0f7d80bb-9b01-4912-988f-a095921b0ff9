import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'adaptive_location_tracker.dart';
import 'motion_detector.dart';
import 'task_area_processor.dart';

class SmartLocationTracker {
  final TaskAreaProcessor _areaProcessor;

  AdaptiveLocationTracker? _adaptiveTracker;
  MotionDetector? _motionDetector;
  Timer? _stationaryTimer;
  Timer? _sleepModeTimer;

  bool _isActive = false;
  TrackingMode _currentMode = TrackingMode.stationary;
  DateTime? _lastLocationUpdate;
  LatLng? _lastKnownPosition;

  // Smart tracking parameters
  static const Duration _stationaryUpdateInterval = Duration(minutes: 5);
  static const Duration _sleepModeInterval = Duration(minutes: 15);
  static const Duration _nightStartTime = Duration(hours: 23); // 11 PM
  static const Duration _nightEndTime = Duration(hours: 6);    // 6 AM

  SmartLocationTracker(this._areaProcessor);

  bool get isActive => _isActive;
  TrackingMode get currentMode => _currentMode;

  Future<void> start() async {
    if (_isActive) return;

    _isActive = true;
    debugPrint('SmartLocationTracker: Starting smart location tracking');

    // Initialize motion detector
    _motionDetector = MotionDetector(onMotionChanged: _handleMotionChange);
    await _motionDetector?.start();

    // Initialize adaptive tracker
    _adaptiveTracker = AdaptiveLocationTracker(_areaProcessor);
    await _adaptiveTracker?.start();

    // Set initial tracking mode
    _updateTrackingMode();

    // Perform initial location check to mark areas if user is already in them
    await _performInitialLocationCheck();

    // Start sleep mode timer to check for night hours
    _sleepModeTimer = Timer.periodic(const Duration(minutes: 30), (_) => _updateTrackingMode());
  }

  void _handleMotionChange(bool isStationary) {
    debugPrint('SmartLocationTracker: Motion changed - stationary: $isStationary');

    if (isStationary) {
      _switchToStationaryMode();
    } else {
      _switchToNormalMode();
    }
  }

  void _switchToNormalMode() {
    if (_currentMode == TrackingMode.normal) return;

    debugPrint('SmartLocationTracker: Switching to normal tracking mode');
    _currentMode = TrackingMode.normal;

    // Cancel stationary timer
    _stationaryTimer?.cancel();
    _stationaryTimer = null;

    // Restart adaptive tracker if needed
    if (_adaptiveTracker?.isActive != true) {
      _adaptiveTracker?.start();
    }

    // Notify adaptive tracker of mode change
    _adaptiveTracker?.updateTrackingMode(TrackingMode.normal);
  }

  void _switchToStationaryMode() {
    if (_currentMode == TrackingMode.stationary) return;

    debugPrint('SmartLocationTracker: Switching to stationary tracking mode');
    _currentMode = TrackingMode.stationary;

    // Start periodic location checks while stationary
    _stationaryTimer = Timer.periodic(_stationaryUpdateInterval, (_) async {
      await _performStationaryLocationCheck();
    });

    // Notify adaptive tracker of mode change
    _adaptiveTracker?.updateTrackingMode(TrackingMode.stationary);
  }

  void _switchToSleepMode() {
    if (_currentMode == TrackingMode.sleep) return;

    debugPrint('SmartLocationTracker: Switching to sleep tracking mode');
    _currentMode = TrackingMode.sleep;

    // Cancel other timers
    _stationaryTimer?.cancel();
    _stationaryTimer = null;

    // Minimal location checks during sleep
    _stationaryTimer = Timer.periodic(_sleepModeInterval, (_) async {
      await _performStationaryLocationCheck();
    });

    // Notify adaptive tracker of mode change
    _adaptiveTracker?.updateTrackingMode(TrackingMode.sleep);
  }

  void _updateTrackingMode() {
    final now = DateTime.now();
    final timeOfDay = Duration(hours: now.hour, minutes: now.minute);

    // Check if it's night time (sleep mode)
    final isNightTime = timeOfDay >= _nightStartTime || timeOfDay <= _nightEndTime;

    if (isNightTime && _currentMode != TrackingMode.sleep) {
      _switchToSleepMode();
    } else if (!isNightTime && _currentMode == TrackingMode.sleep) {
      // Exit sleep mode, return to appropriate mode
      if (_motionDetector?.isStationary == true) {
        _switchToStationaryMode();
      } else {
        _switchToNormalMode();
      }
    }
  }

  Future<void> _performStationaryLocationCheck() async {
    if (!_isActive) return;

    try {
      debugPrint('SmartLocationTracker: Performing stationary location check');

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 15),
      );

      final userLocation = LatLng(position.latitude, position.longitude);

      // Check if user has moved significantly since last check
      if (_lastKnownPosition != null) {
        final distance = Geolocator.distanceBetween(
          _lastKnownPosition!.latitude,
          _lastKnownPosition!.longitude,
          userLocation.latitude,
          userLocation.longitude,
        );

        if (distance > 100) { // Moved more than 100m
          debugPrint('SmartLocationTracker: Significant movement detected ($distance m), switching to normal mode');
          _switchToNormalMode();
          return;
        }
      }

      _lastKnownPosition = userLocation;
      _lastLocationUpdate = DateTime.now();

      // Check areas with current position
      await _areaProcessor.checkAllAreas(userLocation);

    } catch (e) {
      debugPrint('SmartLocationTracker: Error during stationary location check: $e');
    }
  }

  Future<void> _performInitialLocationCheck() async {
    if (!_isActive) return;

    try {
      debugPrint('SmartLocationTracker: Performing initial location check');

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 20),
      );

      final userLocation = LatLng(position.latitude, position.longitude);
      _lastKnownPosition = userLocation;
      _lastLocationUpdate = DateTime.now();

      // Check all areas with current position to mark any active areas
      await _areaProcessor.checkAllAreas(userLocation);

      debugPrint('SmartLocationTracker: Initial location check completed at ${userLocation.latitude}, ${userLocation.longitude}');

    } catch (e) {
      debugPrint('SmartLocationTracker: Error during initial location check: $e');
      // If initial check fails, we'll rely on subsequent checks
    }
  }

  Future<void> stop() async {
    if (!_isActive) return;

    _isActive = false;
    _currentMode = TrackingMode.normal;

    debugPrint('SmartLocationTracker: Stopping smart location tracking');

    // Stop all components
    await _adaptiveTracker?.stop();
    _adaptiveTracker = null;

    _motionDetector?.stop();
    _motionDetector = null;

    // Cancel timers
    _stationaryTimer?.cancel();
    _stationaryTimer = null;

    _sleepModeTimer?.cancel();
    _sleepModeTimer = null;
  }

  void dispose() => stop();

  // Get current tracking statistics for debugging
  Map<String, dynamic> getTrackingStats() =>
    {
      'isActive': _isActive,
      'currentMode': _currentMode.toString(),
      'isStationary': _motionDetector?.isStationary ?? false,
      'lastLocationUpdate': _lastLocationUpdate?.toIso8601String(),
      'adaptiveTrackerActive': _adaptiveTracker?.isActive ?? false,
      'motionDetectorActive': _motionDetector?.isActive ?? false,
    };
}
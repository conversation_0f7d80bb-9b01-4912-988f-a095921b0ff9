/// Event types for location tracking
enum LocationEventType {
  trackingStarted,
  trackingStopped,
  areaEntered,
  areaExited,
  timeEntryCreated,
  timeEntryResumed,
  timeEntryUpdated,
  statusChanged,
}

/// Location tracking event data
class LocationEvent {
  final LocationEventType type;
  final String? projectId;
  final String? projectName;
  final String? areaId;
  final String? areaName;
  final String? timeEntryId;
  final DateTime timestamp;
  final DateTime? trackingStartTime;
  final Map<String, dynamic>? additionalData;

  LocationEvent({
    required this.type,
    this.projectId,
    this.projectName,
    this.areaId,
    this.areaName,
    this.timeEntryId,
    DateTime? timestamp,
    this.trackingStartTime,
    this.additionalData,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// Tracking status information
class TrackingStatus {
  final bool isActive;
  final Map<String, String> activeTimeEntries; // areaId -> timeEntryId
  final String? currentProjectName;
  final DateTime? trackingStartTime;

  TrackingStatus({
    required this.isActive,
    required this.activeTimeEntries,
    this.currentProjectName,
    this.trackingStartTime,
  });
}
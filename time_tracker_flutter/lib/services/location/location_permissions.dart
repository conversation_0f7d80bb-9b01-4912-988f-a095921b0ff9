import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

class LocationPermissions {
  /// Check if location tracking is supported on this platform
  static bool get isLocationSupported =>
      !kIsWeb && (Platform.isAndroid || Platform.isIOS || Platform.isWindows);

  /// Request location permission from the user
  static Future<bool> requestLocationPermission() async {
    if (!isLocationSupported) return false;

    final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) return false;

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    return permission != LocationPermission.deniedForever;
  }

  /// Request notification permission for foreground service
  static Future<void> requestNotificationPermission() async {
    final notificationPermission = await FlutterForegroundTask.checkNotificationPermission();
    if (notificationPermission != NotificationPermission.granted) {
      await FlutterForegroundTask.requestNotificationPermission();
    }
  }

  /// Request battery optimization exemption for Android
  static Future<void> requestBatteryOptimizationPermission() async {
    if (Platform.isAndroid) {
      if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
        await FlutterForegroundTask.requestIgnoreBatteryOptimization();
      }
    }
  }

  /// Request all necessary permissions for location tracking
  static Future<bool> requestAllPermissions() async {
    final locationGranted = await requestLocationPermission();
    if (!locationGranted) return false;

    await requestNotificationPermission();
    await requestBatteryOptimizationPermission();

    return true;
  }
}
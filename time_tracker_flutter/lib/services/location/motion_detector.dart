import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:sensors_plus/sensors_plus.dart';

class MotionDetector {
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  final Function(bool isStationary) onMotionChanged;

  bool _isActive = false;
  bool _isStationary = true;
  DateTime? _lastMovementTime;

  // Motion detection parameters
  static const double _movementThreshold = 2.5; // m/s² - threshold for detecting movement
  static const Duration _stationaryDuration = Duration(minutes: 2); // Time before considering stationary
  static const Duration _samplingInterval = Duration(seconds: 5); // How often to check accelerometer

  // Rolling average for smoothing accelerometer data
  final List<double> _accelerationHistory = [];
  static const int _historySize = 6; // 30 seconds of data at 5-second intervals

  Timer? _motionCheckTimer;

  MotionDetector({required this.onMotionChanged});

  bool get isStationary => _isStationary;
  bool get isActive => _isActive;

  Future<void> start() async {
    if (_isActive) return;

    _isActive = true;
    _lastMovementTime = DateTime.now();

    debugPrint('MotionDetector: Starting motion detection');

    // Start accelerometer monitoring
    _accelerometerSubscription = accelerometerEventStream(
      samplingPeriod: SensorInterval.normalInterval,
    ).listen(_handleAccelerometerEvent);

    // Start periodic motion checking
    _motionCheckTimer = Timer.periodic(_samplingInterval, (_) => _checkMotionState());

    // Immediately notify that user starts in stationary state
    debugPrint('MotionDetector: Initial state - user assumed stationary');
    onMotionChanged(true);
  }

  void _handleAccelerometerEvent(AccelerometerEvent event) {
    if (!_isActive) return;

    // Calculate magnitude of acceleration (removing gravity)
    final magnitude = math.sqrt(
      event.x * event.x + event.y * event.y + event.z * event.z
    );

    // Remove gravity (approximately 9.8 m/s²)
    final acceleration = (magnitude - 9.8).abs();

    // Add to rolling average
    _accelerationHistory.add(acceleration);
    if (_accelerationHistory.length > _historySize) {
      _accelerationHistory.removeAt(0);
    }
  }

  void _checkMotionState() {
    if (!_isActive || _accelerationHistory.isEmpty) return;

    // Calculate average acceleration over the history window
    final avgAcceleration = _accelerationHistory.reduce((a, b) => a + b) / _accelerationHistory.length;

    debugPrint('MotionDetector: Average acceleration: ${avgAcceleration.toStringAsFixed(2)} m/s²');

    final now = DateTime.now();

    if (avgAcceleration > _movementThreshold) {
      // Movement detected
      _lastMovementTime = now;

      if (_isStationary) {
        debugPrint('MotionDetector: Movement detected - user is no longer stationary');
        _isStationary = false;
        onMotionChanged(false);
      }
    } else {
      // Low acceleration detected
      if (_lastMovementTime != null &&
          now.difference(_lastMovementTime!) > _stationaryDuration &&
          !_isStationary) {
        debugPrint('MotionDetector: User appears to be stationary');
        _isStationary = true;
        onMotionChanged(true);
      }
    }
  }

  void stop() {
    if (!_isActive) return;

    _isActive = false;
    _isStationary = false;

    _accelerometerSubscription?.cancel();
    _accelerometerSubscription = null;

    _motionCheckTimer?.cancel();
    _motionCheckTimer = null;

    _accelerationHistory.clear();

    debugPrint('MotionDetector: Stopped motion detection');
  }

  void dispose() => stop();
}
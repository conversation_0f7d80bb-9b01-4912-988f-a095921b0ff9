import 'dart:async';
import 'package:flutter/foundation.dart';
import 'smart_location_tracker.dart';
import 'task_area_processor.dart';

class TaskPositionTracker {
  SmartLocationTracker? _smartTracker;
  final TaskAreaProcessor _areaProcessor;
  bool _isActive = false;

  TaskPositionTracker(this._areaProcessor);

  bool get isActive => _isActive;

  Future<void> start() async {
    if (_isActive) return;

    _isActive = true;
    debugPrint('TaskPositionTracker: Starting smart location tracking');

    _smartTracker = SmartLocationTracker(_areaProcessor);
    await _smartTracker!.start();
  }

  Future<void> stop() async {
    if (!_isActive) return;

    _isActive = false;
    debugPrint('TaskPositionTracker: Stopping smart location tracking');

    await _smartTracker?.stop();
    _smartTracker = null;
  }

  void dispose() {
    stop();
  }

  // Get tracking statistics for debugging
  Map<String, dynamic>? getTrackingStats() {
    return _smartTracker?.getTrackingStats();
  }
}
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Service for managing currency settings and preferences
class CurrencySettingsService {
  static const String _defaultCurrencyKey = 'default_currency';
  static const String _currencyLocaleKey = 'currency_locale';
  static const String _useSystemLocaleKey = 'use_system_locale_for_currency';

  final DatabaseService _databaseService = DatabaseService();

  /// Get the default currency code
  Future<String> getDefaultCurrency() async {
    final currency = await _databaseService.getSetting(_defaultCurrencyKey);
    if (currency != null && CurrencyService.isCurrencySupported(currency)) {
      return currency;
    }
    // Fallback to USD if no default is set or if the stored currency is not supported
    return 'USD';
  }

  /// Set the default currency code
  Future<void> setDefaultCurrency(String currencyCode) async {
    if (!CurrencyService.isCurrencySupported(currencyCode)) {
      throw ArgumentError('Currency $currencyCode is not supported');
    }
    await _databaseService.saveSetting(_defaultCurrencyKey, currencyCode);
  }

  /// Get the currency locale override (if any)
  Future<String?> getCurrencyLocale() async {
    return await _databaseService.getSetting(_currencyLocaleKey);
  }

  /// Set the currency locale override
  Future<void> setCurrencyLocale(String? localeString) async {
    if (localeString != null) {
      await _databaseService.saveSetting(_currencyLocaleKey, localeString);
    } else {
      await _databaseService.deleteSetting(_currencyLocaleKey);
    }
  }

  /// Check if system locale should be used for currency formatting
  Future<bool> shouldUseSystemLocale() async {
    final setting = await _databaseService.getSetting(_useSystemLocaleKey);
    return setting == 'true' || setting == null; // Default to true
  }

  /// Set whether to use system locale for currency formatting
  Future<void> setUseSystemLocale(bool useSystemLocale) async {
    await _databaseService.saveSetting(_useSystemLocaleKey, useSystemLocale.toString());
  }

  /// Get the effective locale for currency formatting
  Future<Locale> getEffectiveCurrencyLocale(BuildContext context) async {
    final useSystemLocale = await shouldUseSystemLocale();
    
    if (useSystemLocale) {
      return await LocaleDateUtils.getEffectiveLocale(context);
    } else {
      final customLocaleString = await getCurrencyLocale();
      if (customLocaleString != null && customLocaleString.isNotEmpty) {
        try {
          final parts = customLocaleString.split('_');
          if (parts.length == 2) {
            return Locale(parts[0], parts[1]);
          } else if (parts.length == 1) {
            return Locale(parts[0]);
          }
        } catch (e) {
          // If custom locale is invalid, fall back to system locale
        }
      }
      return await LocaleDateUtils.getEffectiveLocale(context);
    }
  }

  /// Format currency using the user's preferred settings
  Future<String> formatCurrencyWithSettings(
    double amount,
    String? currencyCode,
    BuildContext context,
  ) async {
    final effectiveCurrency = currencyCode ?? await getDefaultCurrency();
    final effectiveLocale = await getEffectiveCurrencyLocale(context);
    
    return CurrencyService.formatCurrency(amount, effectiveCurrency, effectiveLocale);
  }

  /// Get currency formatting preview with current settings
  Future<String> getCurrencyPreviewWithSettings(
    String currencyCode,
    BuildContext context,
  ) async {
    const exampleAmount = 1234.56;
    final effectiveLocale = await getEffectiveCurrencyLocale(context);
    
    return CurrencyService.formatCurrency(exampleAmount, currencyCode, effectiveLocale);
  }

  /// Validate currency and locale combination
  Future<bool> validateCurrencyLocaleCombo(String currencyCode, String? localeString) async {
    if (!CurrencyService.isCurrencySupported(currencyCode)) {
      return false;
    }

    Locale? locale;
    if (localeString != null && localeString.isNotEmpty) {
      try {
        final parts = localeString.split('_');
        if (parts.length == 2) {
          locale = Locale(parts[0], parts[1]);
        } else if (parts.length == 1) {
          locale = Locale(parts[0]);
        }
      } catch (e) {
        return false;
      }
    }

    return CurrencyService.validateCurrencyFormat(currencyCode, locale);
  }

  /// Get currency settings summary for display
  Future<Map<String, dynamic>> getCurrencySettingsSummary(BuildContext context) async {
    final defaultCurrency = await getDefaultCurrency();
    final currencyLocale = await getCurrencyLocale();
    final useSystemLocale = await shouldUseSystemLocale();
    final effectiveLocale = await getEffectiveCurrencyLocale(context);
    final preview = await getCurrencyPreviewWithSettings(defaultCurrency, context);

    return {
      'defaultCurrency': defaultCurrency,
      'currencyLocale': currencyLocale,
      'useSystemLocale': useSystemLocale,
      'effectiveLocale': effectiveLocale.toString(),
      'preview': preview,
      'currencyName': CurrencyService.getCurrencyName(defaultCurrency),
      'currencySymbol': CurrencyService.getCurrencySymbol(defaultCurrency),
    };
  }

  /// Reset currency settings to defaults
  Future<void> resetCurrencySettings() async {
    await _databaseService.deleteSetting(_defaultCurrencyKey);
    await _databaseService.deleteSetting(_currencyLocaleKey);
    await _databaseService.deleteSetting(_useSystemLocaleKey);
  }

  /// Import currency settings from a map (useful for backup/restore)
  Future<void> importCurrencySettings(Map<String, dynamic> settings) async {
    if (settings.containsKey('defaultCurrency')) {
      final currency = settings['defaultCurrency'] as String?;
      if (currency != null && CurrencyService.isCurrencySupported(currency)) {
        await setDefaultCurrency(currency);
      }
    }

    if (settings.containsKey('currencyLocale')) {
      await setCurrencyLocale(settings['currencyLocale'] as String?);
    }

    if (settings.containsKey('useSystemLocale')) {
      final useSystem = settings['useSystemLocale'];
      if (useSystem is bool) {
        await setUseSystemLocale(useSystem);
      } else if (useSystem is String) {
        await setUseSystemLocale(useSystem.toLowerCase() == 'true');
      }
    }
  }

  /// Export currency settings to a map (useful for backup/restore)
  Future<Map<String, dynamic>> exportCurrencySettings() async {
    return {
      'defaultCurrency': await getDefaultCurrency(),
      'currencyLocale': await getCurrencyLocale(),
      'useSystemLocale': await shouldUseSystemLocale(),
    };
  }

  /// Get recommended currency based on system locale
  Future<String> getRecommendedCurrency(BuildContext context) async {
    final locale = await LocaleDateUtils.getEffectiveLocale(context);
    
    // Map common locales to their typical currencies
    const localeToCurrency = {
      'en_US': 'USD',
      'en_GB': 'GBP',
      'en_CA': 'CAD',
      'en_AU': 'AUD',
      'de': 'EUR',
      'fr': 'EUR',
      'es': 'EUR',
      'it': 'EUR',
      'nl': 'EUR',
      'pt': 'EUR',
      'ja': 'JPY',
      'zh': 'CNY',
      'hi': 'INR',
      'pt_BR': 'BRL',
      'fr_CH': 'CHF',
      'de_CH': 'CHF',
    };

    // Try exact locale match first
    final exactMatch = localeToCurrency[locale.toString()];
    if (exactMatch != null) {
      return exactMatch;
    }

    // Try language code match
    final languageMatch = localeToCurrency[locale.languageCode];
    if (languageMatch != null) {
      return languageMatch;
    }

    // Default to USD
    return 'USD';
  }

  /// Auto-configure currency settings based on system locale
  Future<void> autoConfigureCurrencySettings(BuildContext context) async {
    final recommendedCurrency = await getRecommendedCurrency(context);
    await setDefaultCurrency(recommendedCurrency);
    await setUseSystemLocale(true);
  }

  /// Check if current settings are optimal for the user's locale
  Future<bool> areSettingsOptimalForLocale(BuildContext context) async {
    final currentCurrency = await getDefaultCurrency();
    final recommendedCurrency = await getRecommendedCurrency(context);
    final useSystemLocale = await shouldUseSystemLocale();

    return currentCurrency == recommendedCurrency && useSystemLocale;
  }

  /// Get currency validation errors for current settings
  Future<List<String>> getCurrencyValidationErrors(BuildContext context) async {
    final errors = <String>[];
    
    final defaultCurrency = await getDefaultCurrency();
    if (!CurrencyService.isCurrencySupported(defaultCurrency)) {
      errors.add('Default currency "$defaultCurrency" is not supported');
    }

    final currencyLocale = await getCurrencyLocale();
    if (currencyLocale != null) {
      final isValid = await validateCurrencyLocaleCombo(defaultCurrency, currencyLocale);
      if (!isValid) {
        errors.add('Currency and locale combination is not valid');
      }
    }

    return errors;
  }
}
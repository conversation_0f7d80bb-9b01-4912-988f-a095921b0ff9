import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

/// Service class for managing client operations including CRUD operations,
/// search, filtering, and validation functionality.
class ClientService {
  final DatabaseService _databaseService;

  ClientService({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService();

  /// Get all clients sorted by name
  Future<List<Client>> getClients() async {
    try {
      return await _databaseService.getClients();
    } catch (e) {
      debugPrint('Error getting clients: $e');
      rethrow;
    }
  }

  /// Get a specific client by ID
  Future<Client?> getClient(String id) async {
    try {
      if (id.trim().isEmpty) {
        throw ArgumentError('Client ID cannot be empty');
      }
      return await _databaseService.getClient(id);
    } catch (e) {
      debugPrint('Error getting client $id: $e');
      rethrow;
    }
  }

  /// Search clients by name, email, or phone number
  Future<List<Client>> searchClients(String query) async {
    try {
      return await _databaseService.searchClients(query);
    } catch (e) {
      debugPrint('Error searching clients with query "$query": $e');
      rethrow;
    }
  }

  /// Filter clients by various criteria
  Future<List<Client>> filterClients({
    String? nameContains,
    String? emailContains,
    String? phoneContains,
    String? countryEquals,
    bool? hasEmail,
    bool? hasPhone,
    bool? hasAddress,
    bool? hasTaxId,
  }) async {
    try {
      final allClients = await getClients();
      
      return allClients.where((client) {
        // Name filter
        if (nameContains != null && nameContains.isNotEmpty) {
          if (!client.name.toLowerCase().contains(nameContains.toLowerCase())) {
            return false;
          }
        }

        // Email filter
        if (emailContains != null && emailContains.isNotEmpty) {
          if (client.email == null || 
              !client.email!.toLowerCase().contains(emailContains.toLowerCase())) {
            return false;
          }
        }

        // Phone filter
        if (phoneContains != null && phoneContains.isNotEmpty) {
          if (client.phone == null || !client.phone!.contains(phoneContains)) {
            return false;
          }
        }

        // Country filter
        if (countryEquals != null && countryEquals.isNotEmpty) {
          if (client.address == null || 
              client.address!.country.toLowerCase() != countryEquals.toLowerCase()) {
            return false;
          }
        }

        // Has email filter
        if (hasEmail != null) {
          final clientHasEmail = client.email != null && client.email!.isNotEmpty;
          if (hasEmail != clientHasEmail) return false;
        }

        // Has phone filter
        if (hasPhone != null) {
          final clientHasPhone = client.phone != null && client.phone!.isNotEmpty;
          if (hasPhone != clientHasPhone) return false;
        }

        // Has address filter
        if (hasAddress != null) {
          final clientHasAddress = client.address != null;
          if (hasAddress != clientHasAddress) return false;
        }

        // Has tax ID filter
        if (hasTaxId != null) {
          final clientHasTaxId = client.taxId != null && client.taxId!.isNotEmpty;
          if (hasTaxId != clientHasTaxId) return false;
        }

        return true;
      }).toList();
    } catch (e) {
      debugPrint('Error filtering clients: $e');
      rethrow;
    }
  }

  /// Save a client (create or update)
  Future<void> saveClient(Client client) async {
    try {
      // Validate client before saving
      final validationResult = validateClient(client);
      if (!validationResult.isValid) {
        throw ArgumentError('Client validation failed: ${validationResult.errors.join(', ')}');
      }

      await _databaseService.saveClient(client);
    } catch (e) {
      debugPrint('Error saving client ${client.name}: $e');
      rethrow;
    }
  }

  /// Create a new client
  Future<Client> createClient({
    required String name,
    String? email,
    String? phone,
    Address? address,
    String? taxId,
    String? notes,
  }) async {
    try {
      final client = Client(
        name: name,
        email: email,
        phone: phone,
        address: address,
        taxId: taxId,
        notes: notes,
      );

      await saveClient(client);
      return client;
    } catch (e) {
      debugPrint('Error creating client $name: $e');
      rethrow;
    }
  }

  /// Update an existing client
  Future<Client> updateClient(
    String clientId, {
    String? name,
    String? email,
    String? phone,
    Address? address,
    String? taxId,
    String? notes,
  }) async {
    try {
      final existingClient = await getClient(clientId);
      if (existingClient == null) {
        throw ArgumentError('Client with ID $clientId not found');
      }

      final updatedClient = existingClient.copyWith(
        name: name,
        email: email,
        phone: phone,
        address: address,
        taxId: taxId,
        notes: notes,
      );

      await saveClient(updatedClient);
      return updatedClient;
    } catch (e) {
      debugPrint('Error updating client $clientId: $e');
      rethrow;
    }
  }

  /// Delete a client
  Future<void> deleteClient(String id) async {
    try {
      if (id.trim().isEmpty) {
        throw ArgumentError('Client ID cannot be empty');
      }

      // Check if client exists
      final client = await getClient(id);
      if (client == null) {
        throw ArgumentError('Client with ID $id not found');
      }

      await _databaseService.deleteClient(id);
    } catch (e) {
      debugPrint('Error deleting client $id: $e');
      rethrow;
    }
  }

  /// Check if a client can be safely deleted (no associated invoices)
  Future<bool> canDeleteClient(String clientId) async {
    try {
      final invoices = await _databaseService.getInvoicesByClient(clientId);
      return invoices.isEmpty;
    } catch (e) {
      debugPrint('Error checking if client $clientId can be deleted: $e');
      return false;
    }
  }

  /// Get clients that have invoices
  Future<List<Client>> getClientsWithInvoices() async {
    try {
      final allClients = await getClients();
      final clientsWithInvoices = <Client>[];

      for (final client in allClients) {
        final invoices = await _databaseService.getInvoicesByClient(client.id);
        if (invoices.isNotEmpty) {
          clientsWithInvoices.add(client);
        }
      }

      return clientsWithInvoices;
    } catch (e) {
      debugPrint('Error getting clients with invoices: $e');
      rethrow;
    }
  }

  /// Get clients without invoices
  Future<List<Client>> getClientsWithoutInvoices() async {
    try {
      final allClients = await getClients();
      final clientsWithoutInvoices = <Client>[];

      for (final client in allClients) {
        final invoices = await _databaseService.getInvoicesByClient(client.id);
        if (invoices.isEmpty) {
          clientsWithoutInvoices.add(client);
        }
      }

      return clientsWithoutInvoices;
    } catch (e) {
      debugPrint('Error getting clients without invoices: $e');
      rethrow;
    }
  }

  /// Validate client data
  ClientValidationResult validateClient(Client client) {
    final errors = <String>[];

    // Validate name (required)
    if (client.name.trim().isEmpty) {
      errors.add('Client name is required');
    }

    // Validate email format if provided
    if (client.email != null && client.email!.isNotEmpty) {
      if (!isValidEmail(client.email!)) {
        errors.add('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (client.phone != null && client.phone!.isNotEmpty) {
      if (!isValidPhone(client.phone!)) {
        errors.add('Invalid phone number format');
      }
    }

    // Validate tax ID format if provided
    if (client.taxId != null && client.taxId!.isNotEmpty) {
      if (!isValidTaxId(client.taxId!)) {
        errors.add('Invalid tax ID format');
      }
    }

    // Validate address if provided
    if (client.address != null) {
      if (!client.address!.isValid()) {
        errors.add('Invalid address: missing required fields');
      }
    }

    return ClientValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Validate email format
  bool isValidEmail(String email) {
    if (email.trim().isEmpty) return false;
    
    // Basic email regex pattern
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      caseSensitive: false,
    );
    
    return emailRegex.hasMatch(email.trim());
  }

  /// Validate phone number format
  bool isValidPhone(String phone) {
    if (phone.trim().isEmpty) return false;
    
    // Remove all non-digit characters for validation
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Phone number should have at least 7 digits and at most 15 digits
    // This covers most international phone number formats
    return digitsOnly.length >= 7 && digitsOnly.length <= 15;
  }

  /// Validate tax ID format
  bool isValidTaxId(String taxId) {
    if (taxId.trim().isEmpty) return false;
    
    // Basic tax ID validation - should contain alphanumeric characters
    // and common separators (hyphens, spaces)
    final taxIdRegex = RegExp(r'^[a-zA-Z0-9\s\-]{3,20}$');
    
    return taxIdRegex.hasMatch(taxId.trim());
  }

  /// Get client selection options for UI components
  Future<List<ClientSelectionOption>> getClientSelectionOptions({
    String? searchQuery,
    bool includeCreateNew = true,
  }) async {
    try {
      final clients = searchQuery != null && searchQuery.isNotEmpty
          ? await searchClients(searchQuery)
          : await getClients();

      final options = clients.map((client) => ClientSelectionOption(
        client: client,
        displayText: _formatClientDisplayText(client),
        isCreateNew: false,
      )).toList();

      // Add "Create New" option if enabled and there's a search query
      if (includeCreateNew && searchQuery != null && searchQuery.isNotEmpty) {
        options.insert(0, ClientSelectionOption(
          client: null,
          displayText: 'Create new client: "$searchQuery"',
          isCreateNew: true,
          suggestedName: searchQuery,
        ));
      }

      return options;
    } catch (e) {
      debugPrint('Error getting client selection options: $e');
      rethrow;
    }
  }

  /// Format client display text for selection components
  String _formatClientDisplayText(Client client) {
    final parts = <String>[client.name];
    
    if (client.email != null && client.email!.isNotEmpty) {
      parts.add(client.email!);
    }
    
    if (client.phone != null && client.phone!.isNotEmpty) {
      parts.add(client.phone!);
    }

    return parts.join(' • ');
  }

  /// Get client statistics
  Future<ClientStatistics> getClientStatistics() async {
    try {
      final allClients = await getClients();
      final clientsWithInvoices = await getClientsWithInvoices();
      
      int totalWithEmail = 0;
      int totalWithPhone = 0;
      int totalWithAddress = 0;
      int totalWithTaxId = 0;

      for (final client in allClients) {
        if (client.email != null && client.email!.isNotEmpty) totalWithEmail++;
        if (client.phone != null && client.phone!.isNotEmpty) totalWithPhone++;
        if (client.address != null) totalWithAddress++;
        if (client.taxId != null && client.taxId!.isNotEmpty) totalWithTaxId++;
      }

      return ClientStatistics(
        totalClients: allClients.length,
        clientsWithInvoices: clientsWithInvoices.length,
        clientsWithoutInvoices: allClients.length - clientsWithInvoices.length,
        clientsWithEmail: totalWithEmail,
        clientsWithPhone: totalWithPhone,
        clientsWithAddress: totalWithAddress,
        clientsWithTaxId: totalWithTaxId,
      );
    } catch (e) {
      debugPrint('Error getting client statistics: $e');
      rethrow;
    }
  }

  /// Duplicate client check
  Future<List<Client>> findPotentialDuplicates(Client client) async {
    try {
      final allClients = await getClients();
      final duplicates = <Client>[];

      for (final existingClient in allClients) {
        // Skip the same client (when updating)
        if (existingClient.id == client.id) continue;

        // Check for exact name match
        if (existingClient.name.toLowerCase() == client.name.toLowerCase()) {
          duplicates.add(existingClient);
          continue;
        }

        // Check for email match
        if (client.email != null && 
            client.email!.isNotEmpty && 
            existingClient.email != null &&
            existingClient.email!.toLowerCase() == client.email!.toLowerCase()) {
          duplicates.add(existingClient);
          continue;
        }

        // Check for phone match
        if (client.phone != null && 
            client.phone!.isNotEmpty && 
            existingClient.phone != null &&
            _normalizePhone(existingClient.phone!) == _normalizePhone(client.phone!)) {
          duplicates.add(existingClient);
          continue;
        }
      }

      return duplicates;
    } catch (e) {
      debugPrint('Error finding potential duplicates: $e');
      rethrow;
    }
  }

  /// Normalize phone number for comparison
  String _normalizePhone(String phone) {
    return phone.replaceAll(RegExp(r'[^\d]'), '');
  }
}

/// Result of client validation
class ClientValidationResult {
  final bool isValid;
  final List<String> errors;

  const ClientValidationResult({
    required this.isValid,
    required this.errors,
  });
}

/// Client selection option for UI components
class ClientSelectionOption {
  final Client? client;
  final String displayText;
  final bool isCreateNew;
  final String? suggestedName;

  const ClientSelectionOption({
    required this.client,
    required this.displayText,
    required this.isCreateNew,
    this.suggestedName,
  });
}

/// Client statistics for dashboard/reporting
class ClientStatistics {
  final int totalClients;
  final int clientsWithInvoices;
  final int clientsWithoutInvoices;
  final int clientsWithEmail;
  final int clientsWithPhone;
  final int clientsWithAddress;
  final int clientsWithTaxId;

  const ClientStatistics({
    required this.totalClients,
    required this.clientsWithInvoices,
    required this.clientsWithoutInvoices,
    required this.clientsWithEmail,
    required this.clientsWithPhone,
    required this.clientsWithAddress,
    required this.clientsWithTaxId,
  });

  double get percentageWithInvoices => 
      totalClients > 0 ? (clientsWithInvoices / totalClients) * 100 : 0;

  double get percentageWithEmail => 
      totalClients > 0 ? (clientsWithEmail / totalClients) * 100 : 0;

  double get percentageWithPhone => 
      totalClients > 0 ? (clientsWithPhone / totalClients) * 100 : 0;

  double get percentageWithAddress => 
      totalClients > 0 ? (clientsWithAddress / totalClients) * 100 : 0;

  double get percentageWithTaxId => 
      totalClients > 0 ? (clientsWithTaxId / totalClients) * 100 : 0;
}
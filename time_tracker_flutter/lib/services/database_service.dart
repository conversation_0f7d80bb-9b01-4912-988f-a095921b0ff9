import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/cloud_backup_service.dart';
import 'package:time_tracker_flutter/services/service_initializer.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';

class DatabaseService {
  static const String projectBoxName = 'projects';
  static const String timeEntryBoxName = 'timeEntries';
  static const String settingsBoxName = 'settings';
  static const String locationAreaBoxName = 'locationAreas';
  static const String invoiceBoxName = 'invoices';
  static const String clientBoxName = 'clients';
  static const int schemaVersion = 1;

  // Singleton pattern with ServiceInitializer
  static final DatabaseService _instance = DatabaseService._internal();
  static final _initializer = ServiceInitializer<DatabaseService>(() => _instance._initialize());

  factory DatabaseService() => _instance;
  DatabaseService._internal();

  late Box<Project> _projectBox;
  late Box<TimeEntry> _timeEntryBox;
  late Box<dynamic> _settingsBox;
  late Box<LocationArea> _locationAreaBox;
  late Box<Invoice> _invoiceBox;
  late Box<Client> _clientBox;
  CloudBackupService? _cloudBackupService;
  bool _isRestoring = false;

  // Listeners for data changes
  final List<Function()> _dataChangeListeners = [];

  bool get isInitialized => _initializer.isInitialized;

  // Add a listener for data changes
  void addDataChangeListener(Function() listener) {
    _dataChangeListeners.add(listener);
  }

  // Remove a listener
  void removeDataChangeListener(Function() listener) {
    _dataChangeListeners.remove(listener);
  }

  // Notify all listeners of data changes
  void notifyDataChange() {
    for (final listener in _dataChangeListeners) {
      try {
        listener();
      } catch (e) {
        debugPrint('Error in data change listener: $e');
      }
    }
  }

  // Core initialization method that creates a proper instance
  Future<DatabaseService> _initialize() async {
    try {
      // Register adapters if not already registered
      try {
        if (!Hive.isAdapterRegistered(0)) {
          Hive.registerAdapter(ProjectAdapter());
        }
        if (!Hive.isAdapterRegistered(1)) {
          Hive.registerAdapter(TimeEntryAdapter());
        }
        if (!Hive.isAdapterRegistered(3)) {
          Hive.registerAdapter(LocationAreaAdapter());
        }
        if (!Hive.isAdapterRegistered(4)) {
          Hive.registerAdapter(InvoiceAdapter());
        }
        if (!Hive.isAdapterRegistered(5)) {
          Hive.registerAdapter(ClientAdapter());
        }
        if (!Hive.isAdapterRegistered(6)) {
          Hive.registerAdapter(InvoiceLineItemAdapter());
        }
        if (!Hive.isAdapterRegistered(7)) {
          Hive.registerAdapter(AddressAdapter());
        }
        if (!Hive.isAdapterRegistered(8)) {
          Hive.registerAdapter(InvoiceStatusAdapter());
        }
        if (!Hive.isAdapterRegistered(9)) {
          Hive.registerAdapter(InvoiceLineItemTypeAdapter());
        }
        if (!Hive.isAdapterRegistered(10)) {
          Hive.registerAdapter(InvoiceTemplateAdapter());
        }
        if (!Hive.isAdapterRegistered(11)) {
          Hive.registerAdapter(BusinessInfoAdapter());
        }
        if (!Hive.isAdapterRegistered(12)) {
          Hive.registerAdapter(InvoiceSettingsAdapter());
        }
      } catch (e) {
        debugPrint('Error registering adapters: $e');
      }

      // Open boxes
      _projectBox = await Hive.openBox<Project>(projectBoxName);
      _timeEntryBox = await Hive.openBox<TimeEntry>(timeEntryBoxName);
      _settingsBox = await Hive.openBox(settingsBoxName);
      _locationAreaBox = await Hive.openBox<LocationArea>(locationAreaBoxName);
      _invoiceBox = await Hive.openBox<Invoice>(invoiceBoxName);
      _clientBox = await Hive.openBox<Client>(clientBoxName);

      // Initialize cloud backup service
      try {
        await CloudBackupService.initializeService();
        _cloudBackupService = CloudBackupService();
      } catch (e) {
        debugPrint('Error initializing cloud backup service: $e');
        // Continue even if cloud backup initialization fails
      }

      return this;
    } catch (e) {
      debugPrint('Error initializing database service: $e');
      rethrow;
    }
  }

  // Project methods
  Future<List<Project>> getProjects() async {
    return _initializer.run((service) => Future.value(_projectBox.values.toList()));
  }

  Future<Project?> getProject(String id) async {
    return _initializer.run((service) => Future.value(_projectBox.get(id)));
  }

  Future<void> saveProject(Project project) async {
    return _initializer.run((service) async {
      await _projectBox.put(project.id, project);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteProject(String id) async {
    return _initializer.run((service) async {
      await _projectBox.delete(id);

      // Delete all time entries for this project
      final entriesToDelete = _timeEntryBox.values
          .where((entry) => entry.projectId == id)
          .map((entry) => entry.id)
          .toList();

      for (final entryId in entriesToDelete) {
        await _timeEntryBox.delete(entryId);
      }

      // Delete all location areas for this project
      final areasToDelete = _locationAreaBox.values
          .where((area) => area.projectId == id)
          .map((area) => area.id)
          .toList();

      for (final areaId in areasToDelete) {
        await _locationAreaBox.delete(areaId);
      }

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  // Location area methods
  Future<List<LocationArea>> getLocationAreas({String? projectId}) async {
    return _initializer.run((service) async {
      var areas = _locationAreaBox.values;

      if (projectId != null) {
        areas = areas.where((area) => area.projectId == projectId);
      }

      return areas.toList();
    });
  }

  Future<LocationArea?> getLocationArea(String id) async {
    return _initializer.run((service) => Future.value(_locationAreaBox.get(id)));
  }

  Future<void> saveLocationArea(LocationArea area) async {
    return _initializer.run((service) async {
      await _locationAreaBox.put(area.id, area);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteLocationArea(String id) async {
    return _initializer.run((service) async {
      await _locationAreaBox.delete(id);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  // Time entry methods
  Future<List<TimeEntry>> getTimeEntries({
    String? projectId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return _initializer.run((service) async {
      var entries = _timeEntryBox.values;

      if (projectId != null) {
        entries = entries.where((entry) => entry.projectId == projectId);
      }

      if (startDate != null) {
        entries = entries.where((entry) {
          try {
            final entryDate = DateTime.parse(entry.date);
            return !entryDate.isBefore(startDate);
          } catch (_) {
            return false; // Ignore entries with invalid date format
          }
        });
      }

      if (endDate != null) {
        // Add one day to endDate to make it inclusive
        final inclusiveEndDate = endDate.add(const Duration(days: 1));
        entries = entries.where((entry) {
           try {
            final entryDate = DateTime.parse(entry.date);
            return entryDate.isBefore(inclusiveEndDate);
          } catch (_) {
            return false; // Ignore entries with invalid date format
          }
        });
      }

      return entries.toList();
    });
  }

  Future<TimeEntry?> getTimeEntry(String id) async {
    return _initializer.run((service) => Future.value(_timeEntryBox.get(id)));
  }

  Future<List<TimeEntry>> getActiveTimeEntries({String? projectId}) async {
    return _initializer.run((service) async {
      var entries = _timeEntryBox.values.where((entry) => entry.inProgress);

      if (projectId != null) {
        entries = entries.where((entry) => entry.projectId == projectId);
      }

      return entries.toList();
    });
  }

  Future<TimeEntry?> getActiveTimeEntryForProject(String projectId) async {
    return _initializer.run((service) async {
      final activeEntries = _timeEntryBox.values.where((entry) =>
        entry.projectId == projectId && entry.inProgress
      );

      return activeEntries.isNotEmpty ? activeEntries.first : null;
    });
  }

  Future<void> saveTimeEntry(TimeEntry entry) async {
    return _initializer.run((service) async {

      if (entry.start != null && entry.end != null) {
        final project = await getProject(entry.projectId);
        final roundingMinutes = project?.roundingMinutes;

        entry = entry.copyWith(
          start: roundDateTime(entry.start!, roundingMinutes),
          end: roundDateTime(entry.end!, roundingMinutes),
        );
      }

      await _timeEntryBox.put(entry.id, entry);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteTimeEntry(String id) async {
    return _initializer.run((service) async {
      await _timeEntryBox.delete(id);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteMultipleTimeEntries(List<String> ids) async {
    return _initializer.run((service) async {
      for (final id in ids) {
        await _timeEntryBox.delete(id);
      }

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  // Invoice methods
  Future<List<Invoice>> getInvoices({
    String? clientId,
    DateTime? startDate,
    DateTime? endDate,
    InvoiceStatus? status,
  }) async {
    return _initializer.run((service) async {
      var invoices = _invoiceBox.values;

      if (clientId != null) {
        invoices = invoices.where((invoice) => invoice.clientId == clientId);
      }

      if (startDate != null) {
        invoices = invoices.where((invoice) => !invoice.issueDate.isBefore(startDate));
      }

      if (endDate != null) {
        // Add one day to endDate to make it inclusive
        final inclusiveEndDate = endDate.add(const Duration(days: 1));
        invoices = invoices.where((invoice) => invoice.issueDate.isBefore(inclusiveEndDate));
      }

      if (status != null) {
        invoices = invoices.where((invoice) => invoice.status == status);
      }

      return invoices.toList()..sort((a, b) => b.issueDate.compareTo(a.issueDate));
    });
  }

  Future<Invoice?> getInvoice(String id) async {
    return _initializer.run((service) => Future.value(_invoiceBox.get(id)));
  }

  Future<Invoice?> getInvoiceByNumber(String invoiceNumber) async {
    return _initializer.run((service) async {
      final invoices = _invoiceBox.values.where((invoice) => invoice.invoiceNumber == invoiceNumber);
      return invoices.isNotEmpty ? invoices.first : null;
    });
  }

  Future<List<Invoice>> getInvoicesByDateRange(DateTime startDate, DateTime endDate) async {
    return getInvoices(startDate: startDate, endDate: endDate);
  }

  Future<List<Invoice>> getInvoicesByClient(String clientId) async {
    return getInvoices(clientId: clientId);
  }

  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    return getInvoices(status: status);
  }

  Future<List<Invoice>> getOverdueInvoices() async {
    return _initializer.run((service) async {
      final now = DateTime.now();
      return _invoiceBox.values.where((invoice) {
        return invoice.dueDate != null &&
               invoice.dueDate!.isBefore(now) &&
               invoice.status != InvoiceStatus.paid &&
               invoice.status != InvoiceStatus.cancelled;
      }).toList()..sort((a, b) => a.dueDate!.compareTo(b.dueDate!));
    });
  }

  Future<void> saveInvoice(Invoice invoice) async {
    return _initializer.run((service) async {
      await _invoiceBox.put(invoice.id, invoice);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteInvoice(String id) async {
    return _initializer.run((service) async {
      await _invoiceBox.delete(id);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> updateInvoiceStatus(String invoiceId, InvoiceStatus status) async {
    return _initializer.run((service) async {
      final invoice = await getInvoice(invoiceId);
      if (invoice != null) {
        final updatedInvoice = invoice.copyWith(status: status);
        await saveInvoice(updatedInvoice);
      }
    });
  }

  /// Gets the IDs of time entries that are already billed (included in paid invoices)
  Future<Set<String>> getBilledTimeEntryIds() async {
    return _initializer.run((service) async {
      final paidInvoices = _invoiceBox.values.where((invoice) => 
        invoice.status == InvoiceStatus.paid
      ).toList();
      
      final billedIds = <String>{};
      for (final invoice in paidInvoices) {
        billedIds.addAll(invoice.timeEntryIds);
      }
      
      return billedIds;
    });
  }

  // Client methods
  Future<List<Client>> getClients() async {
    return _initializer.run((service) => Future.value(_clientBox.values.toList()..sort((a, b) => a.name.compareTo(b.name))));
  }

  Future<Client?> getClient(String id) async {
    return _initializer.run((service) => Future.value(_clientBox.get(id)));
  }

  Future<List<Client>> searchClients(String query) async {
    return _initializer.run((service) async {
      if (query.trim().isEmpty) {
        return getClients();
      }

      final lowerQuery = query.toLowerCase();
      return _clientBox.values.where((client) {
        return client.name.toLowerCase().contains(lowerQuery) ||
               (client.email?.toLowerCase().contains(lowerQuery) ?? false) ||
               (client.phone?.contains(query) ?? false);
      }).toList()..sort((a, b) => a.name.compareTo(b.name));
    });
  }

  Future<void> saveClient(Client client) async {
    return _initializer.run((service) async {
      await _clientBox.put(client.id, client);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> deleteClient(String id) async {
    return _initializer.run((service) async {
      // Check if client has any invoices
      final clientInvoices = await getInvoicesByClient(id);
      if (clientInvoices.isNotEmpty) {
        throw Exception('Cannot delete client with existing invoices. Delete invoices first.');
      }

      await _clientBox.delete(id);

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  // Backup and restore
  Future<BackupData> exportDatabase() async {
    return _initializer.run((service) async {
      final projects = _projectBox.values.toList();
      final locationAreas = _locationAreaBox.values.toList();
      final invoices = _invoiceBox.values.toList();
      final clients = _clientBox.values.toList();
      final Map<String, List<TimeEntry>> timeEntries = {};

      for (final project in projects) {
        timeEntries[project.id] = _timeEntryBox.values
            .where((entry) => entry.projectId == project.id)
            .toList();
      }

      return BackupData(
        projects: projects,
        timeEntries: timeEntries,
        locationAreas: locationAreas,
        invoices: invoices,
        clients: clients,
        schemaVersion: 3,
        timestamp: DateTime.now().toUtc().toIso8601String(),
      );
    });
  }

  Future<void> importDatabase(BackupData backupData) async {
    return _initializer.run((service) async {
      debugPrint('Importing database with ${backupData.projects.length} projects, ${backupData.locationAreas.length} location areas, ${backupData.invoices.length} invoices, and ${backupData.clients.length} clients');

      // Clear existing data
      await _projectBox.clear();
      await _timeEntryBox.clear();
      await _locationAreaBox.clear();
      await _invoiceBox.clear();
      await _clientBox.clear();
      debugPrint('Cleared existing data');

      // Import projects
      for (final project in backupData.projects) {
        await _projectBox.put(project.id, project);
      }
      debugPrint('Imported ${backupData.projects.length} projects');

      // Import time entries
      int totalEntries = 0;
      for (final projectId in backupData.timeEntries.keys) {
        final entries = backupData.timeEntries[projectId]!;
        for (final entry in entries) {
          await _timeEntryBox.put(entry.id, entry);
        }
        totalEntries += entries.length;
      }
      debugPrint('Imported $totalEntries time entries');

      // Import location areas
      for (final area in backupData.locationAreas) {
        await _locationAreaBox.put(area.id, area);
      }
      debugPrint('Imported ${backupData.locationAreas.length} location areas');

      // Import clients
      for (final client in backupData.clients) {
        await _clientBox.put(client.id, client);
      }
      debugPrint('Imported ${backupData.clients.length} clients');

      // Import invoices
      for (final invoice in backupData.invoices) {
        await _invoiceBox.put(invoice.id, invoice);
      }
      debugPrint('Imported ${backupData.invoices.length} invoices');

      // Notify listeners that data has changed
      notifyDataChange();
      debugPrint('Notified listeners of data change after import');
    });
  }

  Future<void> saveBackupToFile(String filePath) async {
    return _initializer.run((service) async {
      final backupData = await exportDatabase();
      final jsonString = jsonEncode(backupData.toJson());
      final file = File(filePath);
      await file.writeAsString(jsonString);
    });
  }

  Future<void> restoreFromFile(String filePath) async {
    return _initializer.run((service) async {
      final file = File(filePath);
      final jsonString = await file.readAsString();
      final jsonData = jsonDecode(jsonString);
      final backupData = BackupData.fromJson(jsonData);
      await importDatabase(backupData);
    });
  }

  // Settings
  Future<void> setAutoBackup(bool enabled) async {
    return _initializer.run((service) async {
      await _settingsBox.put('autoBackup', enabled);
    });
  }

  Future<bool> isAutoBackupEnabled() async {
    return _initializer.run((service) async {
      return _settingsBox.get('autoBackup', defaultValue: false);
    });
  }

  Future<void> setCloudAutoBackup(bool enabled) async {
    return _initializer.run((service) async {
      await _settingsBox.put('cloudAutoBackup', enabled);
    });
  }

  Future<bool> isCloudAutoBackupEnabled() async {
    return _initializer.run((service) async {
      return _settingsBox.get('cloudAutoBackup', defaultValue: false);
    });
  }

  // Date format settings
  Future<void> setCustomDateFormat(String? format) async {
    return _initializer.run((service) async {
      if (format == null) {
        await _settingsBox.delete('customDateFormat');
      } else {
        await _settingsBox.put('customDateFormat', format);
      }
    });
  }

  Future<String?> getCustomDateFormat() async {
    return _initializer.run((service) async {
      return _settingsBox.get('customDateFormat');
    });
  }

  Future<void> setUseCustomDateFormat(bool enabled) async {
    return _initializer.run((service) async {
      await _settingsBox.put('useCustomDateFormat', enabled);
    });
  }

  Future<bool> isCustomDateFormatEnabled() async {
    return _initializer.run((service) async {
      return _settingsBox.get('useCustomDateFormat', defaultValue: false);
    });
  }

  // Locale override settings
  Future<void> setCustomLocale(String? localeString) async {
    return _initializer.run((service) async {
      if (localeString == null) {
        await _settingsBox.delete('customLocale');
      } else {
        await _settingsBox.put('customLocale', localeString);
      }
    });
  }

  Future<String?> getCustomLocale() async {
    return _initializer.run((service) async {
      return _settingsBox.get('customLocale');
    });
  }

  Future<void> setUseCustomLocale(bool enabled) async {
    return _initializer.run((service) async {
      await _settingsBox.put('useCustomLocale', enabled);
    });
  }

  Future<bool> isCustomLocaleEnabled() async {
    return _initializer.run((service) async {
      return _settingsBox.get('useCustomLocale', defaultValue: false);
    });
  }

  Future<void> setLastRestoreDate(DateTime date) async {
    return _initializer.run((service) async {
      await _settingsBox.put('lastRestoreDate', date.toIso8601String());
    });
  }

  Future<DateTime?> getLastRestoreDate() async {
    return _initializer.run((service) async {
      final dateStr = _settingsBox.get('lastRestoreDate');
      return dateStr != null ? DateTime.parse(dateStr) : null;
    });
  }

  // Invoice settings methods
  Future<InvoiceSettings> getInvoiceSettings() async {
    return _initializer.run((service) async {
      final settingsData = _settingsBox.get('invoiceSettings');
      if (settingsData != null) {
        return InvoiceSettings.fromJson(Map<String, dynamic>.from(settingsData));
      }
      return InvoiceSettings(); // Return default settings
    });
  }

  Future<void> saveInvoiceSettings(InvoiceSettings settings) async {
    return _initializer.run((service) async {
      await _settingsBox.put('invoiceSettings', settings.toJson());

      // Notify listeners of data change
      notifyDataChange();

      // Auto backup to cloud if enabled
      autoBackupToCloudIfEnabled();
    });
  }

  Future<void> updateInvoiceSettings({
    String? defaultCurrency,
    String? defaultLocale,
    InvoiceTemplate? defaultTemplate,
    BusinessInfo? businessInfo,
    String? invoiceNumberPrefix,
    String? invoiceNumberDateFormat,
    int? invoiceNumberSequenceLength,
    double? defaultTaxRate,
    int? defaultDueDays,
    String? defaultNotes,
  }) async {
    return _initializer.run((service) async {
      final currentSettings = await getInvoiceSettings();
      final updatedSettings = currentSettings.copyWith(
        defaultCurrency: defaultCurrency,
        defaultLocale: defaultLocale,
        defaultTemplate: defaultTemplate,
        businessInfo: businessInfo,
        invoiceNumberPrefix: invoiceNumberPrefix,
        invoiceNumberDateFormat: invoiceNumberDateFormat,
        invoiceNumberSequenceLength: invoiceNumberSequenceLength,
        defaultTaxRate: defaultTaxRate,
        defaultDueDays: defaultDueDays,
        defaultNotes: defaultNotes,
      );
      await saveInvoiceSettings(updatedSettings);
    });
  }

  Future<BusinessInfo?> getBusinessInfo() async {
    return _initializer.run((service) async {
      final settings = await getInvoiceSettings();
      return settings.businessInfo;
    });
  }

  Future<void> saveBusinessInfo(BusinessInfo businessInfo) async {
    return _initializer.run((service) async {
      await updateInvoiceSettings(businessInfo: businessInfo);
    });
  }

  // Cloud backup methods
  Future<String> backupToCloud() async {
    return _initializer.run((service) async {
      _ensureCloudBackupServiceAvailable();
      final backupData = await exportDatabase();
      final filename = await _cloudBackupService!.uploadBackup(backupData);

      // Update the last restore date to prevent showing notification for this backup
      await setLastRestoreDate(DateTime.now());

      return filename;
    });
  }

  Future<void> restoreFromCloud(String? filename) async {
    return _initializer.run((service) async {
      if (_isRestoring) {
        throw Exception('A restore operation is already in progress');
      }

      _isRestoring = true;

      try {
        debugPrint('Starting cloud backup restoration process');

        // Download and decrypt the backup
        final backupData = await _cloudBackupService!.downloadBackup(filename);

        debugPrint('Successfully downloaded backup with ${backupData.projects.length} projects and ${_countTimeEntries(backupData.timeEntries)} time entries');

        // Import the database
        await importDatabase(backupData);

        // Set the last restore date
        await setLastRestoreDate(DateTime.now());

        debugPrint('Backup restoration completed successfully');
      } catch (e) {
        debugPrint('Error during cloud backup restoration: $e');
        rethrow;
      } finally {
        _isRestoring = false;
      }
    });
  }

  /// Restore from the most recent valid backup among a list of backup filenames
  /// This is useful when migrating data or recovering with a recovery key
  Future<bool> restoreFromMostRecentValidBackup(List<String> filenames) async {
    return _initializer.run((service) async {
      if (_isRestoring) {
        throw Exception('A restore operation is already in progress');
      }

      if (filenames.isEmpty) {
        return false;
      }

      _isRestoring = true;

      try {
        debugPrint('Starting backup download for the most recent backup');

        // First sort filenames by date (based on filename format)
        final sortedFilenames = List<String>.from(filenames);
        sortedFilenames.sort((a, b) => b.compareTo(a)); // Assume newer backups have later filenames

        // Download the most recent backup
        final backup = await _cloudBackupService!.downloadBackup(sortedFilenames.first);

        debugPrint('Restoring backup with ${backup.projects.length} projects and ${_countTimeEntries(backup.timeEntries)} time entries');

        // Import the database
        await importDatabase(backup);

        // Set the last restore date
        await setLastRestoreDate(DateTime.now());

        debugPrint('Backup restoration completed successfully');
        return true;
      } catch (e) {
        debugPrint('Error during cloud backup restoration: $e');
        return false;
      } finally {
        _isRestoring = false;
      }
    });
  }

  // Helper method to count total time entries
  int _countTimeEntries(Map<String, List<TimeEntry>> timeEntries) {
    return timeEntries.values.fold(0, (sum, entries) => sum + entries.length);
  }

  Future<List<String>> listCloudBackups({bool forceRefresh = false}) async {
    return _initializer.run((service) async {
      return _cloudBackupService!.listBackups(forceRefresh: forceRefresh);
    });
  }

  Future<List<String>> deleteCloudBackup(String filename) async {
    return _initializer.run((service) async {
      return _cloudBackupService!.deleteBackup(filename);
    });
  }

  Future<String> generateRecoveryCode() async {
    return _initializer.run((service) {
      return _cloudBackupService!.generateRecoveryCode();
    });
  }

  Future<bool> hasGeneratedRecoveryCode() async {
    return _initializer.run((service) async {
      return _cloudBackupService!.hasRecoveryCodeBeenGenerated;
    });
  }

  // Helper method to check if cloud backup service is available
  void _ensureCloudBackupServiceAvailable() {
    if (_cloudBackupService == null) {
      throw Exception('CloudBackupService not available. Initialization may have failed.');
    }
  }

  // Method to get the CloudBackupService instance
  CloudBackupService getCloudBackupService() {
    if (_cloudBackupService == null) {
      throw Exception('CloudBackupService not available. Initialization may have failed.');
    }
    return _cloudBackupService!;
  }

  Future<void> markRecoveryCodeAsSaved() async {
    return _initializer.run((service) {
      return _cloudBackupService!.markRecoveryCodeAsSaved();
    });
  }

  String getRecoveryCode() {
    return _cloudBackupService!.getRecoveryCode();
  }

  /// Reset the recovery key for testing recovery functionality
  /// This clears the recovery code and related settings, but keeps the user ID
  Future<void> resetRecoveryKey() async {
    return _initializer.run((service) {
      return _cloudBackupService!.resetRecoveryKey();
    });
  }

  Future<DateTime?> extractDateFromCloudBackupFilename(String filename) async {
    return _initializer.run((service) async {
      return _cloudBackupService!.extractDateFromBackupFilename(filename);
    });
  }

  // Recovery key methods
  Future<bool> restoreFromRecoveryKey(String recoveryKey) async {
    return _initializer.run((service) async {
      try {
        final success = await _cloudBackupService!.restoreFromRecoveryKey(recoveryKey);

        if (success) {
          // Get the list of backups with force refresh after recovery
          final backups = await listCloudBackups(forceRefresh: true);

          if (backups.isNotEmpty) {
            // Restore from the most recent backup
            return await restoreFromMostRecentValidBackup(backups);
          }
        }

        return success;
      } catch (e) {
        debugPrint('Error restoring from recovery key: $e');
        return false;
      }
    });
  }

  // Apply transfer data from QR code
  Future<bool> applyTransferData(String transferData) async {
    return _initializer.run((service) async {
      try {
        final success = await _cloudBackupService!.applyTransferData(transferData);

        if (success) {
          // Get the list of backups with force refresh after recovery
          final backups = await listCloudBackups(forceRefresh: true);

          if (backups.isNotEmpty) {
            // Restore from the most recent backup
            return await restoreFromMostRecentValidBackup(backups);
          }
        }

        return success;
      } catch (e) {
        debugPrint('Error applying transfer data: $e');
        return false;
      }
    });
  }

  // Get transfer data for QR code
  Future<Map<String, dynamic>> getTransferData() async {
    return _initializer.run((service) async {
      // Generate recovery code if one hasn't been generated yet
      if (!_cloudBackupService!.hasRecoveryCodeBeenGenerated) {
        await _cloudBackupService!.generateRecoveryCode();
      }

      return _cloudBackupService!.getTransferData();
    });
  }

  Future<void> clearAllData() async {
    return _initializer.run((service) async {
      await _projectBox.clear();
      await _timeEntryBox.clear();
    });
  }

  // Auto backup to cloud if enabled
  Future<void> autoBackupToCloudIfEnabled() async {
    return _initializer.run((service) async {
      try {
        final isEnabled = await isCloudAutoBackupEnabled();
        if (isEnabled) {
          final backupData = await exportDatabase();
          await _cloudBackupService!.uploadBackup(backupData);

          // Update the last restore date to prevent showing notification for this backup
          await setLastRestoreDate(DateTime.now());

          debugPrint('Auto cloud backup completed successfully');
        }
      } catch (e) {
        debugPrint('Auto cloud backup failed: $e');
      }
    });
  }

  // Public method to initialize service
  static Future<void> initializeService() async {
    await _initializer.service;
  }

  // Instance method for backward compatibility
  Future<void> initialize() async {
    await DatabaseService.initializeService();
  }

  // Add a migration method to update existing time entries to use the inProgress flag
  Future<void> migrateTimeEntries() async {
    try {
      final entries = await getTimeEntries();
      for (final entry in entries) {
        // Check if this entry might be active using the old logic
        if (entry.start != null && entry.end != null && entry.start == entry.end) {
          // This was considered active in the old system
          // Convert it to use the new system
          final updatedEntry = entry.copyWith(inProgress: true);
          await saveTimeEntry(updatedEntry);
        }
      }
    } catch (e) {
      debugPrint('Error migrating time entries: $e');
    }
  }
}

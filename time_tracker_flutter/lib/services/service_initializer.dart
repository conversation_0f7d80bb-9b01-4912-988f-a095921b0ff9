import 'dart:async';

import 'package:flutter/foundation.dart';

/// A generic wrapper for services that handles lazy initialization
class ServiceInitializer<T> {
  final Future<T> Function() _initFunction;

  bool _isInitialized = false;
  Completer<T>? _initializationCompleter;
  late T _service;

  ServiceInitializer(this._initFunction);

  /// Returns the service instance, initializing it if necessary
  Future<T> get service async {
    // If already initialized, return immediately
    if (_isInitialized) return _service;

    // If initialization is in progress, wait for it to complete
    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    // Start initialization
    _initializationCompleter = Completer<T>();

    try {
      _service = await _initFunction();
      _isInitialized = true;
      _initializationCompleter!.complete(_service);
      return _service;
    } catch (e) {
      _initializationCompleter!.completeError(e);
      debugPrint('Error during service initialization: $e');
      rethrow;
    }
  }

  /// Runs a function with the initialized service
  Future<R> run<R>(Future<R> Function(T service) fn) async {
    final serviceInstance = await service;
    return fn(serviceInstance);
  }

  /// Checks if the service is already initialized
  bool get isInitialized => _isInitialized;
}
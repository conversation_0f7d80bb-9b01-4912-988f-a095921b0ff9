import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

class BackupService {
  static final BackupService _instance = BackupService._internal();

  factory BackupService() => _instance;

  BackupService._internal();

  final DatabaseService _databaseService = DatabaseService();

  Future<String> get _backupDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/backups');

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    return backupDir.path;
  }

  String _generateBackupFilename() {
    final now = DateTime.now();
    final dateStr = now.toIso8601String().split('T')[0];
    final timeStr = now.toIso8601String().split('T')[1].substring(0, 8).replaceAll(':', '-');
    return 'time-tracker-backup-$dateStr-$timeStr.json';
  }

  Future<String> createBackup() async {
    final backupData = await _databaseService.exportDatabase();
    final backupDir = await _backupDirectory;
    final filename = _generateBackupFilename();
    final filePath = '$backupDir/$filename';

    final file = File(filePath);
    await file.writeAsString(jsonEncode(backupData.toJson()));

    return filename;
  }

  Future<List<String>> listBackups() async {
    final backupDir = await _backupDirectory;
    final dir = Directory(backupDir);

    if (!await dir.exists()) {
      return [];
    }

    final files = await dir.list().toList();
    return files
        .whereType<File>()
        .where((file) => file.path.endsWith('.json'))
        .map((file) => file.path.split('/').last)
        .toList()
      ..sort((a, b) => b.compareTo(a)); // Sort newest first
  }

  Future<void> restoreBackup(String filename) async {
    final backupDir = await _backupDirectory;
    final filePath = '$backupDir/$filename';

    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('Backup file not found');
    }

    final jsonString = await file.readAsString();
    final jsonData = jsonDecode(jsonString);
    final backupData = BackupData.fromJson(jsonData);

    await _databaseService.importDatabase(backupData);
    await _databaseService.setLastRestoreDate(DateTime.now());
  }

  Future<void> deleteBackup(String filename) async {
    final backupDir = await _backupDirectory;
    final filePath = '$backupDir/$filename';

    final file = File(filePath);
    if (await file.exists()) {
      await file.delete();
    }
  }

  Future<DateTime?> extractDateFromBackupFilename(String filename) async {
    // Expected format: time-tracker-backup-YYYY-MM-DD-HH-MM-SS.json
    final regex = RegExp(r'time-tracker-backup-(\d{4}-\d{2}-\d{2})-(\d{2}-\d{2}-\d{2})\.json');
    final match = regex.firstMatch(filename);

    if (match != null) {
      final dateStr = match.group(1);
      final timeStr = match.group(2)?.replaceAll('-', ':');

      if (dateStr != null && timeStr != null) {
        try {
          return DateTime.parse('${dateStr}T$timeStr');
        } catch (e) {
          return null;
        }
      }
    }

    return null;
  }

  Future<bool> hasNewerBackup() async {
    final lastRestoreDate = await _databaseService.getLastRestoreDate();
    if (lastRestoreDate == null) {
      return false;
    }

    final backups = await listBackups();
    if (backups.isEmpty) {
      return false;
    }

    // Check the newest backup (first in the list)
    final newestBackup = backups.first;
    final backupDate = await extractDateFromBackupFilename(newestBackup);

    if (backupDate != null && backupDate.isAfter(lastRestoreDate)) {
      return true;
    }

    return false;
  }

  Future<String?> getNewestBackup() async {
    final backups = await listBackups();
    return backups.isNotEmpty ? backups.first : null;
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Service for handling currency formatting and validation with locale support
class CurrencyService {
  // Supported currencies with their ISO codes and symbols
  static const Map<String, Map<String, dynamic>> _supportedCurrencies = {
    'USD': {
      'name': 'US Dollar',
      'symbol': '\$',
      'decimalDigits': 2,
      'code': 'USD',
    },
    'EUR': {
      'name': 'Euro',
      'symbol': '€',
      'decimalDigits': 2,
      'code': 'EUR',
    },
    'GBP': {
      'name': 'British Pound',
      'symbol': '£',
      'decimalDigits': 2,
      'code': 'GBP',
    },
    'JPY': {
      'name': 'Japanese Yen',
      'symbol': '¥',
      'decimalDigits': 0,
      'code': 'JPY',
    },
    'CAD': {
      'name': 'Canadian Dollar',
      'symbol': 'C\$',
      'decimalDigits': 2,
      'code': 'CAD',
    },
    'AUD': {
      'name': 'Australian Dollar',
      'symbol': 'A\$',
      'decimalDigits': 2,
      'code': 'AUD',
    },
    'CHF': {
      'name': 'Swiss Franc',
      'symbol': 'CHF',
      'decimalDigits': 2,
      'code': 'CHF',
    },
    'CNY': {
      'name': 'Chinese Yuan',
      'symbol': '¥',
      'decimalDigits': 2,
      'code': 'CNY',
    },
    'INR': {
      'name': 'Indian Rupee',
      'symbol': '₹',
      'decimalDigits': 2,
      'code': 'INR',
    },
    'BRL': {
      'name': 'Brazilian Real',
      'symbol': 'R\$',
      'decimalDigits': 2,
      'code': 'BRL',
    },
  };

  /// Get list of all supported currencies
  static List<String> getSupportedCurrencies() {
    return _supportedCurrencies.keys.toList();
  }

  /// Get currency information for a given currency code
  static Map<String, dynamic>? getCurrencyInfo(String currencyCode) {
    return _supportedCurrencies[currencyCode.toUpperCase()];
  }

  /// Get currency symbol for a given currency code
  static String getCurrencySymbol(String currencyCode) {
    final info = getCurrencyInfo(currencyCode);
    return info?['symbol'] ?? currencyCode;
  }

  /// Get currency name for a given currency code
  static String getCurrencyName(String currencyCode) {
    final info = getCurrencyInfo(currencyCode);
    return info?['name'] ?? currencyCode;
  }

  /// Get decimal digits for a given currency code
  static int getCurrencyDecimalDigits(String currencyCode) {
    final info = getCurrencyInfo(currencyCode);
    return info?['decimalDigits'] ?? 2;
  }

  /// Validate if a currency code is supported
  static bool isCurrencySupported(String currencyCode) {
    return _supportedCurrencies.containsKey(currencyCode.toUpperCase());
  }

  /// Format currency amount with locale-specific formatting
  static String formatCurrency(
    double amount,
    String currencyCode, [
    Locale? locale,
  ]) {
    try {
      final upperCurrencyCode = currencyCode.toUpperCase();

      // Validate currency is supported
      if (!isCurrencySupported(upperCurrencyCode)) {
        // Fallback to USD if currency not supported
        return formatCurrency(amount, 'USD', locale);
      }

      final decimalDigits = getCurrencyDecimalDigits(upperCurrencyCode);

      // Create NumberFormat for currency with locale
      final formatter = NumberFormat.currency(
        locale: locale?.toString(),
        symbol: getCurrencySymbol(upperCurrencyCode),
        decimalDigits: decimalDigits,
      );

      return formatter.format(amount);
    } catch (e) {
      // Fallback formatting if locale-specific formatting fails
      final symbol = getCurrencySymbol(currencyCode);
      final decimalDigits = getCurrencyDecimalDigits(currencyCode);

      if (decimalDigits == 0) {
        return '$symbol${amount.round()}';
      } else {
        return '$symbol${amount.toStringAsFixed(decimalDigits)}';
      }
    }
  }

  /// Format currency amount with context-aware locale
  static Future<String> formatCurrencyWithContext(
    double amount,
    String currencyCode,
    BuildContext context,
  ) async {
    final locale = await LocaleDateUtils.getEffectiveLocale(context);
    return formatCurrency(amount, currencyCode, locale);
  }

  /// Format currency amount without symbol (just the number with locale formatting)
  static String formatCurrencyAmount(
    double amount,
    String currencyCode, [
    Locale? locale,
  ]) {
    try {
      final upperCurrencyCode = currencyCode.toUpperCase();

      // Validate currency is supported
      if (!isCurrencySupported(upperCurrencyCode)) {
        // Fallback to USD formatting if currency not supported
        return formatCurrencyAmount(amount, 'USD', locale);
      }

      final decimalDigits = getCurrencyDecimalDigits(upperCurrencyCode);

      // Create NumberFormat for numbers with locale
      final formatter = NumberFormat.decimalPattern(locale?.toString());

      if (decimalDigits == 0) {
        return formatter.format(amount.round());
      } else {
        // Format with specific decimal places
        final roundedAmount = double.parse(amount.toStringAsFixed(decimalDigits));
        return formatter.format(roundedAmount);
      }
    } catch (e) {
      // Fallback formatting
      final decimalDigits = getCurrencyDecimalDigits(currencyCode);

      if (decimalDigits == 0) {
        return amount.round().toString();
      } else {
        return amount.toStringAsFixed(decimalDigits);
      }
    }
  }

  /// Validate currency format for a given locale
  static bool validateCurrencyFormat(String currencyCode, Locale? locale) {
    try {
      final upperCurrencyCode = currencyCode.toUpperCase();

      // Check if currency is supported
      if (!isCurrencySupported(upperCurrencyCode)) {
        return false;
      }

      // Try to create a formatter to validate locale compatibility
      NumberFormat.currency(
        locale: locale?.toString(),
        symbol: getCurrencySymbol(upperCurrencyCode),
        decimalDigits: getCurrencyDecimalDigits(upperCurrencyCode),
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get popular currencies (most commonly used)
  static List<String> getPopularCurrencies() {
    return ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
  }

  /// Get currencies sorted by name
  static List<Map<String, dynamic>> getCurrenciesSortedByName() {
    final currencies = <Map<String, dynamic>>[];

    for (final entry in _supportedCurrencies.entries) {
      currencies.add({
        'code': entry.key,
        'name': entry.value['name'],
        'symbol': entry.value['symbol'],
        'decimalDigits': entry.value['decimalDigits'],
      });
    }

    currencies.sort((a, b) => a['name'].compareTo(b['name']));
    return currencies;
  }

  /// Parse currency string to double (removes currency symbols and formatting)
  static double? parseCurrencyString(String currencyString, String currencyCode, [Locale? locale]) {
    try {
      // Remove currency symbol
      final symbol = getCurrencySymbol(currencyCode);
      String cleanString = currencyString.replaceAll(symbol, '').trim();

      // Handle locale-specific number formatting
      if (locale != null) {
        // For locales that use comma as decimal separator
        if (locale.toString().startsWith('de') ||
            locale.toString().startsWith('fr') ||
            locale.toString().startsWith('es') ||
            locale.toString().startsWith('it')) {
          // Replace comma with dot for parsing
          cleanString = cleanString.replaceAll('.', '').replaceAll(',', '.');
        } else {
          // For locales that use comma as thousand separator
          cleanString = cleanString.replaceAll(',', '');
        }
      } else {
        // Default: remove commas (thousand separators)
        cleanString = cleanString.replaceAll(',', '');
      }

      return double.tryParse(cleanString);
    } catch (e) {
      return null;
    }
  }

  /// Format currency for display in lists (compact format)
  static String formatCurrencyCompact(
    double amount,
    String currencyCode, [
    Locale? locale,
  ]) {
    final symbol = getCurrencySymbol(currencyCode);

    if (amount.abs() >= 1000000) {
      return '$symbol${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount.abs() >= 1000) {
      return '$symbol${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return formatCurrency(amount, currencyCode, locale);
    }
  }

  /// Get currency formatting example for preview
  static String getCurrencyFormattingExample(String currencyCode, [Locale? locale]) {
    const exampleAmount = 1234.56;
    return formatCurrency(exampleAmount, currencyCode, locale);
  }

  /// Check if locale uses comma as decimal separator
  static bool localeUsesCommaAsDecimal(Locale? locale) {
    if (locale == null) return false;

    // Common locales that use comma as decimal separator
    const commaDecimalLocales = {
      'de', 'fr', 'es', 'it', 'pt', 'nl', 'pl', 'cs', 'hu', 'ru'
    };

    return commaDecimalLocales.contains(locale.languageCode);
  }

  /// Get appropriate decimal separator for locale
  static String getDecimalSeparator([Locale? locale]) {
    return localeUsesCommaAsDecimal(locale) ? ',' : '.';
  }

  /// Get appropriate thousand separator for locale
  static String getThousandSeparator([Locale? locale]) {
    return localeUsesCommaAsDecimal(locale) ? '.' : ',';
  }
}
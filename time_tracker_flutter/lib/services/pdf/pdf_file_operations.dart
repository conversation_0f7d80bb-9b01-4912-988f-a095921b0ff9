import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_models.dart' as pdf_models;

/// Service for handling PDF file operations
class PDFFileOperations {
  /// Generate and save PDF to device storage
  static Future<String> generateAndSavePDF({
    required Uint8List pdfData,
    required Invoice invoice,
    String? customFileName,
    String? customDirectory,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.7);

      final directory = customDirectory != null
          ? Directory(customDirectory)
          : await getApplicationDocumentsDirectory();

      final fileName = customFileName ??
          'invoice_${invoice.invoiceNumber.replaceAll(RegExp(r'[^\w\-_]'), '_')}.pdf';
      final file = File('${directory.path}/$fileName');

      await file.writeAsBytes(pdfData);
      onProgress?.call(1.0);
      return file.path;
    } catch (e) {
      throw pdf_models.PDFGenerationException('Failed to save PDF: $e');
    }
  }

  /// Save PDF to user-selected location
  static Future<String?> saveInvoicePDFToCustomLocation({
    required Uint8List pdfData,
    required Invoice invoice,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.8);

      final fileName = 'invoice_${invoice.invoiceNumber.replaceAll(RegExp(r'[^\w\-_]'), '_')}.pdf';
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Invoice PDF',
        fileName: fileName,
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        bytes: pdfData,
      );

      onProgress?.call(1.0);
      return result;
    } catch (e) {
      throw pdf_models.PDFGenerationException('Failed to save PDF to custom location: $e');
    }
  }

  /// Share PDF via system share dialog
  static Future<void> shareInvoicePDF({
    required String filePath,
    required Invoice invoice,
    required Client client,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.9);

      await Share.share(
        'Please find attached invoice ${invoice.invoiceNumber} for ${client.name}.\n$filePath',
        subject: 'Invoice ${invoice.invoiceNumber}',
      );

      onProgress?.call(1.0);
    } catch (e) {
      throw pdf_models.PDFGenerationException('Failed to share PDF: $e');
    }
  }

  /// Share PDF via email with attachment
  static Future<void> shareInvoicePDFViaEmail({
    required String filePath,
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
    Locale? locale,
    String? recipientEmail,
    String? customSubject,
    String? customBody,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.9);

      final subject = customSubject ?? 'Invoice ${invoice.invoiceNumber}';
      final body = customBody ??
          'Dear ${client.name},\n\n'
          'Please find attached invoice ${invoice.invoiceNumber} for the amount of '
          '${_formatCurrency(invoice.total, invoice.currency, locale)}.\n\n'
          'Payment is due by ${invoice.dueDate != null ? _formatDate(invoice.dueDate!, locale) : "30 days from invoice date"}.\n\n'
          'Thank you for your business!\n\n'
          'Best regards,\n'
          '${businessInfo.name}';

      await Share.share(
        body,
        subject: subject,
      );

      onProgress?.call(1.0);
    } catch (e) {
      throw pdf_models.PDFGenerationException('Failed to share PDF via email: $e');
    }
  }

  /// Export multiple invoices as a batch
  static Future<List<String>> exportInvoiceBatch({
    required List<Uint8List> pdfDataList,
    required List<Invoice> invoices,
    String? customDirectory,
    Function(double)? onProgress,
  }) async {
    try {
      final filePaths = <String>[];
      final totalInvoices = invoices.length;

      for (int i = 0; i < invoices.length; i++) {
        final invoice = invoices[i];
        final pdfData = pdfDataList[i];

        final filePath = await generateAndSavePDF(
          pdfData: pdfData,
          invoice: invoice,
          customDirectory: customDirectory,
        );

        filePaths.add(filePath);
        onProgress?.call((i + 1) / totalInvoices);
      }

      return filePaths;
    } catch (e) {
      throw pdf_models.PDFGenerationException('Failed to export invoice batch: $e');
    }
  }

  /// Clean up temporary PDF files
  static Future<void> cleanupTemporaryFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory('${directory.path}/temp_pdfs');

      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('Failed to cleanup temporary PDF files: $e');
    }
  }

  /// Get PDF file size estimate
  static Future<int> estimatePDFSize({
    required Invoice invoice,
    required BusinessInfo businessInfo,
    InvoiceTemplate template = InvoiceTemplate.professional,
    List<TimeEntry>? timeEntries,
  }) async {
    try {
      int estimatedSize = 50000; // ~50KB base

      estimatedSize += invoice.additionalItems.length * 500;

      if (timeEntries != null) {
        estimatedSize += timeEntries.length * 300;
      }

      // Note: BusinessInfo from invoice_models.dart doesn't have logoData
      // This would need to be handled differently in a real implementation

      switch (template) {
        case InvoiceTemplate.minimal:
          estimatedSize = (estimatedSize * 0.7).round();
          break;
        case InvoiceTemplate.detailed:
          estimatedSize = (estimatedSize * 1.5).round();
          break;
        case InvoiceTemplate.professional:
          break;
      }

      return estimatedSize;
    } catch (e) {
      return 100000; // Default 100KB estimate
    }
  }

  // Helper methods
  static String _formatCurrency(double amount, String currency, Locale? locale) {
    // This would use the CurrencyService in a real implementation
    return '\$${amount.toStringAsFixed(2)}';
  }

  static String _formatDate(DateTime date, Locale? locale) {
    // This would use LocaleDateUtils in a real implementation
    return '${date.day}/${date.month}/${date.year}';
  }
} 
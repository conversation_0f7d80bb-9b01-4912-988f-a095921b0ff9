import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_models.dart' as pdf_models;
import 'package:time_tracker_flutter/services/pdf/pdf_formatter.dart';

/// Utility class for building PDF widgets
class PDFWidgets {
  /// Build header section with business info and logo
  static pw.Widget buildHeader(pdf_models.PDFBusinessInfo businessInfo, pw.Font boldFont, pw.Font font, String language) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              businessInfo.name,
              style: pw.TextStyle(font: boldFont, fontSize: 20),
            ),
            if (businessInfo.address != null) ...[
              pw.SizedBox(height: 5),
              pw.Text(
                businessInfo.address!.formattedAddress,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (businessInfo.email != null) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                businessInfo.email!,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (businessInfo.phone != null) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                businessInfo.phone!,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
          ],
        ),
        if (businessInfo.logoData != null)
          pw.Container(
            width: 80,
            height: 80,
            child: pw.Image(
              pw.MemoryImage(businessInfo.logoData!),
              fit: pw.BoxFit.contain,
            ),
          ),
      ],
    );
  }

  /// Build invoice title section
  static pw.Widget buildInvoiceTitle(Invoice invoice, pw.Font boldFont, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('invoice', language),
          style: pw.TextStyle(font: boldFont, fontSize: 28, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          '${PDFFormatter.getTranslation('invoice_number', language)}${invoice.invoiceNumber}',
          style: pw.TextStyle(font: boldFont, fontSize: 16),
        ),
      ],
    );
  }

  /// Build client information section
  static pw.Widget buildClientInfo(Client client, pw.Font boldFont, pw.Font font, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('bill_to', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          client.name,
          style: pw.TextStyle(font: boldFont, fontSize: 14),
        ),
        if (client.email != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            client.email!,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
        if (client.address != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            client.address!.formattedAddress,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
        if (client.taxId != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            'Tax ID: ${client.taxId!}',
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
      ],
    );
  }

  /// Build invoice information section
  static pw.Widget buildInvoiceInfo(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('invoice_details', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        _buildInfoRow(PDFFormatter.getTranslation('issue_date', language), PDFFormatter.formatDate(invoice.issueDate, locale), boldFont, font),
        if (invoice.dueDate != null)
          _buildInfoRow(PDFFormatter.getTranslation('due_date', language), PDFFormatter.formatDate(invoice.dueDate!, locale), boldFont, font),
        if (invoice.servicePeriodStart != null && invoice.servicePeriodEnd != null)
          _buildInfoRow(
            PDFFormatter.getTranslation('service_period', language),
            '${PDFFormatter.formatDate(invoice.servicePeriodStart!, locale)} - ${PDFFormatter.formatDate(invoice.servicePeriodEnd!, locale)}',
            boldFont,
            font,
          ),
      ],
    );
  }

  /// Build info row helper
  static pw.Widget _buildInfoRow(String label, String value, pw.Font boldFont, pw.Font font) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 3),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 100,
            child: pw.Text(
              label,
              style: pw.TextStyle(font: font, fontSize: 11),
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: boldFont, fontSize: 11),
          ),
        ],
      ),
    );
  }

  /// Build line items table
  static pw.Widget buildLineItemsTable(
    Invoice invoice,
    List<TimeEntry>? timeEntries,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    final headers = [
      PDFFormatter.getTranslation('description', language),
      PDFFormatter.getTranslation('hours', language),
      PDFFormatter.getTranslation('rate', language),
      PDFFormatter.getTranslation('amount', language),
    ];
    final data = <List<String>>[];

    // Add line items from invoice
    for (final item in invoice.additionalItems) {
      data.add([
        item.description,
        PDFFormatter.formatQuantity(item.quantity),
        PDFFormatter.formatCurrency(item.rate, invoice.currency, locale),
        PDFFormatter.formatCurrency(item.amount, invoice.currency, locale),
      ]);
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: headers.map((header) =>
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                header,
                style: pw.TextStyle(font: boldFont, fontSize: 12),
              ),
            ),
          ).toList(),
        ),
        // Data rows
        ...data.map((row) =>
          pw.TableRow(
            children: row.asMap().entries.map((entry) =>
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  entry.value,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 11,
                  ),
                  textAlign: entry.key == 0 ? pw.TextAlign.left : pw.TextAlign.right,
                ),
              ),
            ).toList(),
          ),
        ),
      ],
    );
  }

  /// Build totals section
  static pw.Widget buildTotalsSection(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 200,
        child: pw.Column(
          children: [
            _buildTotalRow(
              '${PDFFormatter.getTranslation('subtotal', language)}:',
              PDFFormatter.formatCurrency(invoice.subtotal, invoice.currency, locale),
              font,
              font,
            ),
            if (invoice.taxAmount > 0)
              _buildTotalRow(
                '${PDFFormatter.getTranslation('tax', language)} (${invoice.taxRate.toStringAsFixed(1)}%):',
                PDFFormatter.formatCurrency(invoice.taxAmount, invoice.currency, locale),
                font,
                font,
              ),
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 8),
              padding: const pw.EdgeInsets.symmetric(vertical: 8),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  top: pw.BorderSide(width: 2, color: PdfColors.blue800),
                ),
              ),
              child: _buildTotalRow(
                '${PDFFormatter.getTranslation('total', language)}:',
                PDFFormatter.formatCurrency(invoice.total, invoice.currency, locale),
                boldFont,
                boldFont,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build total row helper
  static pw.Widget _buildTotalRow(
    String label,
    String value,
    pw.Font labelFont,
    pw.Font valueFont, {
    double fontSize = 12,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(font: labelFont, fontSize: fontSize),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: valueFont, fontSize: fontSize),
          ),
        ],
      ),
    );
  }

  /// Build notes section
  static pw.Widget buildNotesSection(String notes, pw.Font boldFont, pw.Font font, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('notes', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 5),
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            border: pw.Border.all(color: PdfColors.grey200),
          ),
          child: pw.Text(
            notes,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ),
      ],
    );
  }

  /// Build footer section
  static pw.Widget buildFooter(pdf_models.PDFBusinessInfo businessInfo, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300),
        ),
      ),
      child: pw.Column(
        children: [
          if (businessInfo.website != null)
            pw.Text(
              businessInfo.website!,
              style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.blue600),
            ),
          if (businessInfo.taxId != null) ...[
            pw.SizedBox(height: 3),
            pw.Text(
              'Tax ID: ${businessInfo.taxId!}',
              style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600),
            ),
          ],
          pw.SizedBox(height: 5),
          pw.Text(
            PDFFormatter.getTranslation('thank_you_for_business', language),
            style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  /// Build simple line items for minimal template
  static pw.Widget buildSimpleLineItems(
    Invoice invoice,
    List<TimeEntry>? timeEntries,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    return pw.Column(
      children: invoice.additionalItems.map((item) =>
        pw.Padding(
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Text(
                  item.description,
                  style: pw.TextStyle(font: font, fontSize: 12),
                ),
              ),
              pw.Text(
                '${PDFFormatter.formatQuantity(item.quantity)} × ${PDFFormatter.formatCurrency(item.rate, invoice.currency, locale)}',
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              pw.SizedBox(width: 20),
              pw.Text(
                PDFFormatter.formatCurrency(item.amount, invoice.currency, locale),
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
            ],
          ),
        ),
      ).toList(),
    );
  }

  // Detailed template specific widgets

  /// Build detailed header
  static pw.Widget buildDetailedHeader(pdf_models.PDFBusinessInfo businessInfo, pw.Font boldFont, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: buildHeader(businessInfo, boldFont, font, language),
    );
  }

  /// Build detailed invoice title
  static pw.Widget buildDetailedInvoiceTitle(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        buildInvoiceTitle(invoice, boldFont, locale, language),
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: pw.BoxDecoration(
            color: _getStatusColor(invoice.status),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            PDFFormatter.getStatusText(invoice.status, language).toUpperCase(),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 10,
              color: PdfColors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// Build detailed client info
  static pw.Widget buildDetailedClientInfo(Client client, pw.Font boldFont, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: buildClientInfo(client, boldFont, font, language),
    );
  }

  /// Build detailed invoice info
  static pw.Widget buildDetailedInvoiceInfo(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: buildInvoiceInfo(invoice, boldFont, font, locale, language),
    );
  }

  /// Build detailed time entries table
  static pw.Widget buildDetailedTimeEntries(
    List<TimeEntry> timeEntries,
    Invoice invoice,
    pw.Font boldFont,
    pw.Font font,
    Locale? locale,
    String language,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('time_entries', language),
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(1.5),
            2: const pw.FlexColumnWidth(1),
            3: const pw.FlexColumnWidth(1),
            4: const pw.FlexColumnWidth(1.5),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.blue100),
              children: [
                PDFFormatter.getTranslation('project', language),
                PDFFormatter.getTranslation('date', language),
                PDFFormatter.getTranslation('hours', language),
                PDFFormatter.getTranslation('rate', language),
                PDFFormatter.getTranslation('amount', language),
              ].map((header) =>
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    header,
                    style: pw.TextStyle(font: boldFont, fontSize: 11),
                  ),
                ),
              ).toList(),
            ),
            // Time entry rows would go here - simplified for now
          ],
        ),
        pw.SizedBox(height: 20),
      ],
    );
  }

  /// Build detailed line items
  static pw.Widget buildDetailedLineItems(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('additional_items', language),
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.blue800),
        ),
        pw.SizedBox(height: 10),
        buildLineItemsTable(invoice, null, boldFont, font, locale, language),
      ],
    );
  }

  /// Build detailed totals section
  static pw.Widget buildDetailedTotalsSection(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: buildTotalsSection(invoice, boldFont, font, locale, language),
    );
  }

  /// Build payment terms section
  static pw.Widget buildPaymentTerms(Invoice invoice, pw.Font boldFont, pw.Font font, Locale? locale, String language) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          PDFFormatter.getTranslation('payment_terms', language),
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          PDFFormatter.getTranslation('payment_due_within_30_days', language),
          style: pw.TextStyle(font: font, fontSize: 11),
        ),
        if (invoice.dueDate != null) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            '${PDFFormatter.getTranslation('due_date', language)}: ${PDFFormatter.formatDate(invoice.dueDate!, locale)}',
            style: pw.TextStyle(font: boldFont, fontSize: 11),
          ),
        ],
        pw.SizedBox(height: 10),
      ],
    );
  }

  /// Build detailed footer
  static pw.Widget buildDetailedFooter(pdf_models.PDFBusinessInfo businessInfo, pw.Font font, String language) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: const pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.blue200, width: 2),
        ),
      ),
      child: buildFooter(businessInfo, font, language),
    );
  }

  /// Get status color for display
  static PdfColor _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return PdfColors.grey500;
      case InvoiceStatus.sent:
        return PdfColors.blue500;
      case InvoiceStatus.paid:
        return PdfColors.green500;
      case InvoiceStatus.overdue:
        return PdfColors.red500;
      case InvoiceStatus.cancelled:
        return PdfColors.orange500;
    }
  }
}
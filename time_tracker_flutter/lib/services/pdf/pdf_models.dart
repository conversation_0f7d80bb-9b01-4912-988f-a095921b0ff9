import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';



/// Configuration for business information on invoices with logo data
class PDFBusinessInfo {
  final BusinessInfo businessInfo;
  final Uint8List? logoData;

  const PDFBusinessInfo({
    required this.businessInfo,
    this.logoData,
  });

  String get name => businessInfo.name;
  String? get email => businessInfo.email;
  String? get phone => businessInfo.phone;
  Address? get address => businessInfo.address;
  String? get website => businessInfo.website;
  String? get taxId => businessInfo.taxId;
}

/// Data class for PDF generation parameters
class PDFGenerationParams {
  final Invoice invoice;
  final Client client;
  final PDFBusinessInfo businessInfo;
  final InvoiceTemplate template;
  final List<TimeEntry>? timeEntries;
  final Locale? locale;
  final String language;

  const PDFGenerationParams({
    required this.invoice,
    required this.client,
    required this.businessInfo,
    this.template = InvoiceTemplate.professional,
    this.timeEntries,
    this.locale,
    required this.language,
  });
}

/// Exception thrown when PDF generation fails
class PDFGenerationException implements Exception {
  final String message;

  const PDFGenerationException(this.message);

  @override
  String toString() => 'PDFGenerationException: $message';
}
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:time_tracker_flutter/models/pdf/template_models.dart';
import 'dart:typed_data';

class TemplateEngine {
  static Future<pw.Document> renderTemplate(
    PDFTemplate template,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    final document = pw.Document();

    // Debug: Print template info
    print('TemplateEngine: Rendering template "${template.name}" with ${template.sections.length} sections');

    document.addPage(
      pw.MultiPage(
        pageFormat: template.pageFormat,
        margin: template.margins.toPdfEdgeInsets(),
        build: (pw.Context context) {
          final widgets = <pw.Widget>[];

          for (final section in template.sections) {
            print('TemplateEngine: Building section "${section.id}" of type "${section.type}" with ${section.components.length} components');
            final sectionWidget = _buildSection(section, data, font, boldFont, template.styles);
            if (sectionWidget != null) {
              widgets.add(sectionWidget);
            }
          }

          return widgets;
        },
      ),
    );

    return document;
  }

  static pw.Widget? _buildSection(
    PDFSection section,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    // Handle special section types that render themselves
    switch (section.type) {
      case 'line_items':
        return _buildLineItemsComponent(
          PDFComponent(
            id: section.id,
            type: 'line_items',
            position: section.position,
            style: ComponentStyle(),
            dataSource: section.properties,
            properties: section.properties,
          ),
          data,
          font,
          boldFont,
          styles,
        );
      case 'totals':
        return _buildTotalsComponent(
          PDFComponent(
            id: section.id,
            type: 'totals',
            position: section.position,
            style: ComponentStyle(),
            dataSource: section.properties,
            properties: section.properties,
          ),
          data,
          font,
          boldFont,
          styles,
        );
      case 'text':
        return _buildTextComponent(
          PDFComponent(
            id: section.id,
            type: 'text',
            position: section.position,
            style: ComponentStyle.fromJson(section.properties['style'] as Map<String, dynamic>? ?? {}),
            dataSource: section.properties['dataSource'] as Map<String, dynamic>? ?? {},
            properties: section.properties,
          ),
          data,
          ComponentStyle.fromJson(section.properties['style'] as Map<String, dynamic>? ?? {}).toPdfTextStyle(font, boldFont),
        );
    }

    // Handle regular sections with components
    final children = <pw.Widget>[];

    for (final component in section.components) {
      final widget = _buildComponent(component, data, font, boldFont, styles);
      if (widget != null) {
        children.add(widget);
      }
    }

    if (children.isEmpty) return null;

    switch (section.type) {
      case 'row':
        return pw.Row(
          crossAxisAlignment: _parseCrossAxisAlignment(section.position.alignment),
          children: children,
        );
      case 'column':
        return pw.Column(
          crossAxisAlignment: _parseCrossAxisAlignment(section.position.alignment),
          children: children,
        );
      case 'container':
        return pw.Container(
          width: section.position.width,
          height: section.position.height,
          padding: section.position.x != null || section.position.y != null
              ? pw.EdgeInsets.only(
                  left: section.position.x ?? 0,
                  top: section.position.y ?? 0,
                )
              : null,
          child: children.length == 1 ? children.first : pw.Column(children: children),
        );
      default:
        return children.length == 1 ? children.first : pw.Column(children: children);
    }
  }

  static pw.Widget? _buildComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    try {
      final style = component.style;
      final textStyle = style.toPdfTextStyle(font, boldFont);

      print('TemplateEngine: Building component "${component.id}" of type "${component.type}"');

      switch (component.type) {
        case 'text':
          return _buildTextComponent(component, data, textStyle);
        case 'image':
          return _buildImageComponent(component, data);
        case 'table':
          return _buildTableComponent(component, data, font, boldFont, styles);
        case 'line_items':
          return _buildLineItemsComponent(component, data, font, boldFont, styles);
        case 'totals':
          return _buildTotalsComponent(component, data, font, boldFont, styles);
        case 'header':
          return _buildHeaderComponent(component, data, font, boldFont, styles);
        case 'client_info':
          return _buildClientInfoComponent(component, data, font, boldFont, styles);
        case 'invoice_info':
          return _buildInvoiceInfoComponent(component, data, font, boldFont, styles);
        case 'spacer':
          return pw.SizedBox(height: component.position.height ?? 20);
        default:
          print('TemplateEngine: Unknown component type: ${component.type}');
          return null;
      }
    } catch (e) {
      print('TemplateEngine: Error building component "${component.id}": $e');
      return null;
    }
  }

  static pw.Widget _buildTextComponent(PDFComponent component, PDFData data, pw.TextStyle textStyle) {
    try {
      print('TemplateEngine: Building text component "${component.id}"');
      
      final text = component.dataSource['text'] as String? ?? '';
      final dataPath = component.dataSource['dataPath'] as String?;

      print('TemplateEngine: Text component data - text: "$text", dataPath: "$dataPath"');

      String displayText = text;
      if (dataPath != null) {
        final value = data.getValue(dataPath);
        print('TemplateEngine: Data path "$dataPath" returned: $value');
        if (value != null) {
          displayText = value.toString();
        }
      }

      print('TemplateEngine: Final display text: "$displayText"');

      return pw.Text(
        displayText,
        style: textStyle,
        textAlign: _parseTextAlign(component.style.textAlign),
      );
    } catch (e) {
      print('TemplateEngine: Error in _buildTextComponent: $e');
      return pw.Text('Error', style: textStyle);
    }
  }

  static pw.Widget? _buildImageComponent(PDFComponent component, PDFData data) {
    try {
      print('TemplateEngine: Building image component "${component.id}"');
      
      print('TemplateEngine: Component dataSource: ${component.dataSource}');
      final dataPath = component.dataSource['dataPath'] as String?;
      if (dataPath == null) {
        print('TemplateEngine: Image component has no dataPath');
        return null;
      }

      print('TemplateEngine: Image component dataPath: "$dataPath"');
      final imageData = data.getValue(dataPath);
      print('TemplateEngine: Image data retrieved: ${imageData != null ? 'not null' : 'null'}');
      
      if (imageData == null || imageData is! List<int>) {
        print('TemplateEngine: Image data is not valid List<int>');
        return null;
      }

      return pw.Container(
        width: component.position.width,
        height: component.position.height,
        child: pw.Image(
          pw.MemoryImage(Uint8List.fromList(imageData)),
          fit: pw.BoxFit.contain,
        ),
      );
    } catch (e) {
      print('TemplateEngine: Error in _buildImageComponent: $e');
      print('TemplateEngine: Error stack trace: ${StackTrace.current}');
      return null;
    }
  }

  static pw.Widget? _buildTableComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final headers = component.dataSource['headers'] as List<String>? ?? [];
    final dataPath = component.dataSource['dataPath'] as String?;

    if (dataPath == null) return null;

    final tableData = data.getValue(dataPath);
    if (tableData == null || tableData is! List) return null;

    final rows = <pw.TableRow>[];

    // Header row
    if (headers.isNotEmpty) {
      rows.add(
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: headers.map((header) =>
            pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                header,
                style: pw.TextStyle(font: boldFont, fontSize: 12),
              ),
            ),
          ).toList(),
        ),
      );
    }

    // Data rows
    for (final rowData in tableData) {
      if (rowData is List) {
        rows.add(
          pw.TableRow(
            children: rowData.map((cell) =>
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  cell.toString(),
                  style: pw.TextStyle(font: font, fontSize: 11),
                ),
              ),
            ).toList(),
          ),
        );
      }
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: rows,
    );
  }

  static pw.Widget? _buildLineItemsComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final lineItems = data.getValue('invoice.additionalItems');
    if (lineItems == null || lineItems is! List) return null;

    final headers = [
      'Description',
      'Quantity',
      'Rate',
      'Amount',
    ];

    final rows = <pw.TableRow>[];

    // Header
    rows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.grey100),
        children: headers.map((header) =>
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              header,
              style: pw.TextStyle(font: boldFont, fontSize: 12),
            ),
          ),
        ).toList(),
      ),
    );

    // Data rows
    for (final item in lineItems) {
      if (item is Map<String, dynamic>) {
        rows.add(
          pw.TableRow(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  item['description']?.toString() ?? '',
                  style: pw.TextStyle(font: font, fontSize: 11),
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  item['quantity']?.toString() ?? '',
                  style: pw.TextStyle(font: font, fontSize: 11),
                  textAlign: pw.TextAlign.right,
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  item['rate']?.toString() ?? '',
                  style: pw.TextStyle(font: font, fontSize: 11),
                  textAlign: pw.TextAlign.right,
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Text(
                  item['amount']?.toString() ?? '',
                  style: pw.TextStyle(font: font, fontSize: 11),
                  textAlign: pw.TextAlign.right,
                ),
              ),
            ],
          ),
        );
      }
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(3),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
      },
      children: rows,
    );
  }

  static pw.Widget? _buildTotalsComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final invoice = data.invoice;
    
    // Add null safety check
    if (invoice == null) {
      print('TemplateEngine: invoice is null in _buildTotalsComponent');
      return null;
    }
    
    final subtotal = invoice['subtotal'] as double? ?? 0.0;
    final taxAmount = invoice['taxAmount'] as double? ?? 0.0;
    final total = invoice['total'] as double? ?? 0.0;
    final currency = invoice['currency'] as String? ?? 'USD';

    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 200,
        child: pw.Column(
          children: [
            _buildTotalRow('Subtotal:', '\$${subtotal.toStringAsFixed(2)}', font, font),
            if (taxAmount > 0)
              _buildTotalRow('Tax:', '\$${taxAmount.toStringAsFixed(2)}', font, font),
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 8),
              padding: const pw.EdgeInsets.symmetric(vertical: 8),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  top: pw.BorderSide(width: 2, color: PdfColors.blue800),
                ),
              ),
              child: _buildTotalRow('Total:', '\$${total.toStringAsFixed(2)}', boldFont, boldFont, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  static pw.Widget? _buildHeaderComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final businessInfo = data.businessInfo;

    // Add null safety check
    if (businessInfo == null) {
      print('TemplateEngine: businessInfo is null in _buildHeaderComponent');
      return null;
    }

    final name = businessInfo['name'] as String? ?? '';
    final address = businessInfo['address'] as String? ?? '';
    final email = businessInfo['email'] as String? ?? '';
    final phone = businessInfo['phone'] as String? ?? '';

    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              name,
              style: pw.TextStyle(font: boldFont, fontSize: 20),
            ),
            if (address.isNotEmpty) ...[
              pw.SizedBox(height: 5),
              pw.Text(
                address,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (email.isNotEmpty) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                email,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
            if (phone.isNotEmpty) ...[
              pw.SizedBox(height: 3),
              pw.Text(
                phone,
                style: pw.TextStyle(font: font, fontSize: 10),
              ),
            ],
          ],
        ),
        if (businessInfo['logoData'] != null)
          pw.Container(
            width: 80,
            height: 80,
            child: pw.Image(
              pw.MemoryImage(Uint8List.fromList(businessInfo['logoData'] as List<int>)),
              fit: pw.BoxFit.contain,
            ),
          ),
      ],
    );
  }

  static pw.Widget? _buildClientInfoComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final client = data.client;

    // Add null safety check
    if (client == null) {
      print('TemplateEngine: client is null in _buildClientInfoComponent');
      return null;
    }

    final name = client['name'] as String? ?? '';
    final email = client['email'] as String? ?? '';
    final address = client['address'] as String? ?? '';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Bill To:',
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          name,
          style: pw.TextStyle(font: boldFont, fontSize: 14),
        ),
        if (email.isNotEmpty) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            email,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
        if (address.isNotEmpty) ...[
          pw.SizedBox(height: 3),
          pw.Text(
            address,
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
        ],
      ],
    );
  }

  static pw.Widget? _buildInvoiceInfoComponent(
    PDFComponent component,
    PDFData data,
    pw.Font font,
    pw.Font boldFont,
    Map<String, ComponentStyle> styles,
  ) {
    final invoice = data.invoice;

    // Add null safety check
    if (invoice == null) {
      print('TemplateEngine: invoice is null in _buildInvoiceInfoComponent');
      return null;
    }

    final invoiceNumber = invoice['invoiceNumber'] as String? ?? '';
    final issueDate = invoice['issueDate'] as String? ?? '';
    final dueDate = invoice['dueDate'] as String? ?? '';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Invoice Details:',
          style: pw.TextStyle(font: boldFont, fontSize: 12, color: PdfColors.grey700),
        ),
        pw.SizedBox(height: 8),
        _buildInfoRow('Invoice #:', invoiceNumber, boldFont, font),
        _buildInfoRow('Issue Date:', issueDate, boldFont, font),
        if (dueDate.isNotEmpty)
          _buildInfoRow('Due Date:', dueDate, boldFont, font),
      ],
    );
  }

  static pw.Widget _buildTotalRow(
    String label,
    String value,
    pw.Font labelFont,
    pw.Font valueFont, {
    double fontSize = 12,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(font: labelFont, fontSize: fontSize),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: valueFont, fontSize: fontSize),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInfoRow(String label, String value, pw.Font boldFont, pw.Font font) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 3),
      child: pw.Row(
        children: [
          pw.SizedBox(
            width: 100,
            child: pw.Text(
              label,
              style: pw.TextStyle(font: font, fontSize: 11),
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(font: boldFont, fontSize: 11),
          ),
        ],
      ),
    );
  }

  static pw.CrossAxisAlignment _parseCrossAxisAlignment(String? alignment) {
    switch (alignment?.toLowerCase()) {
      case 'start':
        return pw.CrossAxisAlignment.start;
      case 'end':
        return pw.CrossAxisAlignment.end;
      case 'center':
        return pw.CrossAxisAlignment.center;
      case 'stretch':
        return pw.CrossAxisAlignment.stretch;
      default:
        return pw.CrossAxisAlignment.start;
    }
  }

  static pw.TextAlign _parseTextAlign(String? textAlign) {
    switch (textAlign?.toLowerCase()) {
      case 'left':
        return pw.TextAlign.left;
      case 'right':
        return pw.TextAlign.right;
      case 'center':
        return pw.TextAlign.center;
      case 'justify':
        return pw.TextAlign.justify;
      default:
        return pw.TextAlign.left;
    }
  }
}
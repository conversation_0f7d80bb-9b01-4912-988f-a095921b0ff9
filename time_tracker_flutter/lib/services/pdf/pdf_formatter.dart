import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Utility class for formatting data in PDF documents
class PDFFormatter {
  /// Format currency with locale support
  static String formatCurrency(double amount, String currency, Locale? locale) {
    return CurrencyService.formatCurrency(amount, currency, locale);
  }

  /// Format date with locale support
  static String formatDate(DateTime date, Locale? locale) {
    return LocaleDateUtils.formatDate(date, locale);
  }

  /// Format quantity with appropriate decimal places
  static String formatQuantity(double quantity) {
    if (quantity == quantity.roundToDouble()) {
      return quantity.round().toString();
    } else {
      return quantity.toStringAsFixed(2);
    }
  }

  /// Get status text for display
  static String getStatusText(InvoiceStatus status, String language) {
    switch (status) {
      case InvoiceStatus.draft:
        return InvoiceLocalizationService.getTranslation('draft', language);
      case InvoiceStatus.sent:
        return InvoiceLocalizationService.getTranslation('sent', language);
      case InvoiceStatus.paid:
        return InvoiceLocalizationService.getTranslation('paid', language);
      case InvoiceStatus.overdue:
        return InvoiceLocalizationService.getTranslation('overdue', language);
      case InvoiceStatus.cancelled:
        return InvoiceLocalizationService.getTranslation('cancelled', language);
    }
  }

  /// Get status color for display
  static String getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return '#6B7280'; // grey500
      case InvoiceStatus.sent:
        return '#3B82F6'; // blue500
      case InvoiceStatus.paid:
        return '#10B981'; // green500
      case InvoiceStatus.overdue:
        return '#EF4444'; // red500
      case InvoiceStatus.cancelled:
        return '#F59E0B'; // orange500
    }
  }

  /// Get translation for a key
  static String getTranslation(String key, String language) {
    return InvoiceLocalizationService.getTranslation(key, language);
  }
} 
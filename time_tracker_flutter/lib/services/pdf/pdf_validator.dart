import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_models.dart' as pdf_models;

/// Utility class for validating PDF generation parameters
class PDFValidator {
  /// Validate PDF generation parameters
  static void validatePDFParameters({
    required Invoice invoice,
    required Client client,
    required BusinessInfo businessInfo,
  }) {
    if (invoice.invoiceNumber.isEmpty) {
      throw pdf_models.PDFGenerationException('Invoice number cannot be empty');
    }
    if (client.name.isEmpty) {
      throw pdf_models.PDFGenerationException('Client name cannot be empty');
    }
    if (businessInfo.name.isEmpty) {
      throw pdf_models.PDFGenerationException('Business name cannot be empty');
    }
    if (invoice.total < 0) {
      throw pdf_models.PDFGenerationException('Invoice total cannot be negative');
    }
  }

  /// Check if invoice is considered large (for progress tracking)
  static bool isLargeInvoice(Invoice invoice, List<TimeEntry>? timeEntries) {
    final itemCount = invoice.additionalItems.length + (timeEntries?.length ?? 0);
    return itemCount > 50 || (timeEntries?.length ?? 0) > 100;
  }

  /// Validate template parameters
  static void validateTemplateParameters(pdf_models.PDFGenerationParams params) {
    validatePDFParameters(
      invoice: params.invoice,
      client: params.client,
      businessInfo: params.businessInfo.businessInfo,
    );

    if (params.language.isEmpty) {
      throw pdf_models.PDFGenerationException('Language cannot be empty');
    }
  }
}
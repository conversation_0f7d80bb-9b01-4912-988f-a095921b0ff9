import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_models.dart' as pdf_models;
import 'package:time_tracker_flutter/services/pdf/pdf_formatter.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_widgets.dart';

/// Factory class for creating PDF templates
class PDFTemplateFactory {
  /// Create template based on template type
  static PDFTemplate createTemplate(InvoiceTemplate templateType) {
    switch (templateType) {
      case InvoiceTemplate.professional:
        return ProfessionalTemplate();
      case InvoiceTemplate.minimal:
        return MinimalTemplate();
      case InvoiceTemplate.detailed:
        return DetailedTemplate();
      default:
        return ProfessionalTemplate();
    }
  }
}

/// Abstract base class for PDF templates
abstract class PDFTemplate {
  /// Generate the PDF page
  Future<void> generatePage(
    pw.Document pdf,
    pdf_models.PDFGenerationParams params,
    pw.Font font,
    pw.Font boldFont,
  );
}

/// Professional template implementation
class ProfessionalTemplate implements PDFTemplate {
  @override
  Future<void> generatePage(
    pw.Document pdf,
    pdf_models.PDFGenerationParams params,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            PDFWidgets.buildHeader(params.businessInfo, boldFont, font, params.language),
            pw.SizedBox(height: 30),
            PDFWidgets.buildInvoiceTitle(params.invoice, boldFont, params.locale, params.language),
            pw.SizedBox(height: 20),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  child: PDFWidgets.buildClientInfo(params.client, boldFont, font, params.language),
                ),
                pw.SizedBox(width: 40),
                pw.Expanded(
                  child: PDFWidgets.buildInvoiceInfo(params.invoice, boldFont, font, params.locale, params.language),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            PDFWidgets.buildLineItemsTable(params.invoice, params.timeEntries, boldFont, font, params.locale, params.language),
            pw.SizedBox(height: 20),
            PDFWidgets.buildTotalsSection(params.invoice, boldFont, font, params.locale, params.language),
            pw.SizedBox(height: 30),
            if (params.invoice.notes != null && params.invoice.notes!.isNotEmpty)
              PDFWidgets.buildNotesSection(params.invoice.notes!, boldFont, font, params.language),
            pw.Spacer(),
            PDFWidgets.buildFooter(params.businessInfo, font, params.language),
          ];
        },
      ),
    );
  }
}

/// Minimal template implementation
class MinimalTemplate implements PDFTemplate {
  @override
  Future<void> generatePage(
    pw.Document pdf,
    pdf_models.PDFGenerationParams params,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    params.businessInfo.name,
                    style: pw.TextStyle(font: boldFont, fontSize: 18),
                  ),
                  pw.Text(
                    PDFFormatter.getTranslation('invoice', params.language),
                    style: pw.TextStyle(font: boldFont, fontSize: 24),
                  ),
                ],
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${PDFFormatter.getTranslation('invoice_number', params.language)}${params.invoice.invoiceNumber}',
                style: pw.TextStyle(font: boldFont, fontSize: 14),
              ),
              pw.Text(
                '${PDFFormatter.getTranslation('date', params.language)}: ${PDFFormatter.formatDate(params.invoice.issueDate, params.locale)}',
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              if (params.invoice.servicePeriodStart != null && params.invoice.servicePeriodEnd != null)
                pw.Text(
                  '${PDFFormatter.getTranslation('service_period', params.language)}: ${PDFFormatter.formatDate(params.invoice.servicePeriodStart!, params.locale)} - ${PDFFormatter.formatDate(params.invoice.servicePeriodEnd!, params.locale)}',
                  style: pw.TextStyle(font: font, fontSize: 12),
                ),
              pw.SizedBox(height: 20),
              pw.Text(
                PDFFormatter.getTranslation('bill_to', params.language),
                style: pw.TextStyle(font: boldFont, fontSize: 12),
              ),
              pw.Text(
                params.client.name,
                style: pw.TextStyle(font: font, fontSize: 12),
              ),
              if (params.client.address != null)
                pw.Text(
                  params.client.address!.formattedAddress,
                  style: pw.TextStyle(font: font, fontSize: 10),
                ),
              pw.SizedBox(height: 20),
              PDFWidgets.buildSimpleLineItems(params.invoice, params.timeEntries, boldFont, font, params.locale, params.language),
              pw.SizedBox(height: 20),
              pw.Align(
                alignment: pw.Alignment.centerRight,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text(
                      '${PDFFormatter.getTranslation('subtotal', params.language)}: ${PDFFormatter.formatCurrency(params.invoice.subtotal, params.invoice.currency, params.locale)}',
                      style: pw.TextStyle(font: font, fontSize: 12),
                    ),
                    if (params.invoice.taxAmount > 0)
                      pw.Text(
                        '${PDFFormatter.getTranslation('tax', params.language)} (${params.invoice.taxRate.toStringAsFixed(1)}%): ${PDFFormatter.formatCurrency(params.invoice.taxAmount, params.invoice.currency, params.locale)}',
                        style: pw.TextStyle(font: font, fontSize: 12),
                      ),
                    pw.Container(
                      padding: const pw.EdgeInsets.only(top: 5),
                      decoration: const pw.BoxDecoration(
                        border: pw.Border(
                          top: pw.BorderSide(width: 1),
                        ),
                      ),
                      child: pw.Text(
                        '${PDFFormatter.getTranslation('total', params.language)}: ${PDFFormatter.formatCurrency(params.invoice.total, params.invoice.currency, params.locale)}',
                        style: pw.TextStyle(font: boldFont, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Detailed template implementation
class DetailedTemplate implements PDFTemplate {
  @override
  Future<void> generatePage(
    pw.Document pdf,
    pdf_models.PDFGenerationParams params,
    pw.Font font,
    pw.Font boldFont,
  ) async {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return [
            PDFWidgets.buildDetailedHeader(params.businessInfo, boldFont, font, params.language),
            pw.SizedBox(height: 30),
            PDFWidgets.buildDetailedInvoiceTitle(params.invoice, boldFont, font, params.locale, params.language),
            pw.SizedBox(height: 20),
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Expanded(
                  child: PDFWidgets.buildDetailedClientInfo(params.client, boldFont, font, params.language),
                ),
                pw.SizedBox(width: 40),
                pw.Expanded(
                  child: PDFWidgets.buildDetailedInvoiceInfo(params.invoice, boldFont, font, params.locale, params.language),
                ),
              ],
            ),
            pw.SizedBox(height: 30),
            if (params.timeEntries != null && params.timeEntries!.isNotEmpty)
              PDFWidgets.buildDetailedTimeEntries(params.timeEntries!, params.invoice, boldFont, font, params.locale, params.language),
            if (params.invoice.additionalItems.isNotEmpty)
              PDFWidgets.buildDetailedLineItems(params.invoice, boldFont, font, params.locale, params.language),
            pw.SizedBox(height: 20),
            PDFWidgets.buildDetailedTotalsSection(params.invoice, boldFont, font, params.locale, params.language),
            pw.SizedBox(height: 30),
            PDFWidgets.buildPaymentTerms(params.invoice, boldFont, font, params.locale, params.language),
            if (params.invoice.notes != null && params.invoice.notes!.isNotEmpty)
              PDFWidgets.buildNotesSection(params.invoice.notes!, boldFont, font, params.language),
            pw.Spacer(),
            PDFWidgets.buildDetailedFooter(params.businessInfo, font, params.language),
          ];
        },
      ),
    );
  }
} 
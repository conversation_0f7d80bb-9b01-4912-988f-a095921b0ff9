import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/report_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class ReportingService {
  final DatabaseService _databaseService = DatabaseService();

  int _timeToMinutes(String time) {
    if (time.isEmpty) return 0;
    if (time.contains('-')) {
      try {
        final parts = time.split('-');
        final start = parts[0].trim();
        final end = parts[1].trim();

        final startParts = start.split(':').map(int.parse).toList();
        final endParts = end.split(':').map(int.parse).toList();

        final startMinutes = startParts[0] * 60 + startParts[1];
        final endMinutes = endParts[0] * 60 + endParts[1];

        return endMinutes - startMinutes;
      } catch (e) {
        // Handle potential parsing errors for HH:mm-HH:mm format
        print("Error parsing time range $time: $e");
        return 0;
      }
    } else {
      try {
        final parts = time.split(':').map(int.parse).toList();
        return parts[0] * 60 + parts[1];
      } catch (e) {
        // Handle potential parsing errors for HH:mm format
         print("Error parsing duration $time: $e");
        return 0;
      }
    }
  }

  String _minutesToTime(int totalMinutes) {
    if (totalMinutes < 0) totalMinutes = 0;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  Future<TimeReport> generateReport(
      DateTimeRange dateRange, {
        String? projectId,
      }) async {
    await _databaseService.initialize();

    final List<Project> allProjects = await _databaseService.getProjects();
    final List<TimeEntry> allEntries = await _databaseService.getTimeEntries(
      projectId: projectId,
      startDate: dateRange.start,
      endDate: dateRange.end,
    );

    final Map<String, ProjectReport> projectBreakdown = {};
    int grandTotalMinutes = 0;

    // Filter projects based on selected projectId or include all
    final List<Project> relevantProjects = projectId == null
        ? allProjects
        : allProjects.where((p) => p.id == projectId).toList();

    // Create a map for quick project lookup
    final projectMap = {for (var p in allProjects) p.id: p};

    // Group entries by project
    final Map<String, List<TimeEntry>> entriesByProject = {};
    for (final entry in allEntries) {
      if (!entriesByProject.containsKey(entry.projectId)) {
        entriesByProject[entry.projectId] = [];
      }
      entriesByProject[entry.projectId]!.add(entry);
    }

    // Process each relevant project
    for (final project in relevantProjects) {
      final projectEntries = entriesByProject[project.id] ?? [];

      // Sort entries by date descending
      projectEntries.sort((a, b) => b.date.compareTo(a.date));

      int projectTotalMinutes = 0;
      final List<ReportEntry> reportEntries = [];

      for (final entry in projectEntries) {
        String durationStr;
        int entryMinutes = 0;
        String startStr = '';
        String endStr = '';

        if (entry.start != null && entry.end != null) {
           // Assuming start/end are in HH:mm format already from TimeEntry
           startStr = entry.start!;
           endStr = entry.end!;
           durationStr = '$startStr-$endStr';
           entryMinutes = _timeToMinutes(durationStr); // Use helper to parse HH:mm-HH:mm
        } else if (entry.duration != null) {
           // Assuming duration is HH:mm format
           durationStr = entry.duration!;
           entryMinutes = _timeToMinutes(durationStr); // Use helper to parse HH:mm
        } else {
          durationStr = '00:00'; // Or handle as error
          entryMinutes = 0;
        }

        projectTotalMinutes += entryMinutes;
        DateTime entryDate;
        try {
           entryDate = DateTime.parse(entry.date);
        } catch (_) {
          // Handle invalid date format - skip entry or use a default?
          debugPrint("Skipping entry due to invalid date format: ${entry.date}");
          continue; // Skip this entry
        }

        reportEntries.add(ReportEntry(
          date: DateFormat('yyyy-MM-dd').format(entryDate), // Keep ISO format for data consistency
          start: startStr,
          end: endStr,
          duration: durationStr,
        ));
      }

      // Only add project to report if it has entries in the date range
      if (reportEntries.isNotEmpty) {
        projectBreakdown[project.id] = ProjectReport(
          projectId: project.id,
          projectName: project.name,
          order: project.order,
          totalHours: _minutesToTime(projectTotalMinutes),
          entries: reportEntries,
        );
        grandTotalMinutes += projectTotalMinutes;
      }
    }

    // Sort projects by their order field
    final sortedProjectBreakdown = Map.fromEntries(
      projectBreakdown.entries.toList()
        ..sort((a, b) => a.value.order.compareTo(b.value.order)),
    );

    return TimeReport(
      totalHours: _minutesToTime(grandTotalMinutes),
      projectBreakdown: sortedProjectBreakdown,
      dateRange: dateRange,
    );
  }


  Future<List<List<String>>> generateCsvData(TimeReport report) async {
    final List<List<String>> csvData = [
      ['Project', 'Date', 'Duration', 'Project Total'] // Header row
    ];

    final sortedProjects = report.projectBreakdown.values.toList()
                           ..sort((a, b) => a.order.compareTo(b.order));

    for (final projectReport in sortedProjects) {
      // Calculate running total for the project (display total at the start)
       final projectTotal = projectReport.totalHours;

       // Add project entries
      bool firstEntry = true;
      for (final entry in projectReport.entries) {
        csvData.add([
          projectReport.projectName,
          entry.date,
          entry.duration,
          firstEntry ? projectTotal : '', // Show total only on the first row
        ]);
        firstEntry = false;
      }
      // Add a blank row between projects if not the last project
      if (projectReport.projectId != sortedProjects.last.projectId) {
        csvData.add(['', '', '', '']);
      }
    }

    return csvData;
  }
} 
import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/utils/crypto_utils.dart';

/// Service for managing cloud backups with encryption
class CloudBackupService {
  // Constants
  static const String _supabaseUrl = 'https://nvwgsvoxqpjtzxueennu.supabase.co';
  static const String _supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im52d2dzdm94cXBqdHp4dWVlbm51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA4NzczMzEsImV4cCI6MjA1NjQ1MzMzMX0.FsLis1Iu9mrIZYelejJBcchPsvHlTGChDBfKejMgZd8';
  static const String _bucketName = 'backups';
  static const String _settingsBoxName = 'cloud_backup_settings';
  static const String _recoveryCodeKey = 'cloud_backup_recovery_code';
  static const String _recoveryCodeGeneratedKey = 'cloud_backup_recovery_code_generated';
  static const String _encryptionKeyKey = 'cloud_backup_encryption_key';
  static const String _userIdKey = 'cloud_backup_user_id';
  static const int _lookupKeyLength = 16;
  static const int _keyHashLength = 8;
  static const String _recoveryCodeChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  static const Duration _cacheValidDuration = Duration(minutes: 3);

  // Singleton pattern with proper async initialization
  static CloudBackupService? _instance;
  static Completer<CloudBackupService>? _initCompleter;

  // Core state - these will be set during initialization
  late String _userId;
  late String _encryptionKey;
  String? _recoveryCode;
  String? _lookupKey;
  bool _hasGeneratedRecoveryCode = false;

  // Cache for backup listing
  List<String>? _cachedBackupList;
  DateTime? _backupListCacheTime;

  // Private constructor
  CloudBackupService._();

  // Factory constructor that returns cached instance or throws if not initialized
  factory CloudBackupService() => _instance ??
    (throw Exception('CloudBackupService not initialized. Call CloudBackupService.initialize() first.'));

  // Static initialization method
  static Future<CloudBackupService> initialize() async {
    if (_instance != null) return _instance!;

    if (_initCompleter?.isCompleted == true) return _initCompleter!.future;

    _initCompleter = Completer<CloudBackupService>();

    try {
      final service = CloudBackupService._();
      await service._performInitialization();
      _instance = service;
      _initCompleter!.complete(service);
      return service;
    } catch (e) {
      _initCompleter = null;
      rethrow;
    }
  }

  // Getters with expression bodies
  String get userId => _userId;
  bool get isInitialized => _instance != null;
  bool get hasRecoveryCodeBeenGenerated => _hasGeneratedRecoveryCode;
  SupabaseClient get _supabase => Supabase.instance.client;

  // Actual initialization logic
  Future<void> _performInitialization() async {
    try {
      await Supabase.initialize(url: _supabaseUrl, anonKey: _supabaseAnonKey);
      await _loadDataFromStorage();

      if (kDebugMode) _logStorageState();
    } catch (e) {
      debugPrint('Error initializing CloudBackupService: $e');
      rethrow;
    }
  }

  // Load all data from storage with proper initialization
  Future<void> _loadDataFromStorage() async {
    try {
      final prefs = await Hive.openBox(_settingsBoxName);

      // Load or generate user ID
      _userId = prefs.get(_userIdKey) as String? ?? await _generateAndSaveId(_userIdKey, 16);

      // Load or generate encryption key
      _encryptionKey = prefs.get(_encryptionKeyKey) as String? ?? await _generateAndSaveId(_encryptionKeyKey, 32);

      // Load optional recovery code
      _recoveryCode = prefs.get(_recoveryCodeKey) as String?;
      _lookupKey = _recoveryCode?.let((code) => _generateLookupKey(_normalizeRecoveryCode(code)));

      // Load recovery code generated flag
      _hasGeneratedRecoveryCode = prefs.get(_recoveryCodeGeneratedKey) as bool? ?? false;
    } catch (e) {
      debugPrint('Error loading data from storage: $e');
      rethrow;
    }
  }

  Future<String> _generateAndSaveId(String key, int length) async {
    final id = _generateSecureId(length);
    await _saveToSettings(key, id);
    return id;
  }

  void _logStorageState() => debugPrint(
    'Recovery code: ${_recoveryCode != null ? 'Yes' : 'No'}, '
    'Generated: $_hasGeneratedRecoveryCode, '
    'Keys loaded: Yes'
  );

  String _generateSecureId(int length) {
    final random = Random.secure();
    final values = List<int>.generate(length, (_) => random.nextInt(256));
    return base64Url.encode(values).replaceAll('=', '');
  }

  Future<void> _saveToSettings(String key, dynamic value) async {
    try {
      final prefs = await Hive.openBox(_settingsBoxName);
      await prefs.put(key, value);
    } catch (e) {
      debugPrint('Error saving to settings: $e');
      rethrow;
    }
  }

  // Reset recovery key for testing
  Future<void> resetRecoveryKey() async {
    final prefs = await Hive.openBox(_settingsBoxName);

    _recoveryCode = null;
    _lookupKey = null;
    _hasGeneratedRecoveryCode = false;

    await Future.wait([
      prefs.delete(_recoveryCodeKey),
      prefs.delete(_recoveryCodeGeneratedKey),
      prefs.delete(_encryptionKeyKey),
      prefs.delete(_userIdKey),
    ]);

    debugPrint('Recovery key has been reset for testing recovery');
  }

  // Generate a recovery code for the user
  Future<String> generateRecoveryCode() async {
    if (_recoveryCode != null && _hasGeneratedRecoveryCode) return _recoveryCode!;

    _recoveryCode = _generateFormattedRecoveryCode();
    _lookupKey = _generateLookupKey(_normalizeRecoveryCode(_recoveryCode!));

    final newEncryptionKey = _getDerivedKey(_userId, _recoveryCode!);

    await Future.wait([
      _saveToSettings(_recoveryCodeKey, _recoveryCode),
      _saveToSettings(_encryptionKeyKey, newEncryptionKey),
    ]);

    return _recoveryCode!;
  }

  String _generateFormattedRecoveryCode() {
    final random = Random.secure();
    final codeChars = List<String>.generate(16, (_) =>
      _recoveryCodeChars[random.nextInt(_recoveryCodeChars.length)]
    );

    return [0, 4, 8, 12]
      .map((i) => codeChars.sublist(i, i + 4).join(''))
      .join('-');
  }

  String _normalizeRecoveryCode(String code) => code.replaceAll('-', '').toUpperCase();

  String getRecoveryCode() => _recoveryCode ??
    (throw Exception('Recovery code has not been generated yet. Call generateRecoveryCode() first.'));

  Future<void> markRecoveryCodeAsSaved() async {
    if (_recoveryCode == null) {
      throw Exception('Recovery code has not been generated yet. Call generateRecoveryCode() first.');
    }

    _hasGeneratedRecoveryCode = true;
    await _saveToSettings(_recoveryCodeGeneratedKey, true);
  }

  String _generateLookupKey(String code) {
    final normalizedCode = _normalizeRecoveryCode(code);
    final bytes = utf8.encode(normalizedCode);
    return sha256.convert(bytes).toString().substring(0, _lookupKeyLength);
  }

  String _getKeyHash(String key) {
    final bytes = utf8.encode(key);
    return sha256.convert(bytes).toString().substring(0, _keyHashLength);
  }

  String _getDerivedKey(String userId, String recoveryCode) {
    final combined = '$userId$recoveryCode';
    final bytes = utf8.encode(combined);
    return sha256.convert(bytes).toString();
  }

  String _generateBackupFilename(String timestamp) {
    _validateBackupState();
    final keyHash = _getKeyHash(_encryptionKey);
    return '${_lookupKey}_${_userId}_${keyHash}_$timestamp.enc';
  }

  void _validateBackupState() {
    _lookupKey ??= _recoveryCode?.let((code) => _generateLookupKey(_normalizeRecoveryCode(code))) ??
      (throw Exception('Lookup key not set before generating filename.'));
  }

  // Upload a backup to Supabase
  Future<String> uploadBackup(BackupData backupData) async {
    try {
      final dataString = jsonEncode(backupData.toJson());
      final encryptedData = await CryptoUtils.encryptData(dataString, _encryptionKey);

      final timestamp = DateTime.now().toUtc().toIso8601String().replaceAll(':', '-');
      final filename = _generateBackupFilename(timestamp);

      final bytes = Uint8List.fromList(utf8.encode(encryptedData));

      await _supabase.storage
          .from(_bucketName)
          .uploadBinary(filename, bytes, fileOptions: const FileOptions(
            contentType: 'application/encrypted',
          ));

      _invalidateCache();
      return filename;
    } catch (e) {
      debugPrint('Error uploading backup: $e');
      rethrow;
    }
  }

  // List all backups for the current user
  Future<List<String>> listBackups({bool forceRefresh = false}) async {
    try {
      debugPrint('listBackups called - forceRefresh: $forceRefresh');

      _ensureLookupKey();
      debugPrint('Lookup key: ${_lookupKey!.substring(0, 8)}...');

      if (!forceRefresh && _isBackupListCacheValid()) {
        debugPrint('Using cached backup list with ${_cachedBackupList!.length} items');
        return _cachedBackupList!;
      }

      debugPrint('Fetching backup list from storage...');
      final result = await _supabase.storage
          .from(_bucketName)
          .list(path: '', searchOptions: SearchOptions(search: '${_lookupKey}_'));

      debugPrint('Storage query returned ${result.length} files');

      if (result.isEmpty) {
        debugPrint('No backup files found, caching empty list');
        _cacheBackupList([]);
        return [];
      }

      final names = result.map((file) => file.name).toList();
      debugPrint('Found backup files: ${names.take(3).join(', ')}${names.length > 3 ? '...' : ''}');

      _sortBackupFilesByDate(names);
      _cacheBackupList(names);

      debugPrint('Returning ${names.length} backup files after sorting and caching');
      return names;
    } catch (e) {
      debugPrint('Error listing backups: $e');
      rethrow;
    }
  }

  void _ensureLookupKey() {
    _lookupKey ??= _recoveryCode?.let((code) => _generateLookupKey(_normalizeRecoveryCode(code))) ??
      (throw Exception('Lookup key not set. Recovery code must be generated or set first.'));
  }

  // Download and decrypt a backup
  Future<BackupData> downloadBackup(String? filename) async {
    final backupFilename = filename ?? await _getLatestBackupFilename();

    try {
      final parsedData = _parseBackupFilename(backupFilename);
      final (userId: parsedUserId, keyHash: expectedKeyHash) = parsedData;

      final bytes = await _supabase.storage.from(_bucketName).download(backupFilename);
      final encryptedData = utf8.decode(bytes);

      final decryptionKey = _getDerivedKey(parsedUserId, _recoveryCode!);

      if (_getKeyHash(decryptionKey) != expectedKeyHash) {
        throw Exception('Key hash mismatch');
      }

      final decryptedData = await CryptoUtils.decryptData(encryptedData, decryptionKey);
      final jsonData = jsonDecode(decryptedData);
      return BackupData.fromJson(jsonData);
    } catch (e) {
      debugPrint('Error downloading backup: $e');
      rethrow;
    }
  }

  Future<String> _getLatestBackupFilename() async {
    final backups = await listBackups();
    if (backups.isEmpty) throw Exception('No backups found');
    return backups.first;
  }

  // Download multiple backups sequentially
  Future<List<BackupData>> downloadBackups(List<String> filenames) async {
    if (filenames.isEmpty) return [];

    final results = <BackupData>[];
    for (final filename in filenames) {
      try {
        final backup = await downloadBackup(filename);
        results.add(backup);
      } catch (e) {
        debugPrint('Error downloading backup $filename: $e');
      }
    }
    return results;
  }

  // Delete a backup
  Future<List<String>> deleteBackup(String filename) async {
    try {
      await _supabase.storage.from(_bucketName).remove([filename]);
      _invalidateCache();
      return await listBackups(forceRefresh: true);
    } catch (e) {
      debugPrint('Error deleting backup: $e');
      rethrow;
    }
  }

  // Restore from a recovery key
  Future<bool> restoreFromRecoveryKey(String recoveryKey) async {
    try {
      debugPrint('Starting recovery with key: ${recoveryKey.substring(0, 4)}...');

      final normalizedCode = _normalizeRecoveryCode(recoveryKey);
      if (normalizedCode.length != 16) {
        debugPrint('Invalid recovery key length: ${normalizedCode.length}');
        return false;
      }

      final formattedCode = _formatRecoveryCode(normalizedCode);
      final lookupKey = _generateLookupKey(normalizedCode);

      debugPrint('Generated lookup key: ${lookupKey.substring(0, 8)}...');

      final result = await _supabase.storage
          .from(_bucketName)
          .list(path: '', searchOptions: SearchOptions(search: '${lookupKey}_'));

      final potentialFiles = result.where((file) => file.name.startsWith('${lookupKey}_')).toList();
      if (potentialFiles.isEmpty) {
        debugPrint('No backup files found for lookup key');
        return false;
      }

      debugPrint('Found ${potentialFiles.length} potential backup files');

      // Get the most recent file for validation
      final filesWithDates = await _createFilesWithDates(potentialFiles);
      if (filesWithDates.isEmpty) {
        debugPrint('No files with valid dates found');
        return false;
      }

      final mostRecentFile = filesWithDates.first['file'] as FileObject;
      debugPrint('Using most recent file: ${mostRecentFile.name}');

      final oldRecoveryCode = _recoveryCode;
      _recoveryCode = formattedCode;

      try {
        // Only try the most recent file for validation
        final parsedData = _parseBackupFilename(mostRecentFile.name);
        final (userId: fileUserId, keyHash: fileKeyHash) = parsedData;

        debugPrint('Parsed file - userId: ${fileUserId.substring(0, 8)}..., keyHash: $fileKeyHash');

        final derivedKey = _getDerivedKey(fileUserId, formattedCode);
        final computedKeyHash = _getKeyHash(derivedKey);

        debugPrint('Computed key hash: $computedKeyHash, expected: $fileKeyHash');

        if (computedKeyHash != fileKeyHash) {
          debugPrint('Key hash mismatch - recovery key does not match this backup');
          _recoveryCode = oldRecoveryCode;
          return false;
        }

        debugPrint('Key hash validation passed, downloading backup...');
        // Validate by downloading this one backup
        await downloadBackup(mostRecentFile.name);

        debugPrint('Backup downloaded successfully, saving credentials...');
        await _saveRecoveredCredentials(fileUserId, derivedKey, formattedCode, lookupKey);

        debugPrint('Recovery completed successfully');
        return true;
      } catch (e) {
        debugPrint('Error during recovery validation: $e');
        _recoveryCode = oldRecoveryCode;
        return false;
      }
    } catch (e) {
      debugPrint('Error during restore process: $e');
      return false;
    }
  }

  String _formatRecoveryCode(String normalizedCode) =>
    [0, 4, 8, 12].map((i) => normalizedCode.substring(i, i + 4)).join('-');

  Future<List<Map<String, dynamic>>> _createFilesWithDates(List<FileObject> files) async {
    final filesWithDates = <Map<String, dynamic>>[];

    for (final file in files) {
      try {
        final date = await extractDateFromBackupFilename(file.name);
        if (date != null) filesWithDates.add({'file': file, 'date': date});
      } catch (e) {
        // Skip files with unparseable dates
      }
    }

    filesWithDates.sort((a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));
    return filesWithDates;
  }

  Future<void> _saveRecoveredCredentials(String userId, String encryptionKey, String recoveryCode, String lookupKey) async {
    _userId = userId;
    _encryptionKey = encryptionKey;
    _recoveryCode = recoveryCode;
    _lookupKey = lookupKey;
    _hasGeneratedRecoveryCode = true;

    final prefs = await Hive.openBox(_settingsBoxName);
    await Future.wait([
      prefs.put(_userIdKey, _userId),
      prefs.put(_encryptionKeyKey, _encryptionKey),
      prefs.put(_recoveryCodeKey, _recoveryCode),
      prefs.put(_recoveryCodeGeneratedKey, true),
    ]);

    // Invalidate backup list cache after recovery to force refresh
    _invalidateCache();
    debugPrint('Cache invalidated after credential recovery');
  }

  // Apply transfer data from QR code
  Future<bool> applyTransferData(String transferDataStr) async {
    try {
      final transferData = jsonDecode(transferDataStr);

      if (!_isValidTransferData(transferData)) return false;

      final userId = transferData['userId'] as String;
      final encryptionKey = transferData['encryptionKey'] as String;
      final recoveryCode = transferData['recoveryCode'] as String;

      if (!_validateTransferredEncryptionKey(userId, recoveryCode, encryptionKey)) {
        debugPrint('Warning: Transferred encryption key does not match derivation');
      }

      final lookupKey = _generateLookupKey(_normalizeRecoveryCode(recoveryCode));
      await _saveRecoveredCredentials(userId, encryptionKey, recoveryCode, lookupKey);

      return true;
    } catch (e) {
      debugPrint('Error applying transfer data: $e');
      return false;
    }
  }

  bool _isValidTransferData(dynamic transferData) =>
    transferData is Map &&
    transferData.containsKey('userId') &&
    transferData.containsKey('encryptionKey') &&
    transferData.containsKey('recoveryCode');

  bool _validateTransferredEncryptionKey(String userId, String recoveryCode, String encryptionKey) =>
    _getDerivedKey(userId, recoveryCode) == encryptionKey;

  ({String userId, String keyHash}) _parseBackupFilename(String filename) {
    try {
      final parts = filename.replaceAll('.enc', '').split('_');
      if (parts.length < 4) {
        throw Exception('Invalid backup filename format: expected at least 4 parts separated by underscores.');
      }

      return (userId: parts[1], keyHash: parts[2]);
    } catch (e) {
      debugPrint('Error parsing filename: $e');
      throw Exception('Invalid backup filename format: $e');
    }
  }

  Future<DateTime?> extractDateFromBackupFilename(String filename) async {
    try {
      final parts = filename.replaceAll('.enc', '').split('_');
      if (parts.length < 4) return null;

      final timestamp = parts.sublist(3).join('_');

      return switch (timestamp) {
        String t when t.contains('T') => _tryParseDateWithTSeparator(t),
        String t => _tryParseDateWithRegex(t) ?? _tryParseDateManually(t),
      };
    } catch (e) {
      return null;
    }
  }

  DateTime? _tryParseDateWithTSeparator(String timestamp) {
    try {
      final parts = timestamp.split('T');
      if (parts.length == 2) {
        final datePart = parts[0];
        final timePart = parts[1].replaceAll('-', ':');
        return DateTime.parse('${datePart}T$timePart');
      }
    } catch (e) {
      // Fall through to return null
    }
    return null;
  }

  DateTime? _tryParseDateWithRegex(String timestamp) {
    try {
      final dateTimeRegex = RegExp(r'(\d{4}-\d{2}-\d{2})-(\d{2}-\d{2}-\d{2})');
      final match = dateTimeRegex.firstMatch(timestamp);

      return switch (match) {
        RegExpMatch m when m.group(1) != null && m.group(2) != null =>
          DateTime.parse('${m.group(1)}T${m.group(2)!.replaceAll('-', ':')}'),
        _ => null,
      };
    } catch (e) {
      return null;
    }
  }

  DateTime? _tryParseDateManually(String timestamp) {
    try {
      final allParts = timestamp.split('-');
      if (allParts.length >= 6) {
        return DateTime(
          int.parse(allParts[0]),
          int.parse(allParts[1]),
          int.parse(allParts[2]),
          int.parse(allParts[3]),
          int.parse(allParts[4]),
          int.parse(allParts[5].split('.')[0]),
        );
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  // Get transfer data for QR code
  Future<Map<String, dynamic>> getTransferData() async {
    if (_recoveryCode == null) {
      throw Exception('Recovery code not set. Please generate a recovery code first.');
    }

    return {
      'userId': _userId,
      'encryptionKey': _encryptionKey,
      'recoveryCode': _recoveryCode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  // Cache management with expression bodies
  void _invalidateCache() {
    _cachedBackupList = null;
    _backupListCacheTime = null;
  }

  bool _isBackupListCacheValid() =>
    _cachedBackupList != null &&
    _backupListCacheTime != null &&
    DateTime.now().difference(_backupListCacheTime!) < _cacheValidDuration;

  void _cacheBackupList(List<String> backupList) {
    _cachedBackupList = backupList;
    _backupListCacheTime = DateTime.now();
  }

  void _sortBackupFilesByDate(List<String> files) {
    files.sort((a, b) {
      try {
        final partsA = a.split('-');
        final partsB = b.split('-');
        final timestampA = partsA.sublist(1).join('-');
        final timestampB = partsB.sublist(1).join('-');
        return timestampB.compareTo(timestampA);
      } catch (e) {
        return 0;
      }
    });
  }

  // Static initialization method for backward compatibility
  static Future<void> initializeService() async {
    await initialize();
  }

  // Instance method for backward compatibility
  Future<void> initializeInstance() async {
    await CloudBackupService.initialize();
  }
}

// Extension for optional chaining
extension LetExtension<T> on T? {
  R? let<R>(R Function(T) transform) => this != null ? transform(this as T) : null;
}

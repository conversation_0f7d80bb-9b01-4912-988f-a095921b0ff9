import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';

/// Service for managing invoice creation, calculations, and business logic
class InvoiceService {
  final DatabaseService _databaseService;

  InvoiceService({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService();

  /// Creates an invoice from selected time entries
  Future<Invoice> createInvoiceFromTimeEntries({
    required List<TimeEntry> timeEntries,
    required String clientId,
    String? currency,
    String? locale,
    double? taxRate,
    DateTime? issueDate,
    DateTime? dueDate,
    String? notes,
    Map<String, double>? customRates, // projectId -> rate
    DateTime? servicePeriodStart,
    DateTime? servicePeriodEnd,
  }) async {
    if (timeEntries.isEmpty) {
      throw ArgumentError('Cannot create invoice with no time entries');
    }

    // Get settings for defaults
    final settings = await _databaseService.getInvoiceSettings();

    // Generate invoice number
    final invoiceNumber = await generateInvoiceNumber();

    // Group time entries by project to create line items
    final Map<String, List<TimeEntry>> entriesByProject = {};
    for (final entry in timeEntries) {
      entriesByProject.putIfAbsent(entry.projectId, () => []).add(entry);
    }

    // Create line items from time entries
    final List<InvoiceLineItem> lineItems = [];
    for (final projectId in entriesByProject.keys) {
      final projectEntries = entriesByProject[projectId]!;
      final project = await _databaseService.getProject(projectId);

      if (project == null) {
        throw StateError('Project not found for ID: $projectId');
      }

      // Calculate total hours for this project
      double totalHours = 0.0;
      for (final entry in projectEntries) {
        totalHours += _calculateHoursFromTimeEntry(entry);
      }

      // Use custom rate if provided, otherwise use default rate
      final rate = customRates?[projectId] ?? 50.0;

      final lineItem = InvoiceLineItem(
        description: '${project.name} - Time tracking',
        quantity: _roundToTwoDecimals(totalHours),
        rate: rate,
        type: InvoiceLineItemType.timeEntry,
      );

      lineItems.add(lineItem);
    }

    // Calculate due date using settings default if not provided
    final calculatedDueDate = dueDate ??
        (issueDate ?? DateTime.now()).add(Duration(days: settings.defaultDueDays));

    // Calculate totals first
    double subtotal = 0.0;
    for (final item in lineItems) {
      item.recalculateAmount();
      subtotal += item.amount;
    }

    final actualTaxRate = taxRate ?? settings.defaultTaxRate;
    final taxAmount = _roundToTwoDecimals(subtotal * (actualTaxRate / 100));
    final total = _roundToTwoDecimals(subtotal + taxAmount);

    // Create invoice with calculated totals
    final invoice = Invoice(
      invoiceNumber: invoiceNumber,
      clientId: clientId,
      timeEntryIds: timeEntries.map((e) => e.id).toList(),
      additionalItems: lineItems,
      currency: currency ?? settings.defaultCurrency,
      locale: locale ?? settings.defaultLocale,
      issueDate: issueDate ?? DateTime.now(),
      dueDate: calculatedDueDate,
      taxRate: actualTaxRate,
      subtotal: subtotal,
      taxAmount: taxAmount,
      total: total,
      notes: notes ?? settings.defaultNotes,
      servicePeriodStart: servicePeriodStart,
      servicePeriodEnd: servicePeriodEnd,
    );

    return invoice;
  }

  /// Calculates subtotal, tax amount, and total for an invoice
  void calculateInvoiceTotals(Invoice invoice) {
    _calculateInvoiceTotals(invoice);
  }

    void _calculateInvoiceTotals(Invoice invoice) {
    // Use the invoice's built-in recalculateTotals method
    invoice.recalculateTotals();
  }

  /// Generates a unique invoice number using settings or custom format
  Future<String> generateInvoiceNumber({
    String? prefix,
    String? dateFormat,
    int? sequenceLength,
  }) async {
    // Get settings if no custom values provided
    final settings = await _databaseService.getInvoiceSettings();

    final actualPrefix = prefix ?? settings.invoiceNumberPrefix;
    final actualDateFormat = dateFormat ?? settings.invoiceNumberDateFormat;
    final actualSequenceLength = sequenceLength ?? settings.invoiceNumberSequenceLength;

    final now = DateTime.now();
    final dateStr = _formatDateForInvoiceNumber(now, actualDateFormat);

    // Get existing invoices to determine next sequence number
    final existingInvoices = await _databaseService.getInvoices();
    final currentMonthInvoices = existingInvoices.where((invoice) {
      return invoice.invoiceNumber.startsWith('$actualPrefix-$dateStr');
    }).toList();

    final nextSequence = currentMonthInvoices.length + 1;
    final sequenceStr = nextSequence.toString().padLeft(actualSequenceLength, '0');

    return '$actualPrefix-$dateStr-$sequenceStr';
  }

  /// Updates the status of an invoice
  Future<void> updateInvoiceStatus(String invoiceId, InvoiceStatus status) async {
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice == null) {
      throw ArgumentError('Invoice not found: $invoiceId');
    }

    final updatedInvoice = invoice.copyWith(
      status: status,
      updatedAt: DateTime.now().toIso8601String(),
    );

    await _databaseService.saveInvoice(updatedInvoice);
  }

  /// Gets invoices by date range
  Future<List<Invoice>> getInvoicesByDateRange(DateTime startDate, DateTime endDate) async {
    final allInvoices = await _databaseService.getInvoices();

    return allInvoices.where((invoice) {
      final issueDate = invoice.issueDate;
      return issueDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             issueDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Gets invoices by client
  Future<List<Invoice>> getInvoicesByClient(String clientId) async {
    final allInvoices = await _databaseService.getInvoices();

    return allInvoices.where((invoice) => invoice.clientId == clientId).toList();
  }

  /// Gets invoices by status
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    final allInvoices = await _databaseService.getInvoices();

    return allInvoices.where((invoice) => invoice.status == status).toList();
  }

  /// Adds a line item to an existing invoice
  Future<void> addLineItemToInvoice(String invoiceId, InvoiceLineItem lineItem) async {
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice == null) {
      throw ArgumentError('Invoice not found: $invoiceId');
    }

    final updatedItems = List<InvoiceLineItem>.from(invoice.additionalItems)
      ..add(lineItem);

    final updatedInvoice = invoice.copyWith(
      additionalItems: updatedItems,
    );

    _calculateInvoiceTotals(updatedInvoice);
    await _databaseService.saveInvoice(updatedInvoice);
  }

  /// Removes a line item from an existing invoice
  Future<void> removeLineItemFromInvoice(String invoiceId, String lineItemId) async {
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice == null) {
      throw ArgumentError('Invoice not found: $invoiceId');
    }

    final updatedItems = invoice.additionalItems
        .where((item) => item.id != lineItemId)
        .toList();

    final updatedInvoice = invoice.copyWith(
      additionalItems: updatedItems,
    );

    _calculateInvoiceTotals(updatedInvoice);
    await _databaseService.saveInvoice(updatedInvoice);
  }

  /// Updates a line item in an existing invoice
  Future<void> updateLineItemInInvoice(String invoiceId, InvoiceLineItem updatedLineItem) async {
    final invoice = await _databaseService.getInvoice(invoiceId);
    if (invoice == null) {
      throw ArgumentError('Invoice not found: $invoiceId');
    }

    final updatedItems = invoice.additionalItems.map((item) {
      return item.id == updatedLineItem.id ? updatedLineItem : item;
    }).toList();

    final updatedInvoice = invoice.copyWith(
      additionalItems: updatedItems,
    );

    _calculateInvoiceTotals(updatedInvoice);
    await _databaseService.saveInvoice(updatedInvoice);
  }

  /// Validates invoice data before saving
  bool validateInvoice(Invoice invoice) {
    if (!invoice.isValid()) {
      return false;
    }

    // Additional business logic validation
    if (invoice.taxRate < 0 || invoice.taxRate > 100) {
      return false;
    }

    if (invoice.subtotal < 0 || invoice.total < 0) {
      return false;
    }

    // Validate that totals are calculated correctly
    double expectedSubtotal = 0.0;
    for (final item in invoice.additionalItems) {
      expectedSubtotal += item.amount;
    }

    final expectedTaxAmount = expectedSubtotal * (invoice.taxRate / 100);
    final expectedTotal = expectedSubtotal + expectedTaxAmount;

    // Allow for small rounding differences
    const tolerance = 0.01;
    if ((invoice.subtotal - expectedSubtotal).abs() > tolerance ||
        (invoice.taxAmount - expectedTaxAmount).abs() > tolerance ||
        (invoice.total - expectedTotal).abs() > tolerance) {
      return false;
    }

    return true;
  }

  /// Marks overdue invoices based on due date
  Future<void> updateOverdueInvoices() async {
    final allInvoices = await _databaseService.getInvoices();
    final now = DateTime.now();

    for (final invoice in allInvoices) {
      if (invoice.dueDate != null &&
          invoice.status != InvoiceStatus.paid &&
          invoice.status != InvoiceStatus.cancelled &&
          now.isAfter(invoice.dueDate!)) {

        if (invoice.status != InvoiceStatus.overdue) {
          await updateInvoiceStatus(invoice.id, InvoiceStatus.overdue);
        }
      }
    }
  }

  // Helper methods

  double _calculateHoursFromTimeEntry(TimeEntry entry) {
    if (entry.duration != null) {
      return parseDurationToHours(entry.duration!);
    } else if (entry.start != null && entry.end != null) {
      return calculateHours(entry.start!, entry.end!);
    }
    return 0.0;
  }

  double _roundToTwoDecimals(double value) {
    return (value * 100).round() / 100;
  }

  String _formatDateForInvoiceNumber(DateTime date, String format) {
    switch (format) {
      case 'yyyy':
        return date.year.toString();
      case 'yyyyMM':
        return '${date.year}${date.month.toString().padLeft(2, '0')}';
      case 'yyyyMMdd':
        return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
      default:
        return '${date.year}${date.month.toString().padLeft(2, '0')}';
    }
  }
}
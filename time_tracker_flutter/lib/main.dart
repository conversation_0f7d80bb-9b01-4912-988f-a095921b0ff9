import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:time_tracker_flutter/screens/main_screen.dart';
import 'package:time_tracker_flutter/screens/projects_screen.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/theme/app_theme.dart';
import 'package:time_tracker_flutter/theme/theme_provider.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:time_tracker_flutter/debug_location_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize port for communication between TaskHandler and UI
  FlutterForegroundTask.initCommunicationPort();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Hive
  await Hive.initFlutter();

  // Request notification permissions for Android 13+ (API level 33+)
  if (!kIsWeb && Platform.isAndroid) {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    // Request permission for notifications
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }

  runApp(const MyApp());
}

// Helper function to check for active tracking areas
Future<void> _checkForActiveTracking() async {
  try {
    final locationService = LocationService();
    final databaseService = DatabaseService();

    // Get all active location areas
    final areas = await databaseService.getLocationAreas();
    final activeAreas = areas.where((area) => area.isActive);


    if (activeAreas.isNotEmpty) {
      // Initialize location service
      await LocationService.initializeService();
      // Start location tracking service which will automatically check all locations
      await locationService.startTracking();
    }
  } catch (e) {
    debugPrint('Error checking for active tracking areas: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _initializeServices(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MaterialApp(
            title: 'Time Tracker',
            debugShowCheckedModeBanner: false,
            home: const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }
        
        if (snapshot.hasError) {
          return MaterialApp(
            title: 'Time Tracker',
            debugShowCheckedModeBanner: false,
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text('Error initializing app: ${snapshot.error}'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // Restart the app
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(builder: (_) => const MyApp()),
                        );
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        
        // Services are initialized, now create the ThemeProvider and main app
        return ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MainAppContent(),
        );
      },
    );
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize database service first to ensure all Hive boxes are opened
      final databaseService = DatabaseService();
      await databaseService.initialize();

      // Initialize custom date format cache for better chart performance
      await LocaleDateUtils.updateCustomDateFormatCache();

      // Initialize location service (replaces foreground task handler)
      await LocationService.initializeService();

      // Run debug location tests
      if (kDebugMode) {
        DebugLocationTest.runTests();
      }

      // Migrate existing time entries to use the new inProgress flag
      await databaseService.migrateTimeEntries();
      // Check for active tracking areas and restore tracking if needed
      await _checkForActiveTracking();
    } catch (e) {
      debugPrint('Error during service initialization: $e');
      rethrow; // Re-throw to show error in UI
    }
  }
}

class MainAppContent extends StatelessWidget {
  const MainAppContent({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return WithForegroundTask(
      child: MaterialApp(
        title: 'Time Tracker',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeProvider.themeMode,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''), // English
          Locale('es', ''), // Spanish
          Locale('fr', ''), // French
          Locale('de', ''), // German
          Locale('it', ''), // Italian
          Locale('pt', ''), // Portuguese
          Locale('ru', ''), // Russian
          Locale('ja', ''), // Japanese
          Locale('ko', ''), // Korean
          Locale('zh', ''), // Chinese
          Locale('ar', ''), // Arabic
          Locale('hi', ''), // Hindi
        ],
        home: const MainScreen(),
        routes: {
          '/home': (context) => const ProjectsScreen(),
        },
        onGenerateRoute: (settings) {
          // Handle dynamic routes or refresh requests
          if (settings.name == '/') {
            return MaterialPageRoute(
              builder: (context) => const MainScreen(),
            );
          }
          return null;
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:hive/hive.dart';
import 'package:time_tracker_flutter/theme/app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;

  void updateColorScheme() {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
      systemNavigationBarColor: isDarkMode ? AppTheme.darkBackground : AppTheme.lightBackground,
      systemNavigationBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
      systemNavigationBarDividerColor: Colors.transparent,
    ));

    notifyListeners();
  }

  ThemeProvider() {
    try {
      final themeMode = Hive.box('settings').get('themeMode');
      _themeMode = themeMode != null ? ThemeMode.values.byName(themeMode) : ThemeMode.system;
    } catch (e) {
      // If the settings box is not available yet, use system default
      debugPrint('Settings box not available yet, using system theme mode: $e');
      _themeMode = ThemeMode.system;
    }

    updateColorScheme();
  }

  ThemeMode get themeMode => _themeMode;

  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      final brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }

  void toggleTheme() {
    _themeMode = isDarkMode ? ThemeMode.light : ThemeMode.dark;

    try {
      Hive.box('settings').put('themeMode', _themeMode.name);
    } catch (e) {
      debugPrint('Could not save theme mode to settings: $e');
    }

    updateColorScheme();
  }
}

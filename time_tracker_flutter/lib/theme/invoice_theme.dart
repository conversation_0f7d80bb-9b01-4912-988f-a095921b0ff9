import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/theme/app_theme.dart';

class InvoiceTheme {
  static const Color invoiceAccentColor = Color(0xFF4CAF50);
  static const Color invoiceDraftColor = Color(0xFFFF9800);
  static const Color invoiceSentColor = Color(0xFF2196F3);
  static const Color invoicePaidColor = Color(0xFF4CAF50);
  static const Color invoiceOverdueColor = Color(0xFFF44336);
  static const Color invoiceCancelledColor = Color(0xFF9E9E9E);

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return invoiceDraftColor;
      case 'sent':
        return invoiceSentColor;
      case 'paid':
        return invoicePaidColor;
      case 'overdue':
        return invoiceOverdueColor;
      case 'cancelled':
        return invoiceCancelledColor;
      default:
        return invoiceDraftColor;
    }
  }

  static Widget statusChip(String status, {bool isSmall = false}) {
    final color = getStatusColor(status);
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 6 : 8,
        vertical: isSmall ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: color,
          fontSize: isSmall ? 10 : 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  static ThemeData getInvoiceTheme(BuildContext context) {
    final baseTheme = Theme.of(context);
    final isDark = baseTheme.brightness == Brightness.dark;

    return baseTheme.copyWith(
      cardTheme: CardThemeData(
        color: isDark ? AppTheme.darkSurfaceColor : AppTheme.lightSurfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      dividerTheme: DividerThemeData(
        color: isDark ? Colors.grey.shade700 : Colors.grey.shade300,
        thickness: 1,
      ),
    );
  }

  static TextStyle get invoiceNumberStyle => const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static TextStyle get invoiceAmountStyle => const TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );

  static TextStyle get invoiceClientStyle => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static TextStyle get invoiceDateStyle => const TextStyle(
    fontSize: 14,
    color: Colors.grey,
  );

  static TextStyle get sectionHeaderStyle => const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppTheme.primaryColor,
  );

  static BoxDecoration get invoiceCardDecoration => BoxDecoration(
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );
}
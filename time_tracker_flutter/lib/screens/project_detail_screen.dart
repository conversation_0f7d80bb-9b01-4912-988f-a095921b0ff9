import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/edit_project_screen.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/time_utils.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/time_entry_item.dart';
import 'package:time_tracker_flutter/widgets/week_entry_group.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/widgets/location_area_selector_dialog.dart';

class ProjectDetailScreen extends StatefulWidget {
  final Project project;

  const ProjectDetailScreen({
    super.key,
    required this.project,
  });

  @override
  State<ProjectDetailScreen> createState() => _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends State<ProjectDetailScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<TimeEntry> _timeEntries = [];
  bool _isLoading = true;
  String? _totalTime;
  Map<String, Map<String, dynamic>> _weeklyEntries = {};
  Map<String, bool> _expandedWeeks = {};
  bool _isProjectCollapsed = false;
  Map<String, dynamic>? _progressData;

  @override
  void initState() {
    super.initState();
    _loadTimeEntries();
  }

  Future<void> _loadTimeEntries() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final entries = await _databaseService.getTimeEntries(projectId: widget.project.id);

      // Sort entries by date (newest first)
      entries.sort((a, b) => DateTime.parse(b.date).compareTo(DateTime.parse(a.date)));

      // Calculate total time
      final totalTime = calculateTotalTime(entries);

      // Group entries by week
      final weeklyGroups = groupEntriesByWeek(entries);

      // Initialize expanded weeks (current week expanded by default)
      final currentWeekKey = getCurrentWeekKey();
      final expandedWeeks = <String, bool>{};

      for (final weekKey in weeklyGroups.keys) {
        expandedWeeks[weekKey] = weekKey == currentWeekKey;
      }

      // Calculate progress if minimum weekly hours is set
      Map<String, dynamic>? progressData;
      if (widget.project.minimumWeeklyHours != null && entries.isNotEmpty) {
        // Convert int to double for calculation
        final targetHoursPerWeek = widget.project.minimumWeeklyHours!.toDouble();
        progressData = _calculateProgress(entries, targetHoursPerWeek);
      }

      setState(() {
        _timeEntries = entries;
        _totalTime = totalTime;
        _weeklyEntries = weeklyGroups;
        _expandedWeeks = expandedWeeks;
        _progressData = progressData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        showErrorSnackBar('Error loading time entries: ${e.toString()}', context);
      }
    }
  }

  void _showAddTimeEntryDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTimeEntryDialog(
        projectId: widget.project.id,
        onTimeEntryAdded: (TimeEntry entry) {
          _loadTimeEntries();
        },
      ),
    );
  }

  void _showEditTimeEntryDialog(TimeEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AddTimeEntryDialog(
        projectId: widget.project.id,
        initialEntry: entry,
        onTimeEntryAdded: (TimeEntry entry) {
          _loadTimeEntries();
        },
      ),
    );
  }

  Future<void> _deleteTimeEntry(TimeEntry entry) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Delete Time Entry',
        content: 'Are you sure you want to delete this time entry?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      await _databaseService.deleteTimeEntry(entry.id);
      _loadTimeEntries();
    }
  }

  Future<void> _deleteWeekEntries(List<TimeEntry> entries) async {
    try {
      // Delete all entries in the week
      for (final entry in entries) {
        await _databaseService.deleteTimeEntry(entry.id);
      }

      // Reload time entries
      _loadTimeEntries();

      if (mounted) {
        showSuccessSnackBar('All entries in this week have been deleted', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error deleting entries: ${e.toString()}', context);
      }
    }
  }

  Future<void> _deleteProject() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Delete Project',
        content: 'Are you sure you want to delete this project? All time entries will be deleted as well.',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      await _databaseService.deleteProject(widget.project.id);
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  void _navigateToEditProject() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProjectScreen(project: widget.project),
      ),
    ).then((_) {
      // Refresh project data
      _loadProjectDetails();
    });
  }

  Future<void> _loadProjectDetails() async {
    final project = await _databaseService.getProject(widget.project.id);
    if (project != null && mounted) {
      // Reload time entries to recalculate progress with updated project settings
      _loadTimeEntries();
    }
  }

  void _toggleWeek(String weekKey) {
    setState(() {
      _expandedWeeks[weekKey] = !(_expandedWeeks[weekKey] ?? false);
    });
  }

  void _toggleProjectCollapse() {
    setState(() {
      _isProjectCollapsed = !_isProjectCollapsed;
    });
  }

  Map<String, dynamic>? _calculateProgress(List<TimeEntry> entries, double targetHoursPerWeek) {
    if (entries.isEmpty) {
      return null;
    }

    // Get the earliest and latest dates
    final dates = entries.map((e) => DateTime.parse(e.date).millisecondsSinceEpoch).toList();
    final earliestDate = DateTime.fromMillisecondsSinceEpoch(dates.reduce((a, b) => a < b ? a : b));
    final latestDate = DateTime.fromMillisecondsSinceEpoch(dates.reduce((a, b) => a > b ? a : b));

    // Calculate weeks between dates
    final startWeek = getWeekStartDate(earliestDate);
    final endWeek = getWeekStartDate(latestDate);

    final diffDays = endWeek.difference(startWeek).inDays;
    // Match the original project's calculation: Math.max(1, Math.round(diffDays / 7) + 1)
    final weeksSinceStart = (diffDays / 7).round() + 1;
    final adjustedWeeksSinceStart = weeksSinceStart < 1 ? 1 : weeksSinceStart;

    // Calculate target hours
    final targetHours = adjustedWeeksSinceStart * targetHoursPerWeek;

    // Calculate actual hours
    final timeMinutes = _totalTime != null ? timeToMinutes(_totalTime!) : 0;
    final actualHours = timeMinutes / 60;

    // Calculate percentage - match original project's calculation
    final percentage = (actualHours / targetHours * 100).round();
    final clampedPercentage = percentage > 100 ? 100 : percentage;

    return {
      'actual': actualHours.toStringAsFixed(1),
      'target': targetHours.toStringAsFixed(1),
      'percentage': clampedPercentage,
    };
  }

  // Helper method to get progress color based on percentage
  Color getProgressColor(int percentage) {
    if (percentage < 25) {
      return Colors.red;
    } else if (percentage < 75) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  void _navigateToLocationTracking() {
    LocationAreaSelectorDialog.show(context, widget.project);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.project.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.location_on),
            tooltip: 'Location Tracking',
            onPressed: _navigateToLocationTracking,
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _navigateToEditProject,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteProject,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Project header with total time
                Card(
                  margin: const EdgeInsets.all(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                widget.project.name,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (_totalTime != null)
                              Text(
                                _totalTime!,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            IconButton(
                              icon: Icon(_isProjectCollapsed
                                  ? Icons.expand_more
                                  : Icons.expand_less),
                              onPressed: _toggleProjectCollapse,
                            ),
                          ],
                        ),
                        if (widget.project.minimumWeeklyHours != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Target: ${widget.project.minimumWeeklyHours} hours/week',
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                                if (_progressData != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    '${_progressData!['actual']} / ${_progressData!['target']} hours total',
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.secondary,
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  LinearProgressIndicator(
                                    value: _progressData!['percentage'] / 100,
                                    backgroundColor: Colors.grey.shade200,
                                    color: getProgressColor(_progressData!['percentage'].round()),
                                  ),
                                ],
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                // Time entries grouped by week
                if (!_isProjectCollapsed)
                  Expanded(
                    child: _timeEntries.isEmpty
                        ? const Center(
                            child: Text('No time entries yet'),
                          )
                        : ListView.builder(
                            itemCount: _weeklyEntries.length,
                            itemBuilder: (context, index) {
                              final weekKey = _weeklyEntries.keys.elementAt(index);
                              final weekData = _weeklyEntries[weekKey]!;
                              final isExpanded = _expandedWeeks[weekKey] ?? false;

                              return WeekEntryGroup(
                                weekKey: weekKey,
                                totalTime: weekData['totalTime'] as String,
                                entries: weekData['entries'] as List<TimeEntry>,
                                isExpanded: isExpanded,
                                onToggle: () => _toggleWeek(weekKey),
                                onEdit: _showEditTimeEntryDialog,
                                onDelete: _deleteTimeEntry,
                                onDeleteWeek: _deleteWeekEntries,
                                isCurrentWeek: isCurrentWeek(weekKey),
                                minimumWeeklyHours: widget.project.minimumWeeklyHours,
                              );
                            },
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                          ),
                  ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTimeEntryDialog,
        tooltip: 'Add Time Entry',
        child: const Icon(Icons.add),
      ),
    );
  }
}

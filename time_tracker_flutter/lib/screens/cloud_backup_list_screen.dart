import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/backups_tab.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/recovery_tab.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/transfer_tab.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/qr_scanner_dialog.dart';
import 'package:time_tracker_flutter/widgets/recovery_code_dialog.dart';
import 'package:time_tracker_flutter/widgets/transfer_qr_dialog.dart';
import 'package:time_tracker_flutter/screens/main_screen.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
class CloudBackupListScreen extends StatefulWidget {
  const CloudBackupListScreen({super.key});

  @override
  State<CloudBackupListScreen> createState() => _CloudBackupListScreenState();
}

class _CloudBackupListScreenState extends State<CloudBackupListScreen> with SingleTickerProviderStateMixin {
  final DatabaseService _databaseService = DatabaseService();
  List<String> _backups = [];
  bool _isLoading = true;
  bool _isRestoring = false;
  bool _isDeleting = false;

  String? _restoringBackup;
  String? _deletingBackup;

  late TabController _tabController;
  bool _isRecovering = false;
  String? _recoveryError;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeScreen();
  }

  Future<void> _initializeScreen() async {
    // First check if recovery code has been generated
    await _checkRecoveryCode();

    // Then load backups
    await _loadBackups();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkRecoveryCode() async {
    final hasGenerated = await _databaseService.hasGeneratedRecoveryCode();
    if (!hasGenerated) {
      // Show recovery code dialog after a short delay
      Future.delayed(const Duration(milliseconds: 300), () async {
        if (mounted) {
          String? code;
          try {
            code = await _databaseService.generateRecoveryCode();
          } catch (e) {
            if (mounted) {
              showErrorSnackBar('Error generating recovery code: ${e.toString()}', context);
              return;
            }
          }

          if (mounted) {
            await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => RecoveryCodeDialog(
                recoveryCode: code,
                onCodeSaved: () async {
                  // Mark the recovery code as saved
                  await _databaseService.markRecoveryCodeAsSaved();
                },
              ),
            );
          }
        }
      });
    }
  }

  Future<void> _loadBackups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final hasGenerated = await _databaseService.hasGeneratedRecoveryCode();

      if (!hasGenerated) {
        setState(() {
          _backups = [];
          _isLoading = false;
        });

        return;
      }

      final backups = await _databaseService.listCloudBackups();
      setState(() {
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        showErrorSnackBar('Error loading cloud backups: ${e.toString()}', context);
      }
    }
  }

  Future<void> _restoreBackup(String filename) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Restore Backup',
        content: 'This will replace all existing data. Are you sure you want to continue?',
        confirmText: 'Restore',
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isRestoring = true;
      _restoringBackup = filename;
    });

    try {
      debugPrint('Starting backup restoration for file: $filename');
      await _databaseService.restoreFromCloud(filename);
      debugPrint('Backup restoration completed successfully');

      if (mounted) {
        // Show success message
        showSuccessSnackBar('Backup restored successfully', context);

        // Simply pop back to the previous screen
        // The data change notification system will handle updating the UI
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MainScreen(),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error restoring backup: $e');
      if (mounted) {
        showErrorSnackBar('Error restoring backup: ${e.toString()}', context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRestoring = false;
          _restoringBackup = null;
        });
      }
    }
  }

  Future<void> _deleteBackup(String filename) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Delete Backup',
        content: 'Are you sure you want to delete this backup?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed != true) {
      return;
    }

    try {
      setState(() {
        _isDeleting = true;
        _deletingBackup = filename;
      });

      await _databaseService.deleteCloudBackup(filename);
      _loadBackups();
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error deleting backup: ${e.toString()}', context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
          _deletingBackup = null;
        });
      }
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.backupToCloud();
      await _loadBackups();

      if (mounted) {
        showSuccessSnackBar('Cloud backup created successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error creating cloud backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showTransferDialog() async {
    await showDialog(
      context: context,
      builder: (context) => const TransferQRDialog(),
    );
  }

  Future<void> _showRecoveryDialog() async {
    String? code;
    try {
      code = await _databaseService.generateRecoveryCode();
    } catch (e) {
      code = _databaseService.getRecoveryCode();
    }

    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) => RecoveryCodeDialog(
          recoveryCode: code,
          onCodeSaved: () async {
            // Mark the recovery code as saved
            await _databaseService.markRecoveryCodeAsSaved();
          },
        ),
      );
    }
  }

  Future<void> _handleRecoveryAttempt(String recoveryKey) async {
    if (recoveryKey.trim().isEmpty) {
      setState(() {
        _recoveryError = 'Please enter a recovery key';
      });
      return;
    }

    setState(() {
      _isRecovering = true;
      _recoveryError = null;
    });

    try {
      final success = await _databaseService.restoreFromRecoveryKey(recoveryKey);

      if (success) {
        // Refresh the backups list
        await _loadBackups();

        if (mounted) {
          setState(() {
            _recoveryError = null; // Clear any error
          });

          showSuccessSnackBar('Recovery successful! Your backups have been restored.', context);

          // Switch to the backups tab
          _tabController.animateTo(0);
        }
      } else {
        if (mounted) {
          setState(() {
            _recoveryError = 'Invalid recovery key. Please check and try again.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _recoveryError = 'Error during recovery: ${e.toString()}';
        });
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  Future<void> _showQRScannerDialog() async {
    if (mounted) {
      await showDialog(
        context: context,
        builder: (context) => QRScannerDialog(
          onScanSuccess: _handleQRScanSuccess,
        ),
      );
    }
  }

  Future<void> _handleQRScanSuccess(String data) async {
    setState(() {
      _isRecovering = true;
      _recoveryError = null;
    });

    try {
      // Parse the QR code data
      final Map<String, dynamic> transferData = jsonDecode(data);

      if (transferData.containsKey('recoveryCode')) {
        final success = await _databaseService.applyTransferData(data);

        if (success) {
          // Refresh the backups list
          await _loadBackups();

          if (mounted) {
            showSuccessSnackBar('Transfer successful! Your backups have been restored.', context);

            // Switch to the backups tab
            _tabController.animateTo(0);
          }
        } else {
          if (mounted) {
            setState(() {
              _recoveryError = 'Invalid transfer data. Please try again.';
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            _recoveryError = 'Invalid QR code. Please try again.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _recoveryError = 'Error processing QR code: ${e.toString()}';
        });
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cloud Backups'),
        scrolledUnderElevation: 4.0,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          dividerColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.label,
          labelStyle: const TextStyle(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'Backups'),
            Tab(text: 'Transfer'),
            Tab(text: 'Recovery'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          BackupsTab(
            databaseService: _databaseService,
            backups: _backups,
            isLoading: _isLoading,
            isRestoring: _isRestoring,
            isDeleting: _isDeleting,
            restoringBackup: _restoringBackup,
            deletingBackup: _deletingBackup,
            onCreateBackup: _createBackup,
            onRestoreBackup: _restoreBackup,
            onDeleteBackup: _deleteBackup,
          ),
          TransferTab(
            isRecovering: _isRecovering,
            recoveryError: _recoveryError,
            onShowTransferDialog: _showTransferDialog,
            onShowQRScannerDialog: _showQRScannerDialog,
          ),
          RecoveryTab(
            isRecovering: _isRecovering,
            recoveryError: _recoveryError,
            onShowRecoveryDialog: _showRecoveryDialog,
            onShowQRScannerDialog: _showQRScannerDialog,
            onRecoveryAttempt: _handleRecoveryAttempt,
          ),
        ],
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton(
              onPressed: _createBackup,
              tooltip: 'Create Backup',
              child: const Icon(Icons.cloud_upload),
            )
          : null,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/screens/project_detail_screen.dart';
import 'package:time_tracker_flutter/screens/settings_screen.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/theme/theme_provider.dart';
import 'package:time_tracker_flutter/widgets/add_project_dialog.dart';
import 'package:time_tracker_flutter/widgets/project_card.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<Project> _projects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final projects = await _databaseService.getProjects();

      // Sort projects by order
      projects.sort((a, b) => a.order.compareTo(b.order));

      setState(() {
        _projects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        showErrorSnackBar('Error loading projects: ${e.toString()}', context);
      }
    }
  }

  void _showAddProjectDialog() {
    showDialog(
      context: context,
      builder: (context) => AddProjectDialog(
        onProjectAdded: (Project project) {
          setState(() {
            _projects.add(project);
          });
        },
      ),
    );
  }

  void _navigateToProjectDetail(Project project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProjectDetailScreen(project: project),
      ),
    ).then((_) => _loadProjects());
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    ).then((_) => _loadProjects());
  }

  Future<void> _reorderProjects(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final Project item = _projects.removeAt(oldIndex);
    _projects.insert(newIndex, item);

    // Update order for all projects
    for (int i = 0; i < _projects.length; i++) {
      final updatedProject = _projects[i].copyWith(order: i);
      await _databaseService.saveProject(updatedProject);
      _projects[i] = updatedProject;
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Tracker'),
        actions: [
          IconButton(
            icon: Icon(
              themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            ),
            onPressed: () {
              themeProvider.toggleTheme();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _navigateToSettings,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _projects.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'No projects yet',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _showAddProjectDialog,
                        child: const Text('Add Project'),
                      ),
                    ],
                  ),
                )
              : ReorderableListView.builder(
                  itemCount: _projects.length,
                  onReorder: _reorderProjects,
                  itemBuilder: (context, index) {
                    final project = _projects[index];
                    return ProjectCard(
                      key: ValueKey(project.id),
                      project: project,
                      onTap: (_) => _navigateToProjectDetail(project),
                      projectIndex: index,
                      onReorderStart: (_) {},
                      onEdit: (_) {},
                      onDelete: (_) {},
                      onAddTimeEntry: (_) {},
                    );
                  },
                  padding: const EdgeInsets.all(16),
                ),
      floatingActionButton: _projects.isNotEmpty
          ? FloatingActionButton(
              onPressed: _showAddProjectDialog,
              tooltip: 'Add Project',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/widgets/custom_snackbar.dart';
import 'package:time_tracker_flutter/theme/invoice_theme.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';

class InvoiceSettingsScreen extends StatefulWidget {
  const InvoiceSettingsScreen({super.key});

  @override
  State<InvoiceSettingsScreen> createState() => _InvoiceSettingsScreenState();
}

class _InvoiceSettingsScreenState extends State<InvoiceSettingsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _currencyController = TextEditingController();
  final _localeController = TextEditingController();
  final _prefixController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _dueDaysController = TextEditingController();
  final _notesController = TextEditingController();
  final _sequenceLengthController = TextEditingController();

  // Business info controllers
  final _businessNameController = TextEditingController();
  final _businessEmailController = TextEditingController();
  final _businessPhoneController = TextEditingController();
  final _businessWebsiteController = TextEditingController();
  final _businessTaxIdController = TextEditingController();

  // Address controllers
  final _streetController = TextEditingController();
  final _street2Controller = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _countryController = TextEditingController();

  InvoiceSettings? _currentSettings;
  InvoiceTemplate _selectedTemplate = InvoiceTemplate.professional;
  String _selectedDateFormat = 'yyyyMM';
  bool _isLoading = false;
  bool _isSaving = false;

  final List<String> _currencies = [
    'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'CNY', 'INR', 'BRL'
  ];

  final List<String> _locales = [
    'en_US', 'en_GB', 'es_ES', 'fr_FR', 'de_DE', 'it_IT', 'pt_BR', 'ja_JP', 'zh_CN', 'hi_IN'
  ];

  // Invoice language settings
  String _selectedInvoiceLanguage = 'en_US';

  final List<String> _dateFormats = [
    'yyyy', 'yyyyMM', 'yyyyMMdd'
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _currencyController.dispose();
    _localeController.dispose();
    _prefixController.dispose();
    _taxRateController.dispose();
    _dueDaysController.dispose();
    _notesController.dispose();
    _sequenceLengthController.dispose();
    _businessNameController.dispose();
    _businessEmailController.dispose();
    _businessPhoneController.dispose();
    _businessWebsiteController.dispose();
    _businessTaxIdController.dispose();
    _streetController.dispose();
    _street2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = await _databaseService.getInvoiceSettings();
      setState(() {
        _currentSettings = settings;
        _populateControllers(settings);
      });
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error loading settings: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _populateControllers(InvoiceSettings settings) {
    _currencyController.text = settings.defaultCurrency;
    _localeController.text = settings.defaultLocale;
    _prefixController.text = settings.invoiceNumberPrefix;
    _taxRateController.text = settings.defaultTaxRate.toString();
    _dueDaysController.text = settings.defaultDueDays.toString();
    _notesController.text = settings.defaultNotes ?? '';
    _sequenceLengthController.text = settings.invoiceNumberSequenceLength.toString();
    _selectedTemplate = settings.defaultTemplate;
    _selectedDateFormat = settings.invoiceNumberDateFormat;
    _selectedInvoiceLanguage = settings.defaultInvoiceLanguage ?? 'en_US';

    // Business info
    if (settings.businessInfo != null) {
      final business = settings.businessInfo!;
      _businessNameController.text = business.name;
      _businessEmailController.text = business.email ?? '';
      _businessPhoneController.text = business.phone ?? '';
      _businessWebsiteController.text = business.website ?? '';
      _businessTaxIdController.text = business.taxId ?? '';

      // Address
      if (business.address != null) {
        final address = business.address!;
        _streetController.text = address.street;
        _street2Controller.text = address.street2 ?? '';
        _cityController.text = address.city;
        _stateController.text = address.state ?? '';
        _postalCodeController.text = address.postalCode;
        _countryController.text = address.country;
      }
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Create business info if any business field is filled
      BusinessInfo? businessInfo;
      if (_businessNameController.text.trim().isNotEmpty) {
        Address? address;
        if (_streetController.text.trim().isNotEmpty &&
            _cityController.text.trim().isNotEmpty &&
            _postalCodeController.text.trim().isNotEmpty &&
            _countryController.text.trim().isNotEmpty) {
          address = Address(
            street: _streetController.text.trim(),
            street2: _street2Controller.text.trim().isEmpty ? null : _street2Controller.text.trim(),
            city: _cityController.text.trim(),
            state: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
            postalCode: _postalCodeController.text.trim(),
            country: _countryController.text.trim(),
          );
        }

        businessInfo = BusinessInfo(
          name: _businessNameController.text.trim(),
          email: _businessEmailController.text.trim().isEmpty ? null : _businessEmailController.text.trim(),
          phone: _businessPhoneController.text.trim().isEmpty ? null : _businessPhoneController.text.trim(),
          website: _businessWebsiteController.text.trim().isEmpty ? null : _businessWebsiteController.text.trim(),
          taxId: _businessTaxIdController.text.trim().isEmpty ? null : _businessTaxIdController.text.trim(),
          address: address,
        );
      }

      final updatedSettings = InvoiceSettings(
        defaultCurrency: _currencyController.text.trim(),
        defaultLocale: _localeController.text.trim(),
        defaultTemplate: _selectedTemplate,
        businessInfo: businessInfo,
        invoiceNumberPrefix: _prefixController.text.trim(),
        invoiceNumberDateFormat: _selectedDateFormat,
        invoiceNumberSequenceLength: int.parse(_sequenceLengthController.text),
        defaultTaxRate: double.parse(_taxRateController.text),
        defaultDueDays: int.parse(_dueDaysController.text),
        defaultNotes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        createdAt: _currentSettings?.createdAt,
        defaultInvoiceLanguage: _selectedInvoiceLanguage,
      );

      await _databaseService.saveInvoiceSettings(updatedSettings);

      if (mounted) {
        showSuccessSnackBar('Invoice settings saved successfully', context);
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error saving settings: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  String _getInvoiceNumberPreview() {
    try {
      final prefix = _prefixController.text.trim().isEmpty ? 'INV' : _prefixController.text.trim();
      final sequenceLength = int.tryParse(_sequenceLengthController.text) ?? 4;
      final now = DateTime.now();

      String dateStr;
      switch (_selectedDateFormat) {
        case 'yyyy':
          dateStr = now.year.toString();
          break;
        case 'yyyyMM':
          dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}';
          break;
        case 'yyyyMMdd':
          dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
          break;
        default:
          dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}';
      }

      final sequenceStr = '1'.padLeft(sequenceLength, '0');
      return '$prefix-$dateStr-$sequenceStr';
    } catch (e) {
      return 'INV-202501-0001';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Theme(
      data: InvoiceTheme.getInvoiceTheme(context),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Invoice Settings'),
          actions: [
            if (_isSaving)
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                child: const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16),
                child: _buildModernSaveButton(isSmallScreen),
              ),
          ],
        ),
        body: ConstrainedWidthContainer(
          maxWidth: 800,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Form(
                  key: _formKey,
                  child: ListView(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                    children: [
                      _buildModernGeneralSettingsSection(isSmallScreen),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      _buildModernInvoiceLanguageSection(isSmallScreen),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      _buildModernInvoiceNumberSection(isSmallScreen),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      _buildModernBusinessInfoSection(isSmallScreen),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      _buildModernAddressSection(isSmallScreen),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      _buildModernDefaultsSection(isSmallScreen),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildModernSaveButton(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _saveSettings,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 12 : 16,
              vertical: isSmallScreen ? 10 : 8
            ),
            child: const Text(
              'Save',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernGeneralSettingsSection(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'General Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernDropdownField(
                          'Default Currency',
                          _currencyController.text.isEmpty ? null : _currencyController.text,
                          _currencies,
                          onChanged: (value) {
                            if (value != null) {
                              _currencyController.text = value;
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a currency';
                            }
                            return null;
                          },
                          icon: Icons.attach_money_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildModernDropdownField(
                          'Default Locale',
                          _localeController.text.isEmpty ? null : _localeController.text,
                          _locales,
                          onChanged: (value) {
                            if (value != null) {
                              _localeController.text = value;
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a locale';
                            }
                            return null;
                          },
                          icon: Icons.language_rounded,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernDropdownField(
                            'Default Currency',
                            _currencyController.text.isEmpty ? null : _currencyController.text,
                            _currencies,
                            onChanged: (value) {
                              if (value != null) {
                                _currencyController.text = value;
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a currency';
                              }
                              return null;
                            },
                            icon: Icons.attach_money_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernDropdownField(
                            'Default Locale',
                            _localeController.text.isEmpty ? null : _localeController.text,
                            _locales,
                            onChanged: (value) {
                              if (value != null) {
                                _localeController.text = value;
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a locale';
                              }
                              return null;
                            },
                            icon: Icons.language_rounded,
                          ),
                        ),
                      ],
                    ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                _buildModernDropdownField(
                  'Default Template',
                  _selectedTemplate,
                  InvoiceTemplate.values,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedTemplate = value;
                      });
                    }
                  },
                  displayBuilder: (template) => Text(_getTemplateDisplayName(template)),
                  icon: Icons.description_rounded,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernInvoiceNumberSection(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.numbers_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Invoice Numbering',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'Prefix',
                          _prefixController,
                          hintText: 'INV',
                          icon: Icons.text_fields_rounded,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a prefix';
                            }
                            return null;
                          },
                          onChanged: (_) => setState(() {}),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: _buildModernDropdownField(
                                'Date Format',
                                _selectedDateFormat,
                                _dateFormats,
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedDateFormat = value;
                                    });
                                  }
                                },
                                icon: Icons.date_range_rounded,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              flex: 1,
                              child: _buildModernTextField(
                                'Length',
                                _sequenceLengthController,
                                keyboardType: TextInputType.number,
                                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                                icon: Icons.format_size_rounded,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Required';
                                  }
                                  final num = int.tryParse(value);
                                  if (num == null || num < 1 || num > 10) {
                                    return '1-10';
                                  }
                                  return null;
                                },
                                onChanged: (_) => setState(() {}),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: _buildModernTextField(
                            'Prefix',
                            _prefixController,
                            hintText: 'INV',
                            icon: Icons.text_fields_rounded,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a prefix';
                              }
                              return null;
                            },
                            onChanged: (_) => setState(() {}),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: _buildModernDropdownField(
                            'Date Format',
                            _selectedDateFormat,
                            _dateFormats,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedDateFormat = value;
                                });
                              }
                            },
                            icon: Icons.date_range_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 1,
                          child: _buildModernTextField(
                            'Length',
                            _sequenceLengthController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            icon: Icons.format_size_rounded,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Required';
                              }
                              final num = int.tryParse(value);
                              if (num == null || num < 1 || num > 10) {
                                return '1-10';
                              }
                              return null;
                            },
                            onChanged: (_) => setState(() {}),
                          ),
                        ),
                      ],
                    ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.preview_rounded,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        size: isSmallScreen ? 18 : 20,
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Preview',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                                fontWeight: FontWeight.w500,
                                fontSize: isSmallScreen ? 11 : null,
                              ),
                            ),
                            Text(
                              _getInvoiceNumberPreview(),
                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onPrimaryContainer,
                                fontSize: isSmallScreen ? 14 : null,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernBusinessInfoSection(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.business_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Business Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                _buildModernTextField(
                  'Business Name',
                  _businessNameController,
                  icon: Icons.store_rounded,
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'Email',
                          _businessEmailController,
                          keyboardType: TextInputType.emailAddress,
                          icon: Icons.email_rounded,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value)) {
                                return 'Please enter a valid email';
                              }
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildModernTextField(
                          'Phone',
                          _businessPhoneController,
                          keyboardType: TextInputType.phone,
                          icon: Icons.phone_rounded,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            'Email',
                            _businessEmailController,
                            keyboardType: TextInputType.emailAddress,
                            icon: Icons.email_rounded,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value)) {
                                  return 'Please enter a valid email';
                                }
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            'Phone',
                            _businessPhoneController,
                            keyboardType: TextInputType.phone,
                            icon: Icons.phone_rounded,
                          ),
                        ),
                      ],
                    ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'Website',
                          _businessWebsiteController,
                          keyboardType: TextInputType.url,
                          icon: Icons.web_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildModernTextField(
                          'Tax ID',
                          _businessTaxIdController,
                          icon: Icons.receipt_long_rounded,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            'Website',
                            _businessWebsiteController,
                            keyboardType: TextInputType.url,
                            icon: Icons.web_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            'Tax ID',
                            _businessTaxIdController,
                            icon: Icons.receipt_long_rounded,
                          ),
                        ),
                      ],
                    ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernAddressSection(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Business Address',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                _buildModernTextField(
                  'Street Address',
                  _streetController,
                  icon: Icons.home_rounded,
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                _buildModernTextField(
                  'Street Address 2 (Optional)',
                  _street2Controller,
                  icon: Icons.home_work_rounded,
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'City',
                          _cityController,
                          icon: Icons.location_city_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildModernTextField(
                          'State/Province (Optional)',
                          _stateController,
                          icon: Icons.map_rounded,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            'City',
                            _cityController,
                            icon: Icons.location_city_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            'State/Province (Optional)',
                            _stateController,
                            icon: Icons.map_rounded,
                          ),
                        ),
                      ],
                    ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'Postal Code',
                          _postalCodeController,
                          icon: Icons.markunread_mailbox_rounded,
                        ),
                        const SizedBox(height: 12),
                        _buildModernTextField(
                          'Country',
                          _countryController,
                          icon: Icons.public_rounded,
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            'Postal Code',
                            _postalCodeController,
                            icon: Icons.markunread_mailbox_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            'Country',
                            _countryController,
                            icon: Icons.public_rounded,
                          ),
                        ),
                      ],
                    ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDefaultsSection(bool isSmallScreen) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Default Values',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                isSmallScreen
                  ? Column(
                      children: [
                        _buildModernTextField(
                          'Default Tax Rate (%)',
                          _taxRateController,
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                          ],
                          icon: Icons.percent_rounded,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a tax rate';
                            }
                            final rate = double.tryParse(value);
                            if (rate == null || rate < 0 || rate > 100) {
                              return 'Please enter a valid rate (0-100)';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildModernTextField(
                          'Default Due Days',
                          _dueDaysController,
                          keyboardType: TextInputType.number,
                          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                          icon: Icons.schedule_rounded,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter due days';
                            }
                            final days = int.tryParse(value);
                            if (days == null || days < 0) {
                              return 'Please enter a valid number';
                            }
                            return null;
                          },
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildModernTextField(
                            'Default Tax Rate (%)',
                            _taxRateController,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                            ],
                            icon: Icons.percent_rounded,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a tax rate';
                              }
                              final rate = double.tryParse(value);
                              if (rate == null || rate < 0 || rate > 100) {
                                return 'Please enter a valid rate (0-100)';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernTextField(
                            'Default Due Days',
                            _dueDaysController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            icon: Icons.schedule_rounded,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter due days';
                              }
                              final days = int.tryParse(value);
                              if (days == null || days < 0) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                _buildModernTextField(
                  'Default Notes (Optional)',
                  _notesController,
                  maxLines: 3,
                  icon: Icons.note_rounded,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernInvoiceLanguageSection(bool isSmallScreen) {
    final supportedLanguages = InvoiceLocalizationService.getSupportedLanguages();
    final languageDisplayNames = InvoiceLocalizationService.getLanguageDisplayNames();

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.translate_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Invoice Language',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),
            Text(
              'Select the language for invoice text and labels',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: isSmallScreen ? 13 : null,
              ),
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            Column(
              children: [
                _buildModernDropdownField(
                  'Invoice Language',
                  _selectedInvoiceLanguage,
                  supportedLanguages,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedInvoiceLanguage = value;
                      });
                    }
                  },
                  displayBuilder: (language) {
                    final displayName = languageDisplayNames[language] ?? language;
                    final hasComplete = InvoiceLocalizationService.hasCompleteTranslations(language);

                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(child: Text(displayName)),
                        if (hasComplete) ...[
                          const SizedBox(width: 8),
                          const Icon(Icons.check_circle, size: 16, color: Colors.green),
                        ] else ...[
                          const SizedBox(width: 8),
                          const Icon(Icons.warning, size: 16, color: Colors.orange),
                        ],
                      ],
                    );
                  },
                  icon: Icons.language_rounded,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an invoice language';
                    }
                    return null;
                  },
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.preview_rounded,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                            size: isSmallScreen ? 18 : 20,
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            'Preview',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                              fontWeight: FontWeight.w500,
                              fontSize: isSmallScreen ? 11 : null,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: isSmallScreen ? 6 : 8),
                      Text(
                        '${InvoiceLocalizationService.getTranslation('invoice', _selectedInvoiceLanguage)} - ${InvoiceLocalizationService.getTranslation('bill_to', _selectedInvoiceLanguage)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontSize: isSmallScreen ? 13 : null,
                        ),
                      ),
                      Text(
                        '${InvoiceLocalizationService.getTranslation('subtotal', _selectedInvoiceLanguage)}: ${InvoiceLocalizationService.formatCurrencyForLanguage(1234.56, 'USD', _selectedInvoiceLanguage)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontSize: isSmallScreen ? 13 : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernTextField(
    String label,
    TextEditingController controller, {
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    IconData? icon,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    String? hintText,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: icon != null ? Icon(icon, size: 18) : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
      ),
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      validator: validator,
      onChanged: onChanged,
    );
  }

  Widget _buildModernDropdownField<T>(
    String label,
    T? value,
    List<T> items, {
    required void Function(T?) onChanged,
    Widget Function(T)? displayBuilder,
    IconData? icon,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: icon != null ? Icon(icon, size: 18) : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
      ),
      items: items.map((item) {
        return DropdownMenuItem<T>(
          value: item,
          child: displayBuilder != null ? displayBuilder(item) : Text(item.toString()),
        );
      }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }

  String _getTemplateDisplayName(InvoiceTemplate template) {
    switch (template) {
      case InvoiceTemplate.professional:
        return 'Professional';
      case InvoiceTemplate.minimal:
        return 'Minimal';
      case InvoiceTemplate.detailed:
        return 'Detailed';
    }
  }
}
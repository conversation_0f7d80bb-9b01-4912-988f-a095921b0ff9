import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdfrx/pdfrx.dart';
import 'package:time_tracker_flutter/widgets/common_loading_indicator.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';

/// Screen for previewing PDF documents
class PDFPreviewScreen extends StatefulWidget {
  final String filePath;
  final String title;

  const PDFPreviewScreen({
    super.key,
    required this.filePath,
    this.title = 'PDF Preview',
  });

  @override
  State<PDFPreviewScreen> createState() => _PDFPreviewScreenState();
}

class _PDFPreviewScreenState extends State<PDFPreviewScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 0;

  @override
  void initState() {
    super.initState();
    _checkFileExists();
  }

  Future<void> _checkFileExists() async {
    try {
      final file = File(widget.filePath);
      if (!await file.exists()) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'PDF file not found';
        });
        return;
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Error loading PDF: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          if (!_isLoading && !_hasError) ...[
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _sharePDF,
              tooltip: 'Share PDF',
            ),
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: _downloadPDF,
              tooltip: 'Download PDF',
            ),
          ],
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CommonLoadingIndicator.large(message: 'Loading PDF...'),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'Error loading PDF',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                  _errorMessage = null;
                });
                _checkFileExists();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (_totalPages > 1) _buildPageIndicator(),
        Expanded(
          child: PdfViewer.file(
            widget.filePath,
          ),
        ),
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: _currentPage > 1 ? _previousPage : null,
            tooltip: 'Previous page',
          ),
          Text(
            'Page $_currentPage of $_totalPages',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: _currentPage < _totalPages ? _nextPage : null,
            tooltip: 'Next page',
          ),
        ],
      ),
    );
  }

  void _previousPage() {
    // Navigation will be handled by the PdfViewer widget
    setState(() {
      if (_currentPage > 1) {
        _currentPage--;
      }
    });
  }

  void _nextPage() {
    // Navigation will be handled by the PdfViewer widget
    setState(() {
      if (_currentPage < _totalPages) {
        _currentPage++;
      }
    });
  }

  Future<void> _sharePDF() async {
    try {
      // This would typically use the share_plus package
      // For now, we'll show a snackbar
      if (mounted) {
        showSuccessSnackBar('PDF sharing functionality would be implemented here', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error sharing PDF: ${e.toString()}', context);
      }
    }
  }

  Future<void> _downloadPDF() async {
    try {
      // This would typically save the PDF to downloads folder
      // For now, we'll show a snackbar
      if (mounted) {
        showSuccessSnackBar('PDF download functionality would be implemented here', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error downloading PDF: ${e.toString()}', context);
      }
    }
  }
}
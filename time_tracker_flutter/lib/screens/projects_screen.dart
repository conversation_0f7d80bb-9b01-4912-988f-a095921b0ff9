import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:provider/provider.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/settings_screen.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/theme/theme_provider.dart';
import 'package:time_tracker_flutter/widgets/add_time_entry_dialog.dart';
import 'package:time_tracker_flutter/widgets/enhanced_project_card/card.dart';
import 'package:time_tracker_flutter/widgets/project_dialog.dart';
import 'package:time_tracker_flutter/widgets/project_card_notifiers.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
class ProjectsScreen extends StatefulWidget {
  final bool isInTabView;
  final Widget? backupPromptWidget;

  const ProjectsScreen({
    super.key,
    this.isInTabView = false,
    this.backupPromptWidget,
  });

  @override
  State<ProjectsScreen> createState() => _ProjectsScreenState();
}

class _ProjectsScreenState extends State<ProjectsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<Project> _projects = [];
  bool _isLoading = true;

  // Map to store ValueNotifiers for each project card
  final Map<String, ProjectCardNotifiers> _projectCardNotifiers = {};

  // Map to store collapsed states before dragging
  final Map<String, bool> _preDropCollapsedStates = {};

  @override
  void initState() {
    super.initState();
    // Register as a listener for database changes
    _databaseService.addDataChangeListener(_onDataChanged);
    _loadProjects();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This will ensure the screen refreshes when navigated to
    _loadProjects();
  }

  // Callback for when data changes in the database
  void _onDataChanged() {
    debugPrint('Data change notification received in ProjectsScreen');
    if (mounted) {
      _loadProjects();
    }
  }

  @override
  void dispose() {
    // Dispose all ValueNotifiers
    for (final notifiers in _projectCardNotifiers.values) {
      notifiers.dispose();
    }
    _projectCardNotifiers.clear();

    // Remove data change listener
    _databaseService.removeDataChangeListener(_onDataChanged);

    super.dispose();
  }

  // Flag to prevent multiple simultaneous loads
  bool _isLoadingProjects = false;

  Future<void> _loadProjects() async {
    // Prevent multiple simultaneous loads
    if (_isLoadingProjects) return;
    _isLoadingProjects = true;

    // Clear existing project card notifiers
    for (final notifiers in _projectCardNotifiers.values) {
      notifiers.dispose();
    }
    _projectCardNotifiers.clear();

    setState(() {
      _isLoading = true;
      // Clear projects immediately to avoid showing stale data
      _projects = [];
    });

    try {
      debugPrint('Loading projects from database');
      final projects = await _databaseService.getProjects();
      debugPrint('Loaded ${projects.length} projects');

      // Sort projects by order
      projects.sort((a, b) => a.order.compareTo(b.order));

      if (mounted) {
        setState(() {
          _projects = projects;
          _isLoading = false;
        });
        debugPrint('Updated UI with ${projects.length} projects');
      }
    } catch (e) {
      debugPrint('Error loading projects: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        showErrorSnackBar('Error loading projects: ${e.toString()}', context);
      }
    } finally {
      _isLoadingProjects = false;
    }
  }

  void _showAddProjectDialog() {
    showDialog(
      context: context,
      builder: (context) => ProjectDialog(
        onSave: _saveProject,
      ),
    );
  }

  void _showEditProjectDialog(Project project) {
    showDialog(
      context: context,
      builder: (context) => ProjectDialog(
        initialProject: project,
        onSave: _saveProject,
      ),
    );
  }

  Future<void> _saveProject(Project project) async {
    try {
      await _databaseService.saveProject(project);
      _loadProjects();
      showSuccessSnackBar('Project saved successfully', context);
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error saving project: ${e.toString()}', context);
      }
    }
  }

  void _showAddTimeEntryDialog(Project project) {
    showDialog(
      context: context,
      builder: (context) => AddTimeEntryDialog(
        projectId: project.id,
        onTimeEntryAdded: (TimeEntry entry) {
          _loadProjects();
        },
      ),
    );
  }

  Future<void> _deleteProject(Project project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Project',
        content: 'Are you sure you want to delete "${project.name}"? All time entries will be deleted as well.',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      try {
        // Clean up the notifiers for this project
        final notifiers = _projectCardNotifiers[project.id];
        if (notifiers != null) {
          notifiers.dispose();
          _projectCardNotifiers.remove(project.id);
        }

        await _databaseService.deleteProject(project.id);
        _loadProjects();
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error deleting project: ${e.toString()}', context);
        }
      }
    }
  }

  void _reorderProjects(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // Create a new list to avoid modifying the original during drag
    final List<Project> newProjects = List.from(_projects);
    final Project item = newProjects.removeAt(oldIndex);
    newProjects.insert(newIndex, item);

    // Update the order of all projects
    for (int i = 0; i < newProjects.length; i++) {
      newProjects[i] = newProjects[i].copyWith(order: i);
    }

    // Update state only once with the new list
    setState(() {
      _projects = newProjects;
    });

    // Save the updated order to the database
    _saveProjectOrder();
  }

  void _onReorderStart(int index) {
    // Get the project at the index
    final project = _projects[index];

    // Store all current collapsed states
    _preDropCollapsedStates.clear(); // Clear previous states first
    for (final p in _projects) {
      final notifier = _projectCardNotifiers[p.id];
      if (notifier != null) {
        _preDropCollapsedStates[p.id] = notifier.isCollapsed.value;
        // No longer collapsing cards here
      }
    }
  }

  void _onReorderEnd(int index) {
    // Restore all collapsed states
    for (final p in _projects) {
      final notifier = _projectCardNotifiers[p.id];
      final previousState = _preDropCollapsedStates[p.id];
      if (notifier != null && previousState != null) {
        notifier.isCollapsed.value = previousState;
      }
    }
    _preDropCollapsedStates.clear();
  }

  Future<void> _saveProjectOrder() async {
    try {
      // Save each project with its new order
      for (final project in _projects) {
        await _databaseService.saveProject(project);
      }
      if (mounted) {
        showSuccessSnackBar('Project order updated successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error updating project order: ${e.toString()}', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Tracker'),
        actions: [
          if (!widget.isInTabView)
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
            ),
          IconButton(
            icon: Icon(
              themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            ),
            onPressed: () {
              themeProvider.toggleTheme();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _projects.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'No projects yet',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _showAddProjectDialog,
                        child: const Text('Add Project'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    if (widget.backupPromptWidget != null)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                        child: widget.backupPromptWidget!,
                      ),
                    Expanded(
                      child: ReorderableListView.builder(
                        itemCount: _projects.length,
                        itemBuilder: (context, index) {
                          final project = _projects[index];

                          // Create notifiers for this project if they don't exist
                          if (!_projectCardNotifiers.containsKey(project.id)) {
                            _projectCardNotifiers[project.id] = ProjectCardNotifiers(
                              isCollapsed: ValueNotifier<bool>(false),
                              showAllWeeks: ValueNotifier<bool>(false),
                            );
                          }

                          return KeyedSubtree(
                            key: ValueKey(project.id),
                            child: EnhancedProjectCard(
                              project: project,
                              onEdit: _showEditProjectDialog,
                              onDelete: _deleteProject,
                              onAddTimeEntry: _showAddTimeEntryDialog,
                              notifiers: _projectCardNotifiers[project.id]!,
                              projectIndex: index,
                              onReorderStart: _onReorderStart,
                            ),
                          );
                        },
                        onReorder: _reorderProjects,
                        onReorderStart: _onReorderStart,
                        onReorderEnd: _onReorderEnd,
                        buildDefaultDragHandles: false,
                        proxyDecorator: (Widget child, int index, Animation<double> animation) {
                          return AnimatedBuilder(
                            animation: animation,
                            builder: (BuildContext context, Widget? _) {
                              final double animValue = Curves.easeInOut.transform(animation.value);
                              final double elevation = lerpDouble(0, 6, animValue)!;
                              return Material(
                                elevation: elevation,
                                color: Colors.transparent,
                                shadowColor: Colors.black.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(12),
                                child: Opacity(
                                  opacity: lerpDouble(1.0, 0.95, animValue)!,
                                  child: child,
                                ),
                              );
                            },
                          );
                        },
                        padding: const EdgeInsets.only(top: 4),
                        physics: const ClampingScrollPhysics(),
                      ),
                    ),
                  ],
                ),
      floatingActionButton: _projects.isNotEmpty
          ? FloatingActionButton(
              onPressed: _showAddProjectDialog,
              tooltip: 'Add Project',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

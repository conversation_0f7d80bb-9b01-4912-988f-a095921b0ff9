import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:location/location.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/location_service.dart';

class MapSelectorScreen extends StatefulWidget {
  final String projectId;
  final LocationArea? existingArea;

  const MapSelectorScreen({
    Key? key,
    required this.projectId,
    this.existingArea,
  }) : super(key: key);

  @override
  _MapSelectorScreenState createState() => _MapSelectorScreenState();
}

class _MapSelectorScreenState extends State<MapSelectorScreen> {
  static const _defaultRadius = 100.0;
  static const _defaultCooldown = 5;
  static const _defaultZoom = 15.0;
  static const _markerSize = 40.0;

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _radiusController = TextEditingController(text: _defaultRadius.toString());
  final _cooldownController = TextEditingController(text: _defaultCooldown.toString());
  final _databaseService = DatabaseService();
  final _location = Location();

  late final MapController _mapController;
  LatLng _currentLocation = const LatLng(0, 0);
  double _radius = _defaultRadius;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
    _initializeScreen();
  }

  Future<void> _initializeScreen() async {
    if (widget.existingArea != null) {
      _initializeFromExistingArea();
    } else {
      await _getCurrentLocation();
    }
  }

  void _initializeFromExistingArea() {
    final area = widget.existingArea!;
    _nameController.text = area.name;
    _radiusController.text = area.radius.toString();
    _cooldownController.text = area.cooldownTime.toString();
    _currentLocation = LatLng(area.centerLatitude, area.centerLongitude);
    _radius = area.radius;
    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _radiusController.dispose();
    _cooldownController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final hasPermission = await _requestLocationPermission();
      if (!hasPermission) {
        setState(() => _isLoading = false);
        return;
      }

      final locationData = await _location.getLocation();
      if (locationData.latitude != null && locationData.longitude != null) {
        setState(() {
          _currentLocation = LatLng(locationData.latitude!, locationData.longitude!);
          _isLoading = false;
        });
        _mapController.move(_currentLocation, _defaultZoom);
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _requestLocationPermission() async {
    bool serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) return false;
    }

    PermissionStatus permissionStatus = await _location.hasPermission();
    if (permissionStatus == PermissionStatus.denied) {
      permissionStatus = await _location.requestPermission();
      if (permissionStatus != PermissionStatus.granted) return false;
    }

    return true;
  }

  void _onMapTap(TapPosition tapPosition, LatLng point) {
    setState(() => _currentLocation = point);
  }

  Future<void> _saveLocationArea() async {
    if (!_formKey.currentState!.validate()) return;

    final radius = double.tryParse(_radiusController.text) ?? _defaultRadius;
    final cooldown = int.tryParse(_cooldownController.text) ?? _defaultCooldown;

    final locationArea = widget.existingArea?.copyWith(
      name: _nameController.text,
      centerLatitude: _currentLocation.latitude,
      centerLongitude: _currentLocation.longitude,
      radius: radius,
      cooldownTime: cooldown,
    ) ?? LocationArea(
      projectId: widget.projectId,
      name: _nameController.text,
      centerLatitude: _currentLocation.latitude,
      centerLongitude: _currentLocation.longitude,
      radius: radius,
      cooldownTime: cooldown,
    );

    // Save to database and update tracking service if needed
    if (widget.existingArea != null) {
      // For existing areas, use LocationService to handle updates and notify background service
      await LocationService().updateLocationArea(locationArea);
    } else {
      // For new areas, save directly to database
      await _databaseService.saveLocationArea(locationArea);
    }

    if (mounted) Navigator.pop(context, locationArea);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingArea != null ? 'Edit Location Area' : 'Add Location Area'),
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _saveLocationArea,
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildForm(),
        Expanded(child: _buildMap()),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Location Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) =>
                value?.isEmpty ?? true ? 'Please enter a name' : null,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _radiusController,
                    decoration: const InputDecoration(
                      labelText: 'Radius (meters)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Please enter a radius';
                      final radius = double.tryParse(value!);
                      if (radius == null || radius <= 0) {
                        return 'Please enter a valid radius';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      final radius = double.tryParse(value);
                      if (radius != null && radius > 0) {
                        setState(() => _radius = radius);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _cooldownController,
                    decoration: const InputDecoration(
                      labelText: 'Cooldown (minutes)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Please enter a cooldown';
                      final cooldown = int.tryParse(value!);
                      if (cooldown == null || cooldown <= 0) {
                        return 'Please enter a valid cooldown';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMap() {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      child: FlutterMap(
        mapController: _mapController,
        options: MapOptions(
          center: _currentLocation,
          zoom: _defaultZoom,
          onTap: _onMapTap,
        ),
        children: [
          _buildTileLayer(),
          _buildCircleLayer(),
          _buildMarkerLayer(),
        ],
      ),
    );
  }

  Widget _buildTileLayer() {
    return TileLayer(
      urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
      userAgentPackageName: 'com.example.time_tracker_flutter',
    );
  }

  Widget _buildCircleLayer() {
    return CircleLayer(
      circles: [
        CircleMarker(
          point: _currentLocation,
          radius: _radius,
          color: Colors.blue.withOpacity(0.3),
          borderColor: Colors.blue,
          borderStrokeWidth: 2.0,
        ),
      ],
    );
  }

  Widget _buildMarkerLayer() {
    return MarkerLayer(
      markers: [
        Marker(
          point: _currentLocation,
          width: _markerSize,
          height: _markerSize,
          child: const Icon(
            Icons.location_on,
            color: Colors.red,
            size: _markerSize,
          ),
        ),
      ],
    );
  }
}
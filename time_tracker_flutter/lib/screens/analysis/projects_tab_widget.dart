import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/services/analysis_service.dart';
import 'package:time_tracker_flutter/utils/date_format_utils.dart';
import 'package:time_tracker_flutter/widgets/analysis/bar_chart_widget.dart';
import 'package:time_tracker_flutter/widgets/analysis/line_chart_widget.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';

class ProjectsTabWidget extends StatelessWidget {
  final AnalysisResult analysisResult;
  final AnalysisService analysisService;
  final AnalysisPeriod selectedPeriod;

  const ProjectsTabWidget({
    Key? key,
    required this.analysisResult,
    required this.analysisService,
    required this.selectedPeriod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (analysisResult == null || analysisResult.projectAnalysis.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.folder_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No project data available',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Try selecting a different time period',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final result = analysisResult;

    // Determine layout based on screen size
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final isTablet = ResponsiveUtils.isTablet(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    // Adjust layout based on screen dimensions
    final maxContentWidth = 800.0;
    final padding = isTablet ? 20.0 : 16.0;
    final cardPadding = isTablet ? 20.0 : 16.0;
    final chartHeight = isTablet ? 250.0 : (isLandscape ? 200.0 : 180.0);

    // Sort projects by total hours (descending)
    final sortedProjects = List<ProjectAnalysisData>.from(result.projectAnalysis)
      ..sort((a, b) => b.totalHours.compareTo(a.totalHours));

    // Calculate total project hours for comparison
    final totalProjectHours = sortedProjects.fold<double>(
      0, (sum, project) => sum + project.totalHours
    );

    // Find most active project
    final mostActiveProject = sortedProjects.isNotEmpty ? sortedProjects.first : null;

    // Find project with highest average hours per entry
    final highestAvgProject = sortedProjects.isNotEmpty
      ? sortedProjects.reduce((a, b) => a.averageHoursPerEntry > b.averageHoursPerEntry ? a : b)
      : null;

    return SingleChildScrollView(
      padding: EdgeInsets.all(padding),
      child: ConstrainedWidthContainer(
        maxWidth: maxContentWidth,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Projects Summary Card
            if (sortedProjects.length > 1) ...[
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: EdgeInsets.all(cardPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.dashboard,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Projects Overview',
                            style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 8),

                      // Projects comparison chart
                      Center(
                        child: AnalysisBarChart(
                          data: analysisService.getProjectDistribution(result),
                          title: '',
                          height: chartHeight,
                          showPercentages: true,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Key metrics
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Key Metrics',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildTrendSummaryStat(
                              context,
                              'Total Projects',
                              sortedProjects.length.toString(),
                              Icons.folder,
                              isSmallScreen,
                            ),
                            const SizedBox(height: 8),
                            if (mostActiveProject != null) _buildTrendSummaryStat(
                              context,
                              'Most Active Project',
                              '${mostActiveProject.project.name} (${analysisService.formatHours(mostActiveProject.totalHours)})',
                              Icons.star,
                              isSmallScreen,
                            ),
                            const SizedBox(height: 8),
                            if (highestAvgProject != null) _buildTrendSummaryStat(
                              context,
                              'Highest Avg Hours/Entry',
                              '${highestAvgProject.project.name} (${highestAvgProject.averageHoursPerEntry.toStringAsFixed(2)})',
                              Icons.trending_up,
                              isSmallScreen,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Project Cards Section
            Text(
              'Project Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            // Project Cards
            ...sortedProjects.map((projectData) {
              final projectColor = projectData.project.color ?? Theme.of(context).colorScheme.primary;
              final projectTimeData = _getProjectTimeSeriesData(projectData.project.id);
              final growthRate = _calculateProjectGrowthRate(projectTimeData);

              // Aggregate project time data for chart
              final aggregatedProjectData = analysisService.aggregateTimeSeriesData(
                projectTimeData,
                analysisResult.dateRange,
              );

              // Check if we should display monthly data
              final isMonthlyData = DateFormatUtils.shouldShowMonthlyData(analysisResult.dateRange);

              return Card(
                clipBehavior: Clip.antiAlias,
                elevation: 2,
                margin: const EdgeInsets.only(bottom: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Project header with color and progress indicator
                    Container(
                      decoration: BoxDecoration(
                        color: projectColor,
                        boxShadow: [
                          BoxShadow(
                            color: projectColor.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      width: double.infinity,
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  projectData.project.name,
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${projectData.percentageOfTotal.toStringAsFixed(1)}%',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Progress indicator showing percentage of total time
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: projectData.percentageOfTotal / 100,
                              backgroundColor: Colors.white.withOpacity(0.2),
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              minHeight: 6,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Project stats and trend
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Stats grid
                          LayoutBuilder(
                            builder: (context, constraints) {
                              final useGrid = constraints.maxWidth > 450;

                              if (useGrid) {
                                return GridView.count(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: isTablet ? 3 : 2,
                                  crossAxisSpacing: 16,
                                  mainAxisSpacing: 16,
                                  childAspectRatio: 2.0,
                                  children: [
                                    _buildProjectStatCard(
                                      context,
                                      'Total Hours',
                                      analysisService.formatHours(projectData.totalHours),
                                      Icons.access_time,
                                      projectColor,
                                    ),
                                    _buildProjectStatCard(
                                      context,
                                      'Entries',
                                      projectData.entryCount.toString(),
                                      Icons.list_alt,
                                      projectColor,
                                    ),
                                    _buildProjectStatCard(
                                      context,
                                      'Avg Hours/Entry',
                                      projectData.averageHoursPerEntry.toStringAsFixed(2),
                                      Icons.bar_chart,
                                      projectColor,
                                    ),
                                    _buildProjectStatCard(
                                      context,
                                      'Growth Rate',
                                      '${growthRate.toStringAsFixed(1)}%',
                                      growthRate >= 0 ? Icons.trending_up : Icons.trending_down,
                                      growthRate >= 0 ? Colors.green : Colors.red,
                                    ),
                                    _buildProjectStatCard(
                                      context,
                                      'Share of Total',
                                      '${projectData.percentageOfTotal.toStringAsFixed(1)}%',
                                      Icons.pie_chart,
                                      projectColor,
                                    ),
                                    _buildProjectStatCard(
                                      context,
                                      'Days Tracked',
                                      projectTimeData.length.toString(),
                                      Icons.calendar_today,
                                      projectColor,
                                    ),
                                  ],
                                );
                              } else {
                                // For smaller screens, use a column layout
                                return Column(
                                  children: [
                                    _buildProjectStat(
                                      context,
                                      'Total Hours',
                                      analysisService.formatHours(projectData.totalHours),
                                      Icons.access_time,
                                      projectColor,
                                    ),
                                    const Divider(height: 16),
                                    _buildProjectStat(
                                      context,
                                      'Entries',
                                      projectData.entryCount.toString(),
                                      Icons.list_alt,
                                      projectColor,
                                    ),
                                    const Divider(height: 16),
                                    _buildProjectStat(
                                      context,
                                      'Avg Hours/Entry',
                                      projectData.averageHoursPerEntry.toStringAsFixed(2),
                                      Icons.bar_chart,
                                      projectColor,
                                    ),
                                    const Divider(height: 16),
                                    _buildProjectStat(
                                      context,
                                      'Growth Rate',
                                      '${growthRate.toStringAsFixed(1)}%',
                                      growthRate >= 0 ? Icons.trending_up : Icons.trending_down,
                                      growthRate >= 0 ? Colors.green : Colors.red,
                                    ),
                                  ],
                                );
                              }
                            },
                          ),

                          // Project trend chart (if data available)
                          if (aggregatedProjectData.isNotEmpty) ...[
                            const SizedBox(height: 24),
                            Text(
                              'Time Trend',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: chartHeight,
                              child: AnalysisLineChart(
                                data: aggregatedProjectData,
                                title: '',
                                height: chartHeight,
                                showTrendline: true,
                                showMinMax: true,
                              ),
                            ),

                            // Trend insights
                            if (projectTimeData.length >= 3) ...[
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: projectColor.withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.insights,
                                          size: 16,
                                          color: projectColor,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Insights',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: projectColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      _getProjectInsight(projectData, growthRate, projectTimeData),
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  // Helper method to get project-specific time series data
  List<TimeSeriesData> _getProjectTimeSeriesData(String projectId) {
    if (analysisResult == null) return [];

    // Filter time series data for the specific project
    return analysisResult.timeSeriesData
        .where((data) => data.projectId == projectId)
        .toList();
  }

  // Helper method to calculate project growth rate
  double _calculateProjectGrowthRate(List<TimeSeriesData> projectData) {
    if (projectData.length < 2) return 0;

    // Sort by date
    final sortedData = List<TimeSeriesData>.from(projectData)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Split data into two halves to compare
    final midPoint = sortedData.length ~/ 2;
    final firstHalf = sortedData.sublist(0, midPoint);
    final secondHalf = sortedData.sublist(midPoint);

    // Calculate average hours for each half
    final firstHalfAvg = firstHalf.fold<double>(0, (sum, item) => sum + item.hours) / firstHalf.length;
    final secondHalfAvg = secondHalf.fold<double>(0, (sum, item) => sum + item.hours) / secondHalf.length;

    // Calculate growth rate
    if (firstHalfAvg == 0) return 0;
    return ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
  }

  // Helper method to generate project insights
  String _getProjectInsight(ProjectAnalysisData projectData, double growthRate, List<TimeSeriesData> projectTimeData) {
    if (projectTimeData.isEmpty) return 'No data available for insights.';

    final List<String> insights = [];

    // Growth trend insight
    if (growthRate > 20) {
      insights.add('This project shows strong growth (${growthRate.toStringAsFixed(1)}%) in time investment.');
    } else if (growthRate > 5) {
      insights.add('This project shows moderate growth (${growthRate.toStringAsFixed(1)}%) in time investment.');
    } else if (growthRate < -20) {
      insights.add('Time spent on this project has significantly decreased (${growthRate.toStringAsFixed(1)}%).');
    } else if (growthRate < -5) {
      insights.add('Time spent on this project has moderately decreased (${growthRate.toStringAsFixed(1)}%).');
    } else {
      insights.add('Time investment in this project has remained relatively stable.');
    }

    // Average time per entry insight
    if (projectData.averageHoursPerEntry > 3) {
      insights.add('Sessions for this project tend to be longer (${projectData.averageHoursPerEntry.toStringAsFixed(2)} hours on average).');
    } else if (projectData.averageHoursPerEntry < 1) {
      insights.add('Sessions for this project tend to be shorter (${projectData.averageHoursPerEntry.toStringAsFixed(2)} hours on average).');
    }

    // Consistency insight
    if (projectTimeData.length > 5) {
      final sortedData = List<TimeSeriesData>.from(projectTimeData)..sort((a, b) => a.date.compareTo(b.date));
      final totalDays = sortedData.last.date.difference(sortedData.first.date).inDays + 1;
      final consistency = (projectTimeData.length / totalDays) * 100;

      if (consistency > 70) {
        insights.add('Work on this project has been very consistent over the selected period.');
      } else if (consistency < 30) {
        insights.add('Work on this project has been sporadic over the selected period.');
      }
    }

    return insights.join(' ');
  }

  // Card-style project stat for grid layout
  Widget _buildProjectStatCard(BuildContext context, String label, String value, IconData icon, Color color) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Row-style project stat for column layout
  Widget _buildProjectStat(BuildContext context, String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method for trend summary stats
  Widget _buildTrendSummaryStat(BuildContext context, String label, String value, IconData icon, bool isSmallScreen) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: isSmallScreen ? Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ) : Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: isSmallScreen ? Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
          ) : Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class AnalysisFilterWidget extends StatefulWidget {
  final List<Project> projects;
  final String? selectedProjectId;
  final AnalysisPeriod selectedPeriod;
  final DateTimeRange dateRange;
  final bool isExpanded;
  final Function(String?) onProjectChanged;
  final Function(AnalysisPeriod) onPeriodChanged;
  final Function(DateTimeRange) onDateRangeChanged;
  final VoidCallback onToggleExpanded;

  const AnalysisFilterWidget({
    Key? key,
    required this.projects,
    required this.selectedProjectId,
    required this.selectedPeriod,
    required this.dateRange,
    required this.isExpanded,
    required this.onProjectChanged,
    required this.onPeriodChanged,
    required this.onDateRangeChanged,
    required this.onToggleExpanded,
  }) : super(key: key);

  @override
  State<AnalysisFilterWidget> createState() => _AnalysisFilterWidgetState();
}

class _AnalysisFilterWidgetState extends State<AnalysisFilterWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;
  late Animation<double> _blurAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _heightAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOutCubic,
      ),
    );

    _blurAnimation = Tween<double>(begin: 0, end: 10).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.98, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Initialize animation state
    if (widget.isExpanded) {
      _animationController.value = 1.0;
    } else {
      _animationController.value = 0.0;
    }
  }

  @override
  void didUpdateWidget(AnalysisFilterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _selectCustomDateRange() async {
    final initialDateRange = widget.dateRange;
    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: initialDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      widget.onDateRangeChanged(newDateRange);
    }
  }

  Widget _buildPeriodChip(AnalysisPeriod period, String label) {
    final isSelected = widget.selectedPeriod == period;

    // Get appropriate icon for each period
    IconData icon;
    switch (period) {
      case AnalysisPeriod.day:
        icon = Icons.today_rounded;
        break;
      case AnalysisPeriod.week:
        icon = Icons.view_week_rounded;
        break;
      case AnalysisPeriod.month:
        icon = Icons.calendar_month_rounded;
        break;
      case AnalysisPeriod.allTime:
        icon = Icons.all_inclusive_rounded;
        break;
      case AnalysisPeriod.custom:
        icon = Icons.date_range_rounded;
        break;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 8, bottom: 8),
      child: FilterChip(
        selected: isSelected,
        showCheckmark: false,
        avatar: Icon(
          icon,
          size: 18,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimaryContainer
              : Theme.of(context).colorScheme.primary,
        ),
        label: Text(
          label,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 13,
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
        onSelected: (_) => widget.onPeriodChanged(period),
        backgroundColor: Theme.of(context).colorScheme.surface.withOpacity(0.6),
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
        elevation: isSelected ? 1 : 0,
        pressElevation: 2,
        shadowColor: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 1.5 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return ClipRect(
          child: Align(
            heightFactor: _heightAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: widget.isExpanded ? _buildFilterContent() : const SizedBox.shrink(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFilterContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: _blurAnimation.value,
            sigmaY: _blurAnimation.value,
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.surface.withOpacity(0.7),
                  Theme.of(context).colorScheme.surface.withOpacity(0.5),
                ],
              ),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.15),
                  blurRadius: 10,
                  spreadRadius: 1,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.filter_alt_rounded,
                          size: 18,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Filter Analysis',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const Spacer(),
                      Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        clipBehavior: Clip.antiAlias,
                        child: IconButton(
                          icon: const Icon(Icons.close_rounded, size: 20),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(),
                          onPressed: widget.onToggleExpanded,
                          tooltip: 'Close filters',
                          style: IconButton.styleFrom(
                            foregroundColor: Theme.of(context).colorScheme.onSurface,
                            backgroundColor: Colors.transparent,
                            hoverColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Divider(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    height: 28,
                  ),

                  // Project selector
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // Use dropdown for small screens, segmented button for larger screens
                      if (constraints.maxWidth < 450) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.folder_outlined,
                                  size: 16,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Project',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                                  width: 1,
                                ),
                                color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
                              ),
                              child: DropdownButtonFormField<String?>(
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  isDense: true,
                                ),
                                value: widget.selectedProjectId,
                                icon: Icon(
                                  Icons.arrow_drop_down_rounded,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                dropdownColor: Theme.of(context).colorScheme.surface.withOpacity(0.9),
                                borderRadius: BorderRadius.circular(12),
                                items: [
                                  const DropdownMenuItem<String?>(
                                    value: null,
                                    child: Text('All Projects'),
                                  ),
                                  ...widget.projects.map((project) {
                                    return DropdownMenuItem<String?>(
                                      value: project.id,
                                      child: Text(project.name),
                                    );
                                  }),
                                ],
                                onChanged: widget.onProjectChanged,
                              ),
                            ),
                          ],
                        );
                      } else {
                        // For larger screens, show a row with label and dropdown
                        return Row(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.folder_outlined,
                                  size: 16,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Project:',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                                    width: 1,
                                  ),
                                  color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
                                ),
                                child: DropdownButtonFormField<String?>(
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    isDense: true,
                                  ),
                                  value: widget.selectedProjectId,
                                  icon: Icon(
                                    Icons.arrow_drop_down_rounded,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  dropdownColor: Theme.of(context).colorScheme.surface.withOpacity(0.9),
                                  borderRadius: BorderRadius.circular(12),
                                  items: [
                                    const DropdownMenuItem<String?>(
                                      value: null,
                                      child: Text('All Projects'),
                                    ),
                                    ...widget.projects.map((project) {
                                      return DropdownMenuItem<String?>(
                                        value: project.id,
                                        child: Text(project.name),
                                      );
                                    }),
                                  ],
                                  onChanged: widget.onProjectChanged,
                                ),
                              ),
                            ),
                          ],
                        );
                      }
                    },
                  ),

                  const SizedBox(height: 24),

                  // Time period selector
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Time Period',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Use segmented button for time period on larger screens
                  LayoutBuilder(
                    builder: (context, constraints) {
                      if (constraints.maxWidth >= 450) {
                        return Center(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                            ),
                            padding: const EdgeInsets.all(4),
                            child: SegmentedButton<AnalysisPeriod>(
                              segments: [
                                ButtonSegment(
                                  value: AnalysisPeriod.day,
                                  label: const Text('Today'),
                                  icon: const Icon(Icons.today, size: 16),
                                ),
                                ButtonSegment(
                                  value: AnalysisPeriod.week,
                                  label: const Text('Week'),
                                  icon: const Icon(Icons.view_week, size: 16),
                                ),
                                ButtonSegment(
                                  value: AnalysisPeriod.month,
                                  label: const Text('Month'),
                                  icon: const Icon(Icons.calendar_month, size: 16),
                                ),
                                ButtonSegment(
                                  value: AnalysisPeriod.allTime,
                                  label: const Text('All'),
                                  icon: const Icon(Icons.all_inclusive, size: 16),
                                ),
                                ButtonSegment(
                                  value: AnalysisPeriod.custom,
                                  label: const Text('Custom'),
                                  icon: const Icon(Icons.date_range, size: 16),
                                ),
                              ],
                              selected: {widget.selectedPeriod},
                              onSelectionChanged: (Set<AnalysisPeriod> selection) {
                                if (selection.isNotEmpty) {
                                  widget.onPeriodChanged(selection.first);
                                }
                              },
                              style: ButtonStyle(
                                visualDensity: VisualDensity.compact,
                                backgroundColor: MaterialStateProperty.resolveWith<Color>(
                                  (Set<MaterialState> states) {
                                    if (states.contains(MaterialState.selected)) {
                                      return Theme.of(context).colorScheme.primaryContainer;
                                    }
                                    return Colors.transparent;
                                  },
                                ),
                                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                                  (Set<MaterialState> states) {
                                    if (states.contains(MaterialState.selected)) {
                                      return Theme.of(context).colorScheme.onPrimaryContainer;
                                    }
                                    return Theme.of(context).colorScheme.onSurface;
                                  },
                                ),
                              ),
                            ),
                          ),
                        );
                      } else {
                        // Use chips for smaller screens
                        return Wrap(
                          spacing: 0,
                          runSpacing: 0,
                          alignment: WrapAlignment.start,
                          children: [
                            _buildPeriodChip(AnalysisPeriod.day, 'Today'),
                            _buildPeriodChip(AnalysisPeriod.week, 'This Week'),
                            _buildPeriodChip(AnalysisPeriod.month, 'This Month'),
                            _buildPeriodChip(AnalysisPeriod.allTime, 'All Time'),
                            _buildPeriodChip(AnalysisPeriod.custom, 'Custom'),
                          ],
                        );
                      }
                    },
                  ),

                  // Custom date range selector
                  if (widget.selectedPeriod == AnalysisPeriod.custom) ...[
                    const SizedBox(height: 16),
                    Center(
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _selectCustomDateRange,
                          borderRadius: BorderRadius.circular(12),
                          child: Ink(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.6),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                                width: 1.5,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.calendar_today_rounded,
                                    size: 18,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 10),
                                  FutureBuilder<String>(
                                    future: _formatDateRange(),
                                    builder: (context, snapshot) {
                                      return Text(
                                        snapshot.connectionState == ConnectionState.waiting
                                          ? '${LocaleDateUtils.formatDate(widget.dateRange.start, LocaleDateUtils.getCurrentLocale(context))} - ${LocaleDateUtils.formatDate(widget.dateRange.end, LocaleDateUtils.getCurrentLocale(context))}'
                                          : snapshot.hasError
                                            ? '${LocaleDateUtils.formatDate(widget.dateRange.start, LocaleDateUtils.getCurrentLocale(context))} - ${LocaleDateUtils.formatDate(widget.dateRange.end, LocaleDateUtils.getCurrentLocale(context))}'
                                            : snapshot.data!,
                                        style: TextStyle(
                                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      );
                                    },
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(
                                    Icons.arrow_drop_down_rounded,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<String> _formatDateRange() async {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    final startFormatted = await LocaleDateUtils.formatDateAsync(widget.dateRange.start, locale);
    final endFormatted = await LocaleDateUtils.formatDateAsync(widget.dateRange.end, locale);
    return '$startFormatted - $endFormatted';
  }
}
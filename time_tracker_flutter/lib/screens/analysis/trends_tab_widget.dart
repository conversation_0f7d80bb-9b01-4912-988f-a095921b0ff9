import 'dart:math';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/services/analysis_service.dart';
import 'package:time_tracker_flutter/utils/date_format_utils.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/widgets/analysis/bar_chart_widget.dart';
import 'package:time_tracker_flutter/widgets/analysis/line_chart_widget.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';

class TrendsTabWidget extends StatelessWidget {
  final AnalysisResult analysisResult;
  final AnalysisService analysisService;
  final AnalysisPeriod selectedPeriod;

  const TrendsTabWidget({
    Key? key,
    required this.analysisResult,
    required this.analysisService,
    required this.selectedPeriod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Update custom date format cache for better chart formatting
    LocaleDateUtils.updateCustomDateFormatCache();
    
    // Get screen size first
    final size = MediaQuery.of(context).size;
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    if (analysisResult.timeSeriesData.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.trending_up_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No trend data available',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Try selecting a different time period with more data',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final result = analysisResult;
    // Dynamic aggregation based on screen size and data points
    final aggregatedData = analysisService.aggregateTimeSeriesData(
      result.timeSeriesData,
      result.dateRange,
      maxPoints: size.width < 400 ? 15 : (size.width < 600 ? 20 : 30), // Fewer points on smaller screens
    );

    // Determine if we should show monthly data based on date range
    final isMonthlyData = DateFormatUtils.shouldShowMonthlyData(result.dateRange);

    // Adjust card sizes based on screen dimensions
    final cardPadding = isTablet ? 20.0 : 16.0;
    final chartHeight = isTablet ? 350.0 : (isLandscape ? 280.0 : 250.0);
    final maxContentWidth = 800.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 20.0 : 16.0),
      child: ConstrainedWidthContainer(
        maxWidth: maxContentWidth,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date range header
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.date_range,
                        size: 18,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Data Range',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    DateFormatUtils.getDateRangeDescriptionWithContext(result.dateRange, context),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  if (aggregatedData.length != result.timeSeriesData.length) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Data aggregated into ${aggregatedData.length} points for better visualization',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (aggregatedData.isNotEmpty && aggregatedData.first.aggregationLevel != null)
                      Text(
                        'Showing ${_getAggregationLevelText(aggregatedData.first.aggregationLevel!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                  ],
                ],
              ),
            ),
            // Line chart card with trend analysis
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.show_chart,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Hours Logged Over Time',
                            style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Tooltip(
                          message: 'Shows trend line and min/max points',
                          child: Icon(
                            Icons.insights,
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const SizedBox(height: 8),
                    Center(
                      child: AnalysisLineChart(
                        data: aggregatedData,
                        title: '',
                        height: chartHeight,
                        showTrendline: true, // Hide trendline on very small screens
                        showMinMax: true,
                        showLegend: true, // Enable the new legend feature
                        legendPosition: isLandscape ? LegendPosition.right : LegendPosition.bottom,
                      ),
                    ),
                    if (aggregatedData.length != result.timeSeriesData.length)
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 16,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'Data aggregated for better visualization (${aggregatedData.length} points)',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Bar chart card with improved visualization
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.bar_chart,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Daily Distribution',
                            style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Tooltip(
                          message: 'Shows distribution of hours across time periods',
                          child: Icon(
                            Icons.insights,
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const SizedBox(height: 8),
                    Center(
                      child: AnalysisBarChart(
                        data: aggregatedData.map((e) {
                          // Calculate percentage for each day relative to total
                          final totalHours = aggregatedData.fold<double>(
                            0, (sum, item) => sum + item.hours
                          );
                          final percentage = totalHours > 0
                              ? (e.hours / totalHours) * 100
                              : 0;

                          // Create a more informative label with date context and locale support
                          final label = DateFormatUtils.formatChartDateWithContext(
                            e.date,
                            selectedPeriod,
                            context,
                            isMonthlyData: isMonthlyData,
                            fullDateRange: result.dateRange,
                            dataPointIndex: aggregatedData.indexOf(e),
                            totalDataPoints: aggregatedData.length,
                          );

                          return TimeDistributionData(
                            label: label,
                            hours: e.hours,
                            percentage: percentage.toDouble(),
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                            // Include date information for better context
                            date: e.date,
                            aggregationLevel: e.aggregationLevel,
                            rangeStart: e.rangeStart,
                            rangeEnd: e.rangeEnd,
                            dataPointCount: e.dataPointCount,
                          );
                        }).toList(),
                        title: '',
                        height: chartHeight,
                        showPercentages: true,
                        showAverage: true,
                        // Use fewer bars for better mobile experience
                        maxBars: size.width < 400 ? 8 : (size.width < 600 ? 12 : 15),
                        // Add the date range to match line chart
                        dateRange: result.dateRange,
                        period: selectedPeriod,
                      ),
                    ),
                    if (aggregatedData.length != result.timeSeriesData.length)
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 16,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'Data aggregated for better visualization (${aggregatedData.length} points)',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Add a summary of the data
                    if (aggregatedData.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.analytics,
                                  size: 16,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Summary',
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            _buildTrendSummaryStat(
                              context,
                              'Average Hours per Period',
                              _formatAverageStat(aggregatedData, result.dateRange),
                              Icons.calculate,
                              isSmallScreen,
                            ),
                            const SizedBox(height: 8),
                            FutureBuilder<String>(
                              future: aggregatedData.isNotEmpty
                                  ? _formatDetailedDateRange(aggregatedData.reduce((a, b) => a.hours > b.hours ? a : b), context)
                                  : Future.value('N/A'),
                              builder: (context, snapshot) {
                                return _buildTrendSummaryStat(
                                  context,
                                  'Most Productive Period',
                                  snapshot.connectionState == ConnectionState.waiting
                                      ? 'Loading...'
                                      : snapshot.hasError
                                        ? 'Error loading'
                                        : snapshot.data ?? 'N/A',
                                  Icons.emoji_events,
                                  isSmallScreen,
                                );
                              },
                            ),
                            const SizedBox(height: 8),
                            _buildTrendSummaryStat(
                              context,
                              'Days Tracked',
                              _formatDaysTrackedStat(aggregatedData, result.dateRange),
                              Icons.calendar_today,
                              isSmallScreen,
                            ),

                            // Add consistency metric
                            if (aggregatedData.length > 3) ...[
                              const SizedBox(height: 8),
                              _buildTrendSummaryStat(
                                context,
                                'Consistency',
                                _calculateConsistencyMetric(aggregatedData),
                                Icons.repeat,
                                isSmallScreen,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to calculate consistency metric
  String _calculateConsistencyMetric(List<TimeSeriesData> timeData) {
    if (timeData.length < 3) return 'Insufficient data';

    // Sort by date
    final sortedData = List<TimeSeriesData>.from(timeData)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Calculate standard deviation of hours
    final avgHours = sortedData.fold<double>(0, (sum, item) => sum + item.hours) / sortedData.length;
    final sumSquaredDiff = sortedData.fold<double>(
      0, (sum, item) => sum + ((item.hours - avgHours) * (item.hours - avgHours))
    );
    final stdDev = sqrt(sumSquaredDiff / sortedData.length);

    // Calculate coefficient of variation (CV) - lower means more consistent
    final cv = stdDev / avgHours;

    // Calculate date consistency (gaps between entries)
    final dateGaps = <int>[];
    for (int i = 1; i < sortedData.length; i++) {
      final gap = sortedData[i].date.difference(sortedData[i-1].date).inDays;
      dateGaps.add(gap);
    }

    final avgGap = dateGaps.fold<double>(0, (sum, gap) => sum + gap) / dateGaps.length;

    // Combine metrics into a consistency score (0-100)
    // Lower CV and lower avg gap means higher consistency
    double consistencyScore = 100;

    // Penalize for high variation in hours (up to -50 points)
    if (cv > 0.1) consistencyScore -= (cv * 100).clamp(0, 50);

    // Penalize for gaps in tracking (up to -50 points)
    if (avgGap > 1) consistencyScore -= ((avgGap - 1) * 10).clamp(0, 50);

    // Determine consistency level
    String consistencyLevel;
    if (consistencyScore >= 90) {
      consistencyLevel = 'Excellent';
    } else if (consistencyScore >= 75) {
      consistencyLevel = 'Good';
    } else if (consistencyScore >= 60) {
      consistencyLevel = 'Moderate';
    } else if (consistencyScore >= 40) {
      consistencyLevel = 'Fair';
    } else {
      consistencyLevel = 'Poor';
    }

    return consistencyLevel;
  }

  // Format average hours stat with date range context
  String _formatAverageStat(List<TimeSeriesData> data, DateTimeRange dateRange) {
    final avgHours = data.fold<double>(0, (sum, item) => sum + item.hours) / data.length;
    return '${avgHours.toStringAsFixed(2)} hours';
  }

  // Format days tracked stat with date range context
  String _formatDaysTrackedStat(List<TimeSeriesData> data, DateTimeRange dateRange) {
    final totalDays = data.length;
    return '$totalDays days';
  }

  // Format date range in the format "Mar (1 - Apr 2, 2025)" or "Mar (1-5, 2025)" if same month
  Future<String> _formatDetailedDateRange(TimeSeriesData data, BuildContext context) async {
    // Get the date information
    final date = data.date;
    final hours = data.hours.toStringAsFixed(2);
    final locale = LocaleDateUtils.getCurrentLocale(context);

    // If this is aggregated data with range information
    if (data.isAggregated && data.rangeStart != null && data.rangeEnd != null) {
      // Use custom date formatting for better locale support
      final startFormatted = await LocaleDateUtils.formatDateAsync(data.rangeStart!, locale);
      final endFormatted = await LocaleDateUtils.formatDateAsync(data.rangeEnd!, locale);

      // For range data, show the formatted dates with hours
      if (data.rangeStart!.month != data.rangeEnd!.month || data.rangeStart!.year != data.rangeEnd!.year) {
        return '$startFormatted - $endFormatted ($hours hours)';
      } else {
        // Same month, show compact format
        return '$startFormatted - ${data.rangeEnd!.day} ($hours hours)';
      }
    }
    // For single day data
    else {
      final dateFormatted = await LocaleDateUtils.formatDateAsync(date, locale);
      return '$dateFormatted ($hours hours)';
    }
  }

  // Helper method to get aggregation level text
  String _getAggregationLevelText(DataAggregationLevel level) {
    switch (level) {
      case DataAggregationLevel.quarter:
        return 'quarterly data';
      case DataAggregationLevel.month:
        return 'monthly data';
      case DataAggregationLevel.week:
        return 'weekly data';
      case DataAggregationLevel.twoDays:
        return '2-day groups';
      default:
        return 'daily data';
    }
  }

  // Helper method to build trend summary stat
  Widget _buildTrendSummaryStat(
    BuildContext context, String label,
    String value, IconData icon, bool isSmallScreen) {
    final textStyle = isSmallScreen ? Theme.of(context).textTheme.bodySmall?.copyWith(
      fontWeight: FontWeight.w500,
    ) : Theme.of(context).textTheme.bodyMedium?.copyWith(
      fontWeight: FontWeight.w500,
    );

    return Container(
      constraints: BoxConstraints(maxWidth: 800),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: textStyle,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: textStyle,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}


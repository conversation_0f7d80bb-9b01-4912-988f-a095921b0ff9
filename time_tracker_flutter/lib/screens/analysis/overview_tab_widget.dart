import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/services/analysis_service.dart';
import 'package:time_tracker_flutter/widgets/analysis/bar_chart_widget.dart';
import 'package:time_tracker_flutter/widgets/analysis/pie_chart_widget.dart';
import 'package:time_tracker_flutter/widgets/analysis/stats_card_widget.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class OverviewTabWidget extends StatelessWidget {
  final AnalysisResult analysisResult;
  final AnalysisService analysisService;
  final AnalysisPeriod selectedPeriod;

  const OverviewTabWidget({
    Key? key,
    required this.analysisResult,
    required this.analysisService,
    required this.selectedPeriod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (analysisResult == null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Try changing the filters or selecting a different time period',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final result = analysisResult;

    // Determine layout based on screen size
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    // Adjust card sizes based on screen dimensions
    final cardPadding = isTablet ? 20.0 : 16.0;
    final chartHeight = isTablet ? 350.0 : (isLandscape ? 280.0 : 250.0);
    final maxContentWidth = 800.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 20.0 : 16.0),
      child: ConstrainedWidthContainer(
        maxWidth: maxContentWidth,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary section with stats cards
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.summarize,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Summary',
                          style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const SizedBox(height: 8),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: isTablet ? (isLandscape ? 4 : 2) : 2,
                        crossAxisSpacing: 16.0,
                        mainAxisSpacing: 16.0,
                        childAspectRatio: isTablet ? 1.8 : 1.5,
                      ),
                      itemCount: result.mostProductiveDay != null ? 4 : 3,
                      itemBuilder: (context, index) {
                        switch (index) {
                          case 0:
                            return StatsCard(
                              title: 'Total Hours',
                              value: analysisService.formatHours(result.totalHours),
                              icon: Icons.access_time,
                              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                              iconColor: Theme.of(context).colorScheme.onPrimaryContainer,
                            );
                          case 1:
                            return StatsCard(
                              title: 'Total Entries',
                              value: result.totalEntries.toString(),
                              icon: Icons.list_alt,
                              backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                              iconColor: Theme.of(context).colorScheme.onSecondaryContainer,
                            );
                          case 2:
                            return StatsCard(
                              title: 'Avg Hours/Entry',
                              value: result.averageHoursPerEntry.toStringAsFixed(2),
                              icon: Icons.bar_chart,
                              backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
                              iconColor: Theme.of(context).colorScheme.onTertiaryContainer,
                            );
                          case 3:
                            return _buildMostProductiveDayCard(context, result);
                          default:
                            return const SizedBox.shrink();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Weekday distribution chart
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.pie_chart,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Time Distribution by Weekday',
                          style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const SizedBox(height: 8),
                    Center(
                      child: AnalysisPieChart(
                        data: analysisService.getWeekdayDistribution(result),
                        title: '',
                        height: chartHeight,
                      ),
                    ),

                    // Add weekday insights
                    const SizedBox(height: 16),
                    _buildWeekdayInsights(context, result),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Project distribution chart (only shown when no specific project is selected)
            if (result.projectAnalysis.isNotEmpty && analysisResult.projectAnalysis.length > 1)
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: EdgeInsets.all(cardPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.bar_chart,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Project Distribution',
                            style: isSmallScreen ? Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ) : Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 8),
                      Center(
                        child: AnalysisBarChart(
                          data: analysisService.getProjectDistribution(result),
                          title: '',
                          height: chartHeight,
                          showPercentages: true,
                          showAverage: true,
                          maxBars: result.projectAnalysis.length > 10 ? 10 : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper method to build weekday insights
  Widget _buildWeekdayInsights(BuildContext context, AnalysisResult result) {
    // Get weekday distribution data
    final weekdayData = analysisService.getWeekdayDistribution(result);
    if (weekdayData.isEmpty) return const SizedBox.shrink();

    // Find most and least productive days
    final mostProductiveDay = weekdayData.reduce((a, b) => a.hours > b.hours ? a : b);
    final leastProductiveDay = weekdayData.reduce((a, b) => a.hours < b.hours ? a : b);

    // Calculate weekday balance (how evenly distributed the work is)
    final totalHours = weekdayData.fold<double>(0, (sum, day) => sum + day.hours);
    final avgHoursPerDay = totalHours / weekdayData.length;
    final deviations = weekdayData.map((day) => (day.hours - avgHoursPerDay).abs() / avgHoursPerDay);
    final avgDeviation = deviations.fold<double>(0, (sum, dev) => sum + dev) / deviations.length;

    // Determine balance level
    String balanceLevel;
    if (avgDeviation < 0.2) {
      balanceLevel = 'very balanced';
    } else if (avgDeviation < 0.4) {
      balanceLevel = 'fairly balanced';
    } else if (avgDeviation < 0.6) {
      balanceLevel = 'somewhat unbalanced';
    } else {
      balanceLevel = 'highly unbalanced';
    }

    // Generate insight text
    final insightText = 'Your work pattern is $balanceLevel across weekdays. '
        '${mostProductiveDay.label} is your most productive day (${mostProductiveDay.percentage.toStringAsFixed(1)}% of total time), '
        'while ${leastProductiveDay.label} is your least active (${leastProductiveDay.percentage.toStringAsFixed(1)}%).';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.insights,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Weekday Insights',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            insightText,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildDayIndicator(
                context,
                'Most Active: ${mostProductiveDay.label}',
                Colors.green.shade600,
              ),
              const SizedBox(width: 16),
              _buildDayIndicator(
                context,
                'Least Active: ${leastProductiveDay.label}',
                Colors.red.shade600,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build day indicator
  Widget _buildDayIndicator(BuildContext context, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // Helper method to build most productive day card with async date formatting
  Widget _buildMostProductiveDayCard(BuildContext context, AnalysisResult result) {
    if (result.mostProductiveDay == null) return const SizedBox.shrink();
    
    return FutureBuilder<String>(
      future: LocaleDateUtils.formatDateAsync(result.mostProductiveDay!.date, LocaleDateUtils.getCurrentLocale(context)),
      builder: (context, snapshot) {
        return StatsCard(
          title: 'Most Productive',
          value: snapshot.connectionState == ConnectionState.waiting
            ? LocaleDateUtils.formatDate(result.mostProductiveDay!.date, LocaleDateUtils.getCurrentLocale(context))
            : snapshot.hasError
              ? LocaleDateUtils.formatDate(result.mostProductiveDay!.date, LocaleDateUtils.getCurrentLocale(context))
              : snapshot.data!,
          icon: Icons.star,
          backgroundColor: Theme.of(context).colorScheme.errorContainer,
          iconColor: Theme.of(context).colorScheme.onErrorContainer,
        );
      },
    );
  }
}

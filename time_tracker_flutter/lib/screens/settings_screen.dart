import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/screens/backup_list_screen.dart';
import 'package:time_tracker_flutter/screens/cloud_backup_list_screen.dart';
import 'package:time_tracker_flutter/services/backup_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/location_service.dart';
import 'package:time_tracker_flutter/theme/theme_provider.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/date_format_picker_dialog.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:intl/intl.dart';

class SettingsScreen extends StatefulWidget {
  final bool isInTabView;

  const SettingsScreen({super.key, this.isInTabView = false});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final BackupService _backupService = BackupService();
  final LocationService _locationService = LocationService();
  bool _isAutoBackupEnabled = false;
  bool _isCloudAutoBackupEnabled = false;
  String? _customDateFormat;
  bool _isLoading = false;
  bool _isExporting = false;
  bool _isImporting = false;
  bool _isBackingUp = false;
  bool _isCloudBackingUp = false;
  bool _isTestingNotification = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final isAutoBackupEnabled = await _databaseService.isAutoBackupEnabled();
    final isCloudAutoBackupEnabled = await _databaseService.isCloudAutoBackupEnabled();
    final customDateFormat = await _databaseService.getCustomDateFormat();

    setState(() {
      _isAutoBackupEnabled = isAutoBackupEnabled;
      _isCloudAutoBackupEnabled = isCloudAutoBackupEnabled;
      _customDateFormat = customDateFormat;
    });
  }

  Future<void> _toggleAutoBackup(bool value) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.setAutoBackup(value);
      setState(() {
        _isAutoBackupEnabled = value;
      });
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error toggling auto backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportData() async {
    setState(() {
      _isExporting = true;
    });

    try {
      final backupData = await _databaseService.exportDatabase();
      final jsonString = jsonEncode(backupData.toJson());

      final directory = await getTemporaryDirectory();
      final fileName = 'time-tracker-backup-${DateTime.now().toIso8601String().split('T')[0]}.json';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsString(jsonString);

      if (mounted) {
        await Share.shareXFiles(
          [XFile(filePath)],
          subject: 'Time Tracker Backup',
        );
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error exporting data: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _importData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Import Data',
        content: 'This will replace all existing data. Are you sure you want to continue?',
        confirmText: 'Import',
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isImporting = true;
    });

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final jsonString = await file.readAsString();
        final jsonData = jsonDecode(jsonString);
        final backupData = BackupData.fromJson(jsonData);

        await _databaseService.importDatabase(backupData);

        if (mounted) {
          showSuccessSnackBar('Data imported successfully', context);
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error importing data: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isImporting = false;
      });
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _isBackingUp = true;
    });

    try {
      final filename = await _backupService.createBackup();

      if (mounted) {
        showSuccessSnackBar('Backup created: $filename', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error creating backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isBackingUp = false;
      });
    }
  }

  void _navigateToBackupList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BackupListScreen(),
      ),
    );
  }

  void _navigateToCloudBackupList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CloudBackupListScreen(),
      ),
    );
  }

  Future<void> _toggleCloudAutoBackup(bool value) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.setCloudAutoBackup(value);
      setState(() {
        _isCloudAutoBackupEnabled = value;
      });
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error toggling cloud auto backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectCustomDateFormat() async {
    final selectedFormat = await showDialog<String>(
      context: context,
      builder: (context) => DateFormatPickerDialog(
        initialFormat: _customDateFormat,
      ),
    );

    if (selectedFormat != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _databaseService.setCustomDateFormat(selectedFormat);
        setState(() {
          _customDateFormat = selectedFormat;
        });

        if (mounted) {
          showSuccessSnackBar('Date format updated successfully', context);
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error updating date format: ${e.toString()}', context);
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getDateFormatPreview() {
    if (_customDateFormat == null || _customDateFormat!.isEmpty) {
      return 'System default';
    }

    try {
      final format = DateFormat(_customDateFormat!);
      return format.format(DateTime.now());
    } catch (e) {
      return 'Invalid format';
    }
  }

  Future<void> _createCloudBackup() async {
    setState(() {
      _isCloudBackingUp = true;
    });

    try {
      await _databaseService.backupToCloud();

      if (mounted) {
        showSuccessSnackBar('Cloud backup created successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error creating cloud backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isCloudBackingUp = false;
      });
    }
  }

  Future<void> _testNotification() async {
    setState(() {
      _isTestingNotification = true;
    });

    try {
      final success = await _locationService.startTracking();

      if (mounted) {
        if (success) {
          showSuccessSnackBar('Test notification sent', context);
        } else {
          showErrorSnackBar('Failed to send notification', context);
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error testing notification: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isTestingNotification = false;
      });
    }
  }

  Future<void> _stopNotificationTest() async {
    try {
      await _locationService.stopTracking();

      if (mounted) {
        showSuccessSnackBar('Notification test stopped', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error stopping notification: ${e.toString()}', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: widget.isInTabView
          ? AppBar(
              title: const Text('Settings'),
              automaticallyImplyLeading: false,
            )
          : AppBar(
              title: const Text('Settings'),
            ),
      body: ListView(
        children: [
          // Theme settings
          ListTile(
            title: const Text('Dark Mode'),
            subtitle: const Text('Toggle between light and dark theme'),
            trailing: Switch(
              value: themeProvider.isDarkMode,
              onChanged: (value) {
                themeProvider.toggleTheme();
              },
            ),
          ),
          const Divider(),

          // Date format settings
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Date Format',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ListTile(
            title: const Text('Custom Date Format'),
            subtitle: Text(_customDateFormat ?? 'Using system default'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getDateFormatPreview(),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(Icons.edit),
              ],
            ),
            onTap: _selectCustomDateFormat,
          ),
          const Divider(),

          // Backup settings
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Backup & Restore',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ListTile(
            title: const Text('Auto Backup'),
            subtitle: const Text('Automatically backup data when changes are made'),
            trailing: Switch(
              value: _isAutoBackupEnabled,
              onChanged: _isLoading ? null : _toggleAutoBackup,
            ),
          ),
          ListTile(
            title: const Text('Export Data'),
            subtitle: const Text('Export all data to a file'),
            trailing: _isExporting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.file_download),
            onTap: _isExporting ? null : _exportData,
          ),
          ListTile(
            title: const Text('Import Data'),
            subtitle: const Text('Import data from a file'),
            trailing: _isImporting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.file_upload),
            onTap: _isImporting ? null : _importData,
          ),
          const Divider(),

          // Local backups
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Local Backups',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ListTile(
            title: const Text('Create Backup'),
            subtitle: const Text('Create a new local backup'),
            trailing: _isBackingUp
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.backup),
            onTap: _isBackingUp ? null : _createBackup,
          ),
          ListTile(
            title: const Text('Manage Backups'),
            subtitle: const Text('View and restore local backups'),
            trailing: const Icon(Icons.folder),
            onTap: _navigateToBackupList,
          ),

          // Cloud backup
          const Divider(),
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Cloud Backups',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ListTile(
            title: const Text('Cloud Auto Backup'),
            subtitle: const Text('Automatically backup data to the cloud'),
            trailing: Switch(
              value: _isCloudAutoBackupEnabled,
              onChanged: _isLoading ? null : _toggleCloudAutoBackup,
            ),
          ),
          ListTile(
            title: const Text('Create Cloud Backup'),
            subtitle: const Text('Create a new cloud backup'),
            trailing: _isCloudBackingUp
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.cloud_upload),
            onTap: _isCloudBackingUp ? null : _createCloudBackup,
          ),
          ListTile(
            title: const Text('Manage Cloud Backups'),
            subtitle: const Text('View, restore, and transfer cloud backups'),
            trailing: const Icon(Icons.cloud),
            onTap: _navigateToCloudBackupList,
          ),

          // Location Tracking Test Section
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Location Tracking',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Column(
              children: [
                ListTile(
                  title: const Text('Test Tracking Notification'),
                  subtitle: const Text('Send a test notification to verify lock screen display'),
                  trailing: _isTestingNotification
                      ? const CircularProgressIndicator()
                      : const Icon(Icons.notifications_active),
                  onTap: _isTestingNotification ? null : _testNotification,
                ),
                ListTile(
                  title: const Text('Stop Notification Test'),
                  subtitle: const Text('Cancel the test notification'),
                  trailing: const Icon(Icons.notifications_off),
                  onTap: _stopNotificationTest,
                ),
              ],
            ),
          ),

          // Developer tools section
          const Divider(),
          const Padding(
            padding: EdgeInsets.only(left: 16, top: 16, bottom: 8),
            child: Text(
              'Developer Tools',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          ListTile(
            title: const Text('Reset Recovery Key'),
            subtitle: const Text('Clear recovery key to test recovery functionality'),
            trailing: const Icon(Icons.key_off),
            onTap: _resetRecoveryKey,
          ),
        ],
      ),
    );
  }

  Future<void> _resetRecoveryKey() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Reset Recovery Key',
        content: 'This will clear your current recovery key for testing recovery functionality. You\'ll need to use your recovery code to restore access. Continue?',
        confirmText: 'Reset',
        destructive: true,
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _databaseService.resetRecoveryKey();

      if (mounted) {
        showSuccessSnackBar('Recovery key reset successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error resetting recovery key: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

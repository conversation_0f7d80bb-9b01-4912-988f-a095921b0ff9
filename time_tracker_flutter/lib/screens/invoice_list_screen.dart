import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/services/invoice_service.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_screen.dart';
import 'package:time_tracker_flutter/screens/invoice_detail_screen.dart';
import 'package:time_tracker_flutter/screens/pdf_preview_screen.dart';
import 'package:time_tracker_flutter/widgets/invoice_card.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/common_loading_indicator.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/screens/invoice_settings_screen.dart';
import 'package:time_tracker_flutter/screens/client_management_screen.dart';

/// Screen for displaying and managing invoices
class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  State<InvoiceListScreen> createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final ClientService _clientService = ClientService();
  final InvoiceService _invoiceService = InvoiceService();
  final PDFService _pdfService = PDFService();

  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  Map<String, Client> _clientsMap = {};
  bool _isLoading = true;
  bool _isGeneratingPdf = false;

  // Filter state
  InvoiceStatus? _selectedStatus;
  String? _selectedClientId;
  DateTimeRange? _selectedDateRange;
  String _searchQuery = '';

  // UI state
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load invoices and clients in parallel
      final results = await Future.wait([
        _databaseService.getInvoices(),
        _clientService.getClients(),
      ]);

      final invoices = results[0] as List<Invoice>;
      final clients = results[1] as List<Client>;

      // Create clients map for quick lookup
      final clientsMap = <String, Client>{};
      for (final client in clients) {
        clientsMap[client.id] = client;
      }

      // Sort invoices by issue date (newest first)
      invoices.sort((a, b) => b.issueDate.compareTo(a.issueDate));

      setState(() {
        _invoices = invoices;
        _clientsMap = clientsMap;
        _isLoading = false;
      });

      _applyFilters();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        showErrorSnackBar('Error loading invoices: ${e.toString()}', context);
      }
    }
  }

  void _applyFilters() {
    List<Invoice> filtered = List.from(_invoices);

    // Apply status filter
    if (_selectedStatus != null) {
      filtered = filtered.where((invoice) => invoice.status == _selectedStatus).toList();
    }

    // Apply client filter
    if (_selectedClientId != null) {
      filtered = filtered.where((invoice) => invoice.clientId == _selectedClientId).toList();
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((invoice) {
        final issueDate = invoice.issueDate;
        return issueDate.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               issueDate.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        final client = _clientsMap[invoice.clientId];
        return invoice.invoiceNumber.toLowerCase().contains(query) ||
               (client?.name.toLowerCase().contains(query) ?? false) ||
               (invoice.notes?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    setState(() {
      _filteredInvoices = filtered;
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedClientId = null;
      _selectedDateRange = null;
      _searchQuery = '';
    });
    _applyFilters();
  }

  Future<void> _showStatusChangeDialog(Invoice invoice) async {
    final newStatus = await showDialog<InvoiceStatus>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Invoice Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: InvoiceStatus.values.map((status) {
            return RadioListTile<InvoiceStatus>(
              title: Text(_getStatusDisplayText(status)),
              value: status,
              groupValue: invoice.status,
              onChanged: (value) => Navigator.of(context).pop(value),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (newStatus != null && newStatus != invoice.status) {
      try {
        await _invoiceService.updateInvoiceStatus(invoice.id, newStatus);

        // If marking as paid, show a confirmation message
        if (newStatus == InvoiceStatus.paid) {
          if (mounted) {
            showSuccessSnackBar(
              'Invoice marked as paid. Time entries are now marked as billed and will not be available for future invoices.',
              context,
            );
          }
        } else {
          if (mounted) {
            showSuccessSnackBar('Invoice status updated', context);
          }
        }

        await _loadData();
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error updating status: ${e.toString()}', context);
        }
      }
    }
  }

  Future<void> _deleteInvoice(Invoice invoice) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Invoice',
        content: 'Are you sure you want to delete invoice ${invoice.invoiceNumber}? This action cannot be undone.',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseService.deleteInvoice(invoice.id);
        await _loadData();

        if (mounted) {
          showSuccessSnackBar('Invoice deleted successfully', context);
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error deleting invoice: ${e.toString()}', context);
        }
      }
    }
  }

  void _showCreateInvoiceDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InvoiceCreateScreen(),
      ),
    ).then((result) {
      // Refresh the list if an invoice was created
      if (result != null) {
        debugPrint('Invoice creation result received, refreshing list...');
        _loadData();
      }
    });
  }

  void _showEditInvoiceDialog(Invoice invoice) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceCreateScreen(invoice: invoice),
      ),
    ).then((result) {
      // Refresh the list if the invoice was updated
      if (result != null) {
        _loadData();
      }
    });
  }

  Future<void> _previewInvoicePdf(Invoice invoice) async {
    final client = _clientsMap[invoice.clientId];
    if (client == null) {
      if (mounted) {
        showErrorSnackBar('Client not found for this invoice', context);
      }
      return;
    }

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Create basic business info (this would normally come from settings)
      final businessInfo = BusinessInfo(
        name: 'Your Business Name',
        email: '<EMAIL>',
        phone: '+****************',
        address: Address(
          street: '123 Business St',
          city: 'Business City',
          postalCode: '12345',
          country: 'Country',
        ),
      );

      final filePath = await _pdfService.generatePDFForPreview(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        template: InvoiceTemplate.professional,
        locale: InvoiceLocalizationService.getLocaleFromLanguage(invoice.locale),
      );

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFPreviewScreen(
              filePath: filePath,
              title: 'Invoice ${invoice.invoiceNumber} Preview',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error generating PDF preview: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Invoices'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Invoice Settings',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const InvoiceSettingsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.people),
            tooltip: 'Manage Clients',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ClientManagementScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
          if (_hasActiveFilters())
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearFilters,
              tooltip: 'Clear filters',
            ),
        ],
      ),
      body: ConstrainedWidthContainer(
        maxWidth: 800,
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                decoration: const InputDecoration(
                  hintText: 'Search invoices...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                  _applyFilters();
                },
              ),
            ),

            // Filters panel
            if (_showFilters) _buildFiltersPanel(),

            // Invoice list
            Expanded(
              child: _isLoading
                  ? const Center(child: CommonLoadingIndicator.large(message: 'Loading invoices...'))
                  : _filteredInvoices.isEmpty
                      ? _buildEmptyState()
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: ListView.builder(
                            itemCount: _filteredInvoices.length,
                            itemBuilder: (context, index) {
                              final invoice = _filteredInvoices[index];
                              final client = _clientsMap[invoice.clientId];

                              return InvoiceCard(
                                invoice: invoice,
                                client: client,
                                onTap: () => _showEditInvoiceDialog(invoice),
                                onEdit: () => _showEditInvoiceDialog(invoice),
                                onDelete: () => _deleteInvoice(invoice),
                                onStatusChange: () => _showStatusChangeDialog(invoice),
                                onDetail: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => InvoiceDetailScreen(invoiceId: invoice.id),
                                    ),
                                  );
                                },
                                onPreviewPdf: _isGeneratingPdf ? null : () => _previewInvoicePdf(invoice),
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateInvoiceDialog,
        tooltip: 'Create Invoice',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFiltersPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          // Status filter
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // Status chips
              ...InvoiceStatus.values.map((status) {
                final isSelected = _selectedStatus == status;
                return FilterChip(
                  label: Text(_getStatusDisplayText(status)),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = selected ? status : null;
                    });
                    _applyFilters();
                  },
                );
              }),

              // Date range filter
              FilterChip(
                label: Text(_selectedDateRange != null
                    ? 'Date Range'
                    : 'All Dates'),
                selected: _selectedDateRange != null,
                onSelected: (selected) async {
                  if (selected) {
                    final dateRange = await showDateRangePicker(
                      context: context,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      initialDateRange: _selectedDateRange,
                    );

                    if (dateRange != null) {
                      setState(() {
                        _selectedDateRange = dateRange;
                      });
                      _applyFilters();
                    }
                  } else {
                    setState(() {
                      _selectedDateRange = null;
                    });
                    _applyFilters();
                  }
                },
              ),

              // Client filter
              FilterChip(
                label: Text(_selectedClientId != null
                    ? _clientsMap[_selectedClientId]?.name ?? 'Unknown Client'
                    : 'All Clients'),
                selected: _selectedClientId != null,
                onSelected: (selected) async {
                  if (selected) {
                    await _showClientFilterDialog();
                  } else {
                    setState(() {
                      _selectedClientId = null;
                    });
                    _applyFilters();
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _showClientFilterDialog() async {
    final selectedClient = await showDialog<Client>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Client'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _clientsMap.values.length,
            itemBuilder: (context, index) {
              final client = _clientsMap.values.elementAt(index);
              return ListTile(
                title: Text(client.name),
                subtitle: client.email != null ? Text(client.email!) : null,
                onTap: () => Navigator.of(context).pop(client),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (selectedClient != null) {
      setState(() {
        _selectedClientId = selectedClient.id;
      });
      _applyFilters();
    }
  }

  Widget _buildEmptyState() {
    if (_hasActiveFilters()) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            const SizedBox(height: 16),
            Text(
              'No invoices match your filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: _clearFilters,
              child: const Text('Clear filters'),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          Text(
            'No invoices yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          const Text('Create your first invoice to get started'),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showCreateInvoiceDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create Invoice'),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedStatus != null ||
           _selectedClientId != null ||
           _selectedDateRange != null ||
           _searchQuery.isNotEmpty;
  }

  String _getStatusDisplayText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
    }
  }
}
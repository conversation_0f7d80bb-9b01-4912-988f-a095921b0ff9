import 'package:flutter/material.dart';

/// Navigation buttons for the invoice creation process
class InvoiceNavigationButtons extends StatelessWidget {
  final int currentStep;
  final bool Function(int) canProceedFromStep;
  final bool isEditing;
  final VoidCallback onPrevious;
  final VoidCallback onNext;
  final VoidCallback onSave;

  const InvoiceNavigationButtons({
    super.key,
    required this.currentStep,
    required this.canProceedFromStep,
    required this.isEditing,
    required this.onPrevious,
    required this.onNext,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          if (currentStep > 0)
            OutlinedButton(
              onPressed: onPrevious,
              child: const Text('Previous'),
            ),
          const Spacer(),
          if (currentStep < 2)
            FilledButton(
              onPressed: canProceedFromStep(currentStep) ? onNext : null,
              child: const Text('Next'),
            )
          else
            FilledButton(
              onPressed: canProceedFromStep(currentStep) ? onSave : null,
              child: Text(isEditing ? 'Update Invoice' : 'Create Invoice'),
            ),
        ],
      ),
    );
  }
} 
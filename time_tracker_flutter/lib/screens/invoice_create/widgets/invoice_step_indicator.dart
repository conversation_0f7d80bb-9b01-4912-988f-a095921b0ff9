import 'package:flutter/material.dart';

/// Step indicator widget for the invoice creation process
class InvoiceStepIndicator extends StatelessWidget {
  final int currentStep;
  final bool Function(int) canProceedFromStep;

  const InvoiceStepIndicator({
    super.key,
    required this.currentStep,
    required this.canProceedFromStep,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          for (int i = 0; i < 3; i++) ...[
            _buildStepCircle(context, i),
            if (i < 2) Expanded(child: _buildStepConnector(context, i)),
          ],
        ],
      ),
    );
  }

  Widget _buildStepCircle(BuildContext context, int step) {
    final isActive = step == currentStep;
    final isCompleted = step < currentStep;
    final canProceed = canProceedFromStep(step);

    Color backgroundColor;
    Color textColor;
    Widget child;

    if (isCompleted) {
      backgroundColor = Theme.of(context).colorScheme.primary;
      textColor = Theme.of(context).colorScheme.onPrimary;
      child = const Icon(Icons.check_rounded, size: 18);
    } else if (isActive) {
      backgroundColor = canProceed
          ? Theme.of(context).colorScheme.primary
          : Theme.of(context).colorScheme.outline;
      textColor = canProceed
          ? Theme.of(context).colorScheme.onPrimary
          : Theme.of(context).colorScheme.onSurface;
      child = Text(
        '${step + 1}',
        style: TextStyle(
          color: textColor,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      );
    } else {
      backgroundColor = Theme.of(context).colorScheme.surfaceContainerHighest;
      textColor = Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6);
      child = Text(
        '${step + 1}',
        style: TextStyle(color: textColor, fontSize: 14),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        boxShadow: isActive || isCompleted
            ? [
                BoxShadow(
                  color: backgroundColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Center(child: child),
    );
  }

  Widget _buildStepConnector(BuildContext context, int step) {
    final isCompleted = step < currentStep;
    return Container(
      height: 3,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        gradient: isCompleted
            ? LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                ],
              )
            : null,
        color: isCompleted
            ? null
            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }
} 
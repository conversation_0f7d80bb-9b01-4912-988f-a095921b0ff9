import 'package:flutter/material.dart';

/// App bar for the invoice create screen with progress indicator and preview button
class InvoiceCreateAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool isEditing;
  final int currentStep;
  final bool showPreview;
  final VoidCallback onPreviewToggle;

  const InvoiceCreateAppBar({
    super.key,
    required this.isEditing,
    required this.currentStep,
    required this.showPreview,
    required this.onPreviewToggle,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(isEditing ? 'Edit Invoice' : 'Create Invoice'),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(4.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: LinearProgressIndicator(
            value: (currentStep + 1) / 3,
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ),
      ),
      actions: [
        if (currentStep == 2)
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: _buildPreviewButton(context),
          ),
      ],
    );
  }

  Widget _buildPreviewButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPreviewToggle,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  showPreview ? Icons.edit_rounded : Icons.preview_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  showPreview ? 'Edit' : 'Preview',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 4.0);
} 
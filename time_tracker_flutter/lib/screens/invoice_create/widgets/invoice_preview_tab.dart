import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_controller.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Invoice preview tab widget
class InvoicePreviewTab extends StatelessWidget {
  final InvoiceCreateController controller;

  const InvoicePreviewTab({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.preview_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Invoice Preview',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Preview your invoice before saving',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            InvoiceLocalizationService.getTranslation('invoice', controller.locale),
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '${InvoiceLocalizationService.getTranslation('date', controller.locale)}: ${LocaleDateUtils.formatDate(controller.issueDate)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              if (controller.dueDate != null)
                                Text(
                                  '${InvoiceLocalizationService.getTranslation('due', controller.locale)}: ${LocaleDateUtils.formatDate(controller.dueDate!)}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              if (controller.servicePeriodEnabled && controller.servicePeriodStart != null && controller.servicePeriodEnd != null)
                                Text(
                                  '${InvoiceLocalizationService.getTranslation('service_period', controller.locale)}: ${LocaleDateUtils.formatDate(controller.servicePeriodStart!)} - ${LocaleDateUtils.formatDate(controller.servicePeriodEnd!)}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                      // Client information
                      if (controller.selectedClient != null) ...[
                        Text(
                          InvoiceLocalizationService.getTranslation('bill_to', controller.locale),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(controller.selectedClient!.name),
                        if (controller.selectedClient!.email != null)
                          Text(controller.selectedClient!.email!),
                        if (controller.selectedClient!.address != null) ...[
                          Text(controller.selectedClient!.address!.street),
                          Text('${controller.selectedClient!.address!.city}, ${controller.selectedClient!.address!.state} ${controller.selectedClient!.address!.postalCode}'),
                          Text(controller.selectedClient!.address!.country),
                        ],
                        const SizedBox(height: 32),
                      ],

                      // Line items
                      _buildLineItems(),

                      const SizedBox(height: 24),

                      // Totals
                      _buildTotals(),

                      // Notes
                      if (controller.notes.trim().isNotEmpty) ...[
                        const SizedBox(height: 24),
                        Text(
                          InvoiceLocalizationService.getTranslation('notes', controller.locale),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(controller.notes.trim()),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineItems() {
    final items = <Widget>[];

    // Add header
    items.add(
      Builder(
        builder: (context) => Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                InvoiceLocalizationService.getTranslation('description', controller.locale),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                InvoiceLocalizationService.getTranslation('hours', controller.locale),
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.right,
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                InvoiceLocalizationService.getTranslation('rate', controller.locale),
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.right,
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                InvoiceLocalizationService.getTranslation('amount', controller.locale),
                style: const TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
      ),
    ));

    // Add time entry items grouped by project
    final projectGroups = <String, List<TimeEntry>>{};
    for (final entry in controller.selectedTimeEntries) {
      projectGroups.putIfAbsent(entry.projectId, () => []).add(entry);
    }

    for (final projectEntry in projectGroups.entries) {
      final projectId = projectEntry.key;
      final entries = projectEntry.value;
      final project = controller.projects[projectId];
      final totalHours = entries.fold<double>(0, (sum, entry) => sum + controller.calculateTimeEntryHours(entry));
      final rate = controller.customRates[projectId] ?? 50.0;
      final amount = totalHours * rate;

      items.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(project?.name ?? 'Unknown Project'),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  totalHours.toStringAsFixed(2),
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  CurrencyService.formatCurrency(rate, controller.currency, Locale(controller.locale)),
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  CurrencyService.formatCurrency(amount, controller.currency, Locale(controller.locale)),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Add additional items
    for (final item in controller.additionalItems) {
      items.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(item.description),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  item.quantity.toStringAsFixed(2),
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  CurrencyService.formatCurrency(item.rate, controller.currency, Locale(controller.locale)),
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  CurrencyService.formatCurrency(item.amount, controller.currency, Locale(controller.locale)),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(children: items);
  }

  Widget _buildTotals() {
    final subtotal = controller.calculateSubtotal();
    final taxAmount = subtotal * (controller.taxRate / 100);
    final total = subtotal + taxAmount;

    return Column(
      children: [
        const Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildTotalRow(
                  '${InvoiceLocalizationService.getTranslation('subtotal', controller.locale)}:',
                  CurrencyService.formatCurrency(subtotal, controller.currency, Locale(controller.locale)),
                ),
                if (controller.taxRate > 0)
                  _buildTotalRow(
                    '${InvoiceLocalizationService.getTranslation('tax', controller.locale)} (${controller.taxRate.toStringAsFixed(1)}%):',
                    CurrencyService.formatCurrency(taxAmount, controller.currency, Locale(controller.locale)),
                  ),
                const SizedBox(height: 8),
                _buildTotalRow(
                  '${InvoiceLocalizationService.getTranslation('total', controller.locale)}:',
                  CurrencyService.formatCurrency(total, controller.currency, Locale(controller.locale)),
                  isTotal: true,
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTotalRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              textAlign: TextAlign.right,
              style: isTotal
                  ? const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)
                  : null,
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 100,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: isTotal
                  ? const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)
                  : null,
            ),
          ),
        ],
      ),
    );
  }
}
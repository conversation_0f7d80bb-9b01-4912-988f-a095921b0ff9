import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_controller.dart';
import 'package:time_tracker_flutter/widgets/client_selector.dart';
import 'package:time_tracker_flutter/widgets/time_entry_selector.dart';
import 'package:time_tracker_flutter/utils/responsive_utils.dart';

/// Step 1: Client and time entries selection
class ClientTimeEntriesStep extends StatelessWidget {
  final InvoiceCreateController controller;

  const ClientTimeEntriesStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Padding(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: isSmallScreen ? 20 : 22,
              ),
              SizedBox(width: isSmallScreen ? 8 : 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Client & Time Entries',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: isSmallScreen ? 18 : 20,
                      ),
                    ),
                    Text(
                      'Select client and time entries for this invoice',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        fontSize: isSmallScreen ? 12 : 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 16 : 24),
          Expanded(
            child: ListView(
              children: [
                _ClientSelectionCard(controller: controller),
                SizedBox(height: isSmallScreen ? 16 : 24),
                _TimeEntriesSelectionCard(controller: controller),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ClientSelectionCard extends StatelessWidget {
  final InvoiceCreateController controller;

  const _ClientSelectionCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Client Selection',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            ClientSelector(
              selectedClient: controller.selectedClient,
              onClientSelected: controller.setSelectedClient,
            ),
          ],
        ),
      ),
    );
  }
}

class _TimeEntriesSelectionCard extends StatelessWidget {
  final InvoiceCreateController controller;

  const _TimeEntriesSelectionCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: isSmallScreen ? 12 : 20,
            offset: Offset(0, isSmallScreen ? 4 : 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 6 : 8),
                Text(
                  'Time Entries Selection',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 16 : 20),
            AnimatedBuilder(
              animation: controller,
              builder: (context, child) {
                double averageRate = 50.0;
                if (controller.selectedTimeEntries.isNotEmpty) {
                  double totalRate = 0.0;
                  int rateCount = 0;
                  for (final entry in controller.selectedTimeEntries) {
                    final rate = controller.customRates[entry.projectId] ?? 50.0;
                    totalRate += rate;
                    rateCount++;
                  }
                  if (rateCount > 0) {
                    averageRate = totalRate / rateCount;
                  }
                }

                return TimeEntrySelector(
                  initialSelectedIds: controller.selectedTimeEntries.map((e) => e.id).toList(),
                  onSelectionChanged: controller.setSelectedTimeEntries,
                  projectFilter: null,
                  currency: controller.currency,
                  locale: controller.locale,
                  defaultHourlyRate: averageRate,
                  onRateChanged: (rate) {
                    for (final entry in controller.selectedTimeEntries) {
                      controller.setCustomRate(entry.projectId, rate);
                    }
                  },
                  currentHourlyRate: averageRate,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

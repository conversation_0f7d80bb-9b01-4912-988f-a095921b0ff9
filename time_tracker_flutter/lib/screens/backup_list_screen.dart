import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:time_tracker_flutter/services/backup_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/screens/main_screen.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class BackupListScreen extends StatefulWidget {
  const BackupListScreen({super.key});

  @override
  State<BackupListScreen> createState() => _BackupListScreenState();
}

class _BackupListScreenState extends State<BackupListScreen> {
  final BackupService _backupService = BackupService();
  final DatabaseService _databaseService = DatabaseService();
  List<String> _backups = [];
  bool _isLoading = true;
  bool _isRestoring = false;
  String? _restoringBackup;

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final backups = await _backupService.listBackups();
      setState(() {
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        showErrorSnackBar('Error loading backups: ${e.toString()}', context);
      }
    }
  }

  Future<void> _restoreBackup(String filename) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Restore Backup',
        content: 'This will replace all existing data. Are you sure you want to continue?',
        confirmText: 'Restore',
      ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isRestoring = true;
      _restoringBackup = filename;
    });

    try {
      await _backupService.restoreBackup(filename);

      if (mounted) {
        showSuccessSnackBar('Backup restored successfully', context);

        // Pop back to settings screen
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error restoring backup: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isRestoring = false;
        _restoringBackup = null;
      });
    }
  }

  Future<void> _deleteBackup(String filename) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const ConfirmDialog(
        title: 'Delete Backup',
        content: 'Are you sure you want to delete this backup?',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed != true) {
      return;
    }

    try {
      await _backupService.deleteBackup(filename);
      _loadBackups();
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error deleting backup: ${e.toString()}', context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backups'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.of(context).pop();
            } else {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const MainScreen()),
                (route) => false,
              );
            }
          },
          tooltip: 'Back',
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _backups.isEmpty
              ? const Center(
                  child: Text('No backups found'),
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Tap the restore icon to restore a backup, or the delete icon to remove it.',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _backups.length,
                        itemBuilder: (context, index) {
                          final filename = _backups[index];
                          final isRestoring = _isRestoring && _restoringBackup == filename;

                          return Slidable(
                            endActionPane: ActionPane(
                              motion: const ScrollMotion(),
                              children: [
                                SlidableAction(
                                  onPressed: (_) => _deleteBackup(filename),
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                  icon: Icons.delete,
                                  label: 'Delete',
                                ),
                              ],
                            ),
                            child: ListTile(
                              title: Text(filename),
                              subtitle: FutureBuilder<DateTime?>(
                                future: _backupService.extractDateFromBackupFilename(filename),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData && snapshot.data != null) {
                                    final locale = LocaleDateUtils.getCurrentLocale(context);
                                    final date = snapshot.data!.toLocal();
                                    return Text(
                                      'Created: ${LocaleDateUtils.formatDate(date, locale)} ${LocaleDateUtils.formatTime(date, locale)}',
                                    );
                                  }
                                  return const Text('Unknown date');
                                },
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isRestoring)
                                    const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  else
                                    IconButton(
                                      icon: const Icon(Icons.restore),
                                      onPressed: () => _restoreBackup(filename),
                                      tooltip: 'Restore',
                                    ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: isRestoring ? null : () => _deleteBackup(filename),
                                    tooltip: 'Delete',
                                  ),
                                ],
                              ),
                              onTap: null,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
    );
  }
}

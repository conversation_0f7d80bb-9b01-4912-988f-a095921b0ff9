import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/error_container.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/section_card.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';

class RecoveryTab extends StatefulWidget {
  final bool isRecovering;
  final String? recoveryError;
  final Function() onShowRecoveryDialog;
  final Function() onShowQRScannerDialog;
  final Function(String) onRecoveryAttempt;

  const RecoveryTab({
    super.key,
    required this.isRecovering,
    this.recoveryError,
    required this.onShowRecoveryDialog,
    required this.onShowQRScannerDialog,
    required this.onRecoveryAttempt,
  });

  @override
  State<RecoveryTab> createState() => _RecoveryTabState();
}

class _RecoveryTabState extends State<RecoveryTab> {
  String _recoveryKeyInput = '';

  @override
  Widget build(BuildContext context) {
    return ConstrainedWidthContainer(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Your Recovery Code section
          SectionCard(
            icon: Icons.key,
            title: 'Your Recovery Code',
            description: 'You\'ll need this code to recover your backups if you lose your device',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                OutlinedButton.icon(
                  onPressed: widget.onShowRecoveryDialog,
                  icon: const Icon(Icons.visibility),
                  label: const Text('View Recovery Code'),
                  style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Recover with Code section
          SectionCard(
            icon: Icons.restore,
            title: 'Recover with Code',
            description: 'Enter your recovery code to restore access to your backups',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextField(
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    hintText: 'XXXX-XXXX-XXXX-XXXX',
                    labelText: 'Recovery Key',
                    helperText: 'Enter the recovery key from your other device',
                    prefixIcon: const Icon(Icons.vpn_key),
                  ),
                  textCapitalization: TextCapitalization.characters,
                  autocorrect: false,
                  enableSuggestions: false,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 16,
                    letterSpacing: 1.0,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _recoveryKeyInput = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: widget.isRecovering || _recoveryKeyInput.isEmpty
                      ? null
                      : () => widget.onRecoveryAttempt(_recoveryKeyInput),
                  icon: widget.isRecovering
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.restore),
                  label: Text(widget.isRecovering ? 'Recovering...' : 'Recover Backups'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                if (widget.recoveryError != null) ...[
                  const SizedBox(height: 16),
                  ErrorContainer(errorMessage: widget.recoveryError!),
                ],
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Scan QR Code section
          SectionCard(
            icon: Icons.qr_code_scanner,
            title: 'Scan QR Code',
            description: 'Scan a QR code from another device to transfer your backups',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  onPressed: widget.isRecovering ? null : widget.onShowQRScannerDialog,
                  icon: widget.isRecovering
                      ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.qr_code_scanner),
                  label: Text(widget.isRecovering ? 'Processing...' : 'Scan QR Code'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
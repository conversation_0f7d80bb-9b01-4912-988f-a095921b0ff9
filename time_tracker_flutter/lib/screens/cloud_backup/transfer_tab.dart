import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/error_container.dart';
import 'package:time_tracker_flutter/screens/cloud_backup/section_card.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';

class TransferTab extends StatelessWidget {
  final bool isRecovering;
  final String? recoveryError;
  final Function() onShowTransferDialog;
  final Function() onShowQRScannerDialog;

  const TransferTab({
    super.key,
    required this.isRecovering,
    this.recoveryError,
    required this.onShowTransferDialog,
    required this.onShowQRScannerDialog,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedWidthContainer(
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Send section
          SectionCard(
            icon: Icons.send,
            title: 'Send to Another Device',
            description: 'Generate a QR code that can be scanned by another device',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                OutlinedButton.icon(
                  onPressed: onShowTransferDialog,
                  icon: const Icon(Icons.qr_code),
                  label: const Text('Show Transfer QR Code'),
                  style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Receive section
          SectionCard(
            icon: Icons.qr_code_scanner,
            title: 'Receive from Another Device',
            description: 'Scan a QR code from another device to receive their backups',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  onPressed: isRecovering ? null : onShowQRScannerDialog,
                  icon: isRecovering
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.qr_code_scanner),
                  label: Text(isRecovering ? 'Processing...' : 'Scan QR Code'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                if (recoveryError != null) ...[
                  const SizedBox(height: 16),
                  ErrorContainer(errorMessage: recoveryError!),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
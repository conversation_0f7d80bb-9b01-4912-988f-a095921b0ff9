import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/utils/constrained_width_container.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

class BackupsTab extends StatelessWidget {
  final DatabaseService databaseService;
  final List<String> backups;
  final bool isLoading;
  final bool isRestoring;
  final bool isDeleting;
  final String? restoringBackup;
  final String? deletingBackup;
  final Function() onCreateBackup;
  final Function(String) onRestoreBackup;
  final Function(String) onDeleteBackup;

  const BackupsTab({
    super.key,
    required this.databaseService,
    required this.backups,
    required this.isLoading,
    required this.isRestoring,
    required this.isDeleting,
    this.restoringBackup,
    this.deletingBackup,
    required this.onCreateBackup,
    required this.onRestoreBackup,
    required this.onDeleteBackup,
  });



  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (backups.isEmpty) {
      return ConstrainedWidthContainer(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_off,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'No cloud backups found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onCreateBackup,
                icon: Icon(Icons.cloud_upload),
                label: Text('Create Backup'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ConstrainedWidthContainer(
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: backups.length,
        itemBuilder: (context, index) {
          final filename = backups[index];
          final isRestoringThis = isRestoring && restoringBackup == filename;
          final isDeletingThis = isDeleting && deletingBackup == filename;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Slidable(
              endActionPane: ActionPane(
                motion: const ScrollMotion(),
                children: [
                  SlidableAction(
                    onPressed: (_) => onDeleteBackup(filename),
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    icon: Icons.delete,
                    label: 'Delete',
                    borderRadius: BorderRadius.circular(12),
                  ),
                ],
              ),
              child: _buildBackupCard(context, filename, isRestoringThis, isDeletingThis),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackupCard(BuildContext context, String filename, bool isRestoringThis, bool isDeletingThis) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.cloud,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        title: FutureBuilder<DateTime?>(
          future: databaseService.extractDateFromCloudBackupFilename(filename),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data != null) {
              final date = snapshot.data!;
              return FutureBuilder<String>(
                future: LocaleDateUtils.formatDateAsync(date, LocaleDateUtils.getCurrentLocale(context)),
                builder: (context, dateSnapshot) {
                  if (dateSnapshot.connectionState == ConnectionState.waiting) {
                    return Text(
                      LocaleDateUtils.formatDate(date, LocaleDateUtils.getCurrentLocale(context)), // Fallback while loading
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  } else if (dateSnapshot.hasError) {
                    return Text(
                      LocaleDateUtils.formatDate(date, LocaleDateUtils.getCurrentLocale(context)), // Fallback on error
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  } else {
                    return Text(
                      dateSnapshot.data!,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  }
                },
              );
            }
            return const Text('Unknown date');
          },
        ),
        subtitle: FutureBuilder<DateTime?>(
          future: databaseService.extractDateFromCloudBackupFilename(filename),
          builder: (context, snapshot) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  snapshot.hasData && snapshot.data != null
                      ? LocaleDateUtils.formatTime(snapshot.data!, LocaleDateUtils.getCurrentLocale(context))
                      : '',
                  style: TextStyle(
                    fontSize: 13,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Backup ID: ${filename.split('_').first}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            );
          },
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isRestoringThis)
              const SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              IconButton(
                icon: Icon(
                  Icons.restore,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: () => onRestoreBackup(filename),
                tooltip: 'Restore',
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primaryContainer.withAlpha(128),
                ),
              ),
            if (isDeletingThis)
              const Padding(
                padding: EdgeInsets.only(left: 8),
                child: SizedBox(
                  width: 32,
                  height: 32,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: isRestoringThis ? null : () => onDeleteBackup(filename),
                  tooltip: 'Delete',
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.red.withAlpha(25),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
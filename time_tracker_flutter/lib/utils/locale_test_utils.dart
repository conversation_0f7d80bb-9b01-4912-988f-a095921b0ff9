import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Utility class for testing locale functionality
class LocaleTestUtils {
  /// Test locale gathering and formatting with various scenarios
  static Map<String, dynamic> testLocaleFormatting(BuildContext context) {
    final now = DateTime.now();
    final currentLocale = LocaleDateUtils.getCurrentLocale(context);
    
    // Test different locales
    final testLocales = [
      const Locale('en', 'US'), // English (US)
      const Locale('en', 'GB'), // English (UK)
      const Locale('de', 'DE'), // German
      const Locale('fr', 'FR'), // French
      const Locale('es', 'ES'), // Spanish
      const Locale('ja', 'JP'), // Japanese
      const Locale('zh', 'CN'), // Chinese
    ];

    final results = <String, dynamic>{
      'currentLocale': currentLocale.toString(),
      'currentLocaleLanguage': currentLocale.languageCode,
      'currentLocaleCountry': currentLocale.countryCode,
      'systemLocale': Localizations.localeOf(context).toString(),
      'testDate': now.toIso8601String(),
      'formattingResults': <String, Map<String, String>>{},
    };

    // Test formatting with current locale
    results['currentLocaleFormatting'] = {
      'date': LocaleDateUtils.formatDate(now, currentLocale),
      'shortDate': LocaleDateUtils.formatShortDate(now, currentLocale),
      'time': LocaleDateUtils.formatTime(now, currentLocale),
      'weekday': LocaleDateUtils.formatWeekday(now, currentLocale),
      'month': LocaleDateUtils.formatMonth(now, currentLocale),
      'monthYear': LocaleDateUtils.formatMonthYear(now, currentLocale),
      'uses24Hour': LocaleDateUtils.uses24HourTime(currentLocale).toString(),
    };

    // Test formatting with different locales
    for (final locale in testLocales) {
      results['formattingResults'][locale.toString()] = {
        'date': LocaleDateUtils.formatDate(now, locale),
        'shortDate': LocaleDateUtils.formatShortDate(now, locale),
        'time': LocaleDateUtils.formatTime(now, locale),
        'weekday': LocaleDateUtils.formatWeekday(now, locale),
        'month': LocaleDateUtils.formatMonth(now, locale),
        'monthYear': LocaleDateUtils.formatMonthYear(now, locale),
        'uses24Hour': LocaleDateUtils.uses24HourTime(locale).toString(),
      };
    }

    // Test date range formatting
    final startDate = now.subtract(const Duration(days: 30));
    final endDate = now;
    
    results['dateRangeFormatting'] = {
      'current': LocaleDateUtils.formatDateRange(startDate, endDate, currentLocale),
      'en_US': LocaleDateUtils.formatDateRange(startDate, endDate, const Locale('en', 'US')),
      'de_DE': LocaleDateUtils.formatDateRange(startDate, endDate, const Locale('de', 'DE')),
      'fr_FR': LocaleDateUtils.formatDateRange(startDate, endDate, const Locale('fr', 'FR')),
    };

    return results;
  }

  /// Verify that locale-aware formatting is working correctly
  static bool verifyLocaleFormatting(BuildContext context) {
    try {
      final now = DateTime.now();
      final currentLocale = LocaleDateUtils.getCurrentLocale(context);
      
      // Test basic formatting functions
      final date = LocaleDateUtils.formatDate(now, currentLocale);
      final time = LocaleDateUtils.formatTime(now, currentLocale);
      final weekday = LocaleDateUtils.formatWeekday(now, currentLocale);
      
      // Verify that results are not empty and contain expected patterns
      if (date.isEmpty || time.isEmpty || weekday.isEmpty) {
        return false;
      }
      
      // Test that different locales produce different results
      final usDate = LocaleDateUtils.formatDate(now, const Locale('en', 'US'));
      final deDate = LocaleDateUtils.formatDate(now, const Locale('de', 'DE'));
      
      // For most cases, different locales should produce different formats
      // (though some might be similar, so we just check they're not null)
      if (usDate.isEmpty || deDate.isEmpty) {
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('Locale formatting verification failed: $e');
      return false;
    }
  }

  /// Get a summary of locale support status
  static Map<String, dynamic> getLocaleSupportSummary(BuildContext context) {
    final currentLocale = LocaleDateUtils.getCurrentLocale(context);
    final supportedLocales = [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi'
    ];
    
    return {
      'currentLocale': currentLocale.toString(),
      'isSupported': supportedLocales.contains(currentLocale.languageCode),
      'supportedLanguages': supportedLocales,
      'uses24HourTime': LocaleDateUtils.uses24HourTime(currentLocale),
      'localizationWorking': verifyLocaleFormatting(context),
    };
  }

  /// Print locale test results to debug console
  static void printLocaleTestResults(BuildContext context) {
    final results = testLocaleFormatting(context);
    final summary = getLocaleSupportSummary(context);
    
    debugPrint('=== LOCALE TEST RESULTS ===');
    debugPrint('Current Locale: ${results['currentLocale']}');
    debugPrint('System Locale: ${results['systemLocale']}');
    debugPrint('Localization Working: ${summary['localizationWorking']}');
    debugPrint('Uses 24-Hour Time: ${summary['uses24HourTime']}');
    debugPrint('');
    
    debugPrint('Current Locale Formatting:');
    final currentFormatting = results['currentLocaleFormatting'] as Map<String, String>;
    currentFormatting.forEach((key, value) {
      debugPrint('  $key: $value');
    });
    debugPrint('');
    
    debugPrint('Date Range Formatting:');
    final dateRangeFormatting = results['dateRangeFormatting'] as Map<String, String>;
    dateRangeFormatting.forEach((key, value) {
      debugPrint('  $key: $value');
    });
    debugPrint('');
    
    debugPrint('Different Locale Examples:');
    final formattingResults = results['formattingResults'] as Map<String, Map<String, String>>;
    formattingResults.forEach((locale, formats) {
      debugPrint('  $locale:');
      debugPrint('    Date: ${formats['date']}');
      debugPrint('    Time: ${formats['time']}');
      debugPrint('    24h: ${formats['uses24Hour']}');
    });
    debugPrint('=== END LOCALE TEST ===');
  }
} 
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  Timer? _memoryCheckTimer;
  final List<double> _memoryUsageHistory = [];
  static const int _maxHistoryLength = 100;

  void startMonitoring() {
    if (kDebugMode) {
      _memoryCheckTimer = Timer.periodic(const Duration(minutes: 1), (_) => _checkMemoryUsage());
      debugPrint('Performance monitoring started');
    }
  }

  void stopMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
    if (kDebugMode) {
      debugPrint('Performance monitoring stopped');
    }
  }

  void _checkMemoryUsage() async {
    try {
      // Get memory usage info
      final info = await ServicesBinding.instance.defaultBinaryMessenger.send(
        'flutter/platform',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('SystemChrome.getDeviceInfo')
        ),
      );
      
      // This is a simplified memory check - in a real app you might use 
      // platform-specific implementations to get actual memory usage
      if (kDebugMode) {
        debugPrint('Memory check performed at ${DateTime.now()}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking memory usage: $e');
      }
    }
  }

  void logLocationEvent(String event) {
    if (kDebugMode) {
      debugPrint('Location Event: $event at ${DateTime.now()}');
    }
  }

  void logBatteryOptimization(String action) {
    if (kDebugMode) {
      debugPrint('Battery Optimization: $action at ${DateTime.now()}');
    }
  }

  // Performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'memoryHistoryLength': _memoryUsageHistory.length,
      'averageMemoryUsage': _memoryUsageHistory.isNotEmpty 
          ? _memoryUsageHistory.reduce((a, b) => a + b) / _memoryUsageHistory.length 
          : 0,
      'isMonitoring': _memoryCheckTimer?.isActive ?? false,
    };
  }

  void dispose() {
    stopMonitoring();
    _memoryUsageHistory.clear();
  }
} 
import 'dart:convert';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';

class CryptoUtils {
  // Prefix to identify Web Crypto API encrypted data
  static const String webCryptoPrefix = 'WC:';

  // Check if data was encrypted with Web Crypto API
  static bool isWebCryptoData(String encryptedData) {
    return encryptedData.startsWith(webCryptoPrefix);
  }

  // Generate a SHA-256 hash
  static String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Encrypt data using Web-Crypto-compatible AES-GCM (fallback to CBC/JSON)
  static Future<String> encryptData(String data, String key) async {
    // Try AES-GCM first
    try {
      final keyBytes = sha256.convert(utf8.encode(key)).bytes;
      final encryptKey = encrypt.Key(Uint8List.fromList(keyBytes));
      final iv = encrypt.IV.fromSecureRandom(12);
      final encrypter = encrypt.Encrypter(encrypt.AES(encryptKey, mode: encrypt.AESMode.gcm));
      final encrypted = encrypter.encrypt(data, iv: iv);

      final combined = Uint8List(iv.bytes.length + encrypted.bytes.length);
      combined.setRange(0, iv.bytes.length, iv.bytes);
      combined.setRange(iv.bytes.length, combined.length, encrypted.bytes);

      return webCryptoPrefix + base64.encode(combined);
    } catch (e) {
      debugPrint('GCM encryption failed: $e, falling back to CBC/JSON');
    }

    // Fallback to the existing CBC + JSON wrapper
    try {
      final keyBytes = sha256.convert(utf8.encode(key)).bytes;
      final encryptKey = encrypt.Key(Uint8List.fromList(keyBytes));
      final iv = encrypt.IV.fromSecureRandom(16);
      final encrypter = encrypt.Encrypter(encrypt.AES(encryptKey, mode: encrypt.AESMode.cbc));
      final encrypted = encrypter.encrypt(data, iv: iv);

      final combined = {
        'iv': base64.encode(iv.bytes),
        'data': encrypted.base64,
        'keyHash': generateHash(key).substring(0, 16),
      };

      return jsonEncode(combined);
    } catch (e) {
      debugPrint('Encryption error: $e');
      rethrow;
    }
  }

  // Decrypt data
  static Future<String> decryptData(String encryptedData, String key) async {
    try {
      if (isWebCryptoData(encryptedData)) {
        return _decryptWebCrypto(encryptedData, key);
      } else {
        throw Exception('Invalid encrypted data format: Only Web Crypto API encrypted data is supported.');
      }
    } catch (e) {
      debugPrint('Decryption error: $e');
      rethrow;
    }
  }

  // Decrypt data that was encrypted with Web Crypto API (from original app)
  static Future<String> _decryptWebCrypto(String encryptedData, String key) async {
    try {
      // Remove the prefix
      final data = encryptedData.substring(webCryptoPrefix.length);

      // Decode the base64 data
      final bytes = base64.decode(data);

      // Extract IV (first 12 bytes) and encrypted data
      final iv = bytes.sublist(0, 12);
      final encryptedBytes = bytes.sublist(12);

      // Derive key from the provided key
      final keyBytes = sha256.convert(utf8.encode(key)).bytes;
      final encryptKey = encrypt.Key(Uint8List.fromList(keyBytes));
      final ivObj = encrypt.IV(Uint8List.fromList(iv));

      debugPrint('Decrypting Web Crypto data with key length: ${keyBytes.length}');
      debugPrint('IV length: ${iv.length}, encrypted data length: ${encryptedBytes.length}');

      // Try AES-GCM mode (Web Crypto compatible)
      try {
        final encrypter = encrypt.Encrypter(encrypt.AES(encryptKey, mode: encrypt.AESMode.gcm));
        final decrypted = encrypter.decrypt(
          encrypt.Encrypted(Uint8List.fromList(encryptedBytes)),
          iv: ivObj,
        );

        // Simple check if decryption likely succeeded (can be improved)
        // We don't strictly require JSON here, just that decryption ran.
        debugPrint('Successfully decrypted with GCM mode (data length: ${decrypted.length})');
        return decrypted;

      } catch (e) {
        // If GCM fails, it's likely the key is wrong or data is corrupt.
        debugPrint('GCM decryption failed: $e');
        throw Exception('Failed to decrypt Web Crypto data (AES-GCM): $e');
      }

    } catch (e) {
      debugPrint('Web Crypto decryption error: $e');
      throw Exception('Error decrypting Web Crypto data: $e');
    }
  }
}

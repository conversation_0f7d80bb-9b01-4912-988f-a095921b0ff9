import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/analysis_models.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

/// Utility class for date formatting in the analysis screens
class DateFormatUtils {
  /// Format a date for chart x-axis labels based on the analysis period and screen constraints
  ///
  /// Adaptively formats dates based on period, data density, and available space
  static String formatChartDate(
    DateTime date,
    AnalysisPeriod period, {
    bool isMonthlyData = false,
    bool isCompactView = false,
    DateTimeRange? fullDateRange,
    int? dataPointIndex,
    int? totalDataPoints,
    Locale? locale,
  }) {
    // For very compact views (small mobile screens)
    if (isCompactView) {
      return _getCompactLabel(date, period, isMonthlyData, locale);
    }

    // For multi-year data
    if (_isMultiYearData(fullDateRange)) {
      return _getMultiYearLabel(date, dataPointIndex, totalDataPoints, locale);
    }

    // For monthly data
    if (period == AnalysisPeriod.allTime || isMonthlyData) {
      return _getMonthlyLabel(date, dataPointIndex, totalDataPoints, locale);
    }

    // For weekly data
    if (period == AnalysisPeriod.week) {
      return LocaleDateUtils.getWeekdayFormat(locale).format(date);
    }

    // For daily data (default)
    return _getDailyLabel(date, fullDateRange, dataPointIndex, totalDataPoints, locale);
  }

  /// Context-aware version of formatChartDate with enhanced locale support
  static String formatChartDateWithContext(
    DateTime date,
    AnalysisPeriod period,
    BuildContext context, {
    bool isMonthlyData = false,
    bool isCompactView = false,
    DateTimeRange? fullDateRange,
    int? dataPointIndex,
    int? totalDataPoints,
  }) {
    final locale = LocaleDateUtils.getCurrentLocale(context);

    // Enhanced formatting with better locale support
    try {
      // For very compact views (small mobile screens)
      if (isCompactView) {
        return _getCompactLabelWithLocale(date, period, isMonthlyData, locale);
      }

      // For multi-year data
      if (_isMultiYearData(fullDateRange)) {
        return _getMultiYearLabelWithLocale(date, dataPointIndex, totalDataPoints, locale);
      }

      // For monthly data
      if (period == AnalysisPeriod.allTime || isMonthlyData) {
        return _getMonthlyLabelWithLocale(date, dataPointIndex, totalDataPoints, locale);
      }

      // For weekly data
      if (period == AnalysisPeriod.week) {
        return LocaleDateUtils.getWeekdayFormat(locale).format(date);
      }

      // For daily data (default)
      return _getDailyLabelWithLocale(date, fullDateRange, dataPointIndex, totalDataPoints, locale);
    } catch (e) {
      // Fallback to original method
      return formatChartDate(
        date,
        period,
        isMonthlyData: isMonthlyData,
        isCompactView: isCompactView,
        fullDateRange: fullDateRange,
        dataPointIndex: dataPointIndex,
        totalDataPoints: totalDataPoints,
        locale: locale,
      );
    }
  }

  /// Get a compact label for small screens
  static String _getCompactLabel(DateTime date, AnalysisPeriod period, bool isMonthlyData, [Locale? locale]) {
    if (period == AnalysisPeriod.allTime || isMonthlyData) {
      // For monthly data in compact view, show locale-aware month/year
      final monthFormat = LocaleDateUtils.getMonthFormat(locale);
      return '${monthFormat.format(date)}/${date.year % 100}';
    } else if (period == AnalysisPeriod.week) {
      // For weekly data in compact view, show day of week initial
      return LocaleDateUtils.getWeekdayFormat(locale).format(date)[0];
    } else {
      // For daily data in compact view, use locale-aware compact date formatting
      return LocaleDateUtils.formatCompactDate(date, locale);
    }
  }

  /// Get a compact label for small screens with enhanced locale support
  static String _getCompactLabelWithLocale(DateTime date, AnalysisPeriod period, bool isMonthlyData, Locale locale) {
    return _getCompactLabel(date, period, isMonthlyData, locale);
  }

  /// Get a label for multi-year data with enhanced locale support
  static String _getMultiYearLabelWithLocale(DateTime date, int? dataPointIndex, int? totalDataPoints, Locale locale) {
    return _getMultiYearLabel(date, dataPointIndex, totalDataPoints, locale);
  }

  /// Get a label for monthly data with enhanced locale support
  static String _getMonthlyLabelWithLocale(DateTime date, int? dataPointIndex, int? totalDataPoints, Locale locale) {
    return _getMonthlyLabel(date, dataPointIndex, totalDataPoints, locale);
  }

  /// Get a label for daily data with enhanced locale support
  static String _getDailyLabelWithLocale(DateTime date, DateTimeRange? fullDateRange, int? dataPointIndex, int? totalDataPoints, Locale locale) {
    return _getDailyLabel(date, fullDateRange, dataPointIndex, totalDataPoints, locale);
  }

  /// Get a label for multi-year data
  static String _getMultiYearLabel(DateTime date, int? dataPointIndex, int? totalDataPoints, [Locale? locale]) {
    final isFirstOrLast = (dataPointIndex == 0) || (dataPointIndex != null && totalDataPoints != null && dataPointIndex == totalDataPoints - 1);

    if (isFirstOrLast) {
      // Show year for first/last points
      return LocaleDateUtils.getMonthYearFormat(locale).format(date);
    }
    // Show just month for intermediate points
    return LocaleDateUtils.getMonthFormat(locale).format(date);
  }

  /// Get a label for monthly data
  static String _getMonthlyLabel(DateTime date, int? dataPointIndex, int? totalDataPoints, [Locale? locale]) {
    final isFirstOrLast = (dataPointIndex == 0) || (dataPointIndex != null && totalDataPoints != null && dataPointIndex == totalDataPoints - 1);

    if (isFirstOrLast) {
      // Show month and abbreviated year for first/last points
      return LocaleDateUtils.getMonthYearFormat(locale).format(date);
    }
    // Show just month for intermediate points
    return LocaleDateUtils.getMonthFormat(locale).format(date);
  }

  /// Get a label for daily data
  static String _getDailyLabel(DateTime date, DateTimeRange? fullDateRange, int? dataPointIndex, int? totalDataPoints, [Locale? locale]) {
    final isFirstOrLast = (dataPointIndex == 0) || (dataPointIndex != null && totalDataPoints != null && dataPointIndex == totalDataPoints - 1);
    final isMonthStart = date.day == 1;

    // If we're showing data that spans multiple months
    if (fullDateRange != null && _isMultiMonthData(fullDateRange)) {
      if (isMonthStart || isFirstOrLast) {
        // For first/last points in different years, include year
        if (isFirstOrLast && fullDateRange.start.year != fullDateRange.end.year) {
          return LocaleDateUtils.formatCompactDate(date, locale);
        }
        // Use proper locale-aware date formatting instead of hardcoded month-day pattern
        return LocaleDateUtils.formatDate(date, locale);
      }
      // Show day with month for better context using proper locale formatting
      return LocaleDateUtils.formatDate(date, locale);
    }

    // For data within the same month, show day with weekday initial
    return '${LocaleDateUtils.getWeekdayFormat(locale).format(date)[0]}, ${date.day}';
  }

  /// Check if the date range spans multiple years
  static bool _isMultiYearData(DateTimeRange? dateRange) {
    if (dateRange == null) return false;
    return dateRange.end.difference(dateRange.start).inDays > 365;
  }

  /// Check if the date range spans multiple months
  static bool _isMultiMonthData(DateTimeRange dateRange) {
    return dateRange.end.month != dateRange.start.month ||
           dateRange.end.year != dateRange.start.year;
  }

  /// Format a date range for display
  static String formatDateRange(DateTime start, DateTime end, AnalysisPeriod period, [Locale? locale]) {
    return LocaleDateUtils.formatDateRange(start, end, locale);
  }

  /// Format a date range for display with custom format support
  static Future<String> formatDateRangeAsync(DateTime start, DateTime end, AnalysisPeriod period, [Locale? locale]) async {
    return LocaleDateUtils.formatDateRangeAsync(start, end, locale);
  }

  /// Context-aware version of formatDateRange with enhanced locale support
  static String formatDateRangeWithContext(DateTime start, DateTime end, AnalysisPeriod period, BuildContext context) {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    // Use the enhanced LocaleDateUtils method that respects locale better
    return LocaleDateUtils.formatDateRange(start, end, locale);
  }

  /// Context-aware version of formatDateRange with custom format support
  static Future<String> formatDateRangeWithContextAsync(DateTime start, DateTime end, AnalysisPeriod period, BuildContext context) async {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    return formatDateRangeAsync(start, end, period, locale);
  }

  /// Get a human-readable description of the date range
  static String getDateRangeDescription(DateTimeRange dateRange, [Locale? locale]) {
    final days = dateRange.end.difference(dateRange.start).inDays;
    final start = dateRange.start;
    final end = dateRange.end;

    if (days > 365) {
      final years = (days / 365).floor();
      final startStr = LocaleDateUtils.getMonthYearFormat(locale).format(start);
      final endStr = LocaleDateUtils.getMonthYearFormat(locale).format(end);
      return '$years ${years == 1 ? 'year' : 'years'} ($startStr - $endStr)';
    } else if (days > 30) {
      final months = (days / 30).floor();
      // Use proper locale-aware date formatting instead of hardcoded month-day pattern
      final startStr = LocaleDateUtils.formatDate(start, locale);
      final endStr = LocaleDateUtils.formatDate(end, locale);
      return '$months ${months == 1 ? 'month' : 'months'} ($startStr - $endStr)';
    } else {
      // Use proper locale-aware date formatting instead of hardcoded month-day pattern
      final startStr = LocaleDateUtils.formatDate(start, locale);
      final endStr = LocaleDateUtils.formatDate(end, locale);
      return '$days ${days == 1 ? 'day' : 'days'} ($startStr - $endStr)';
    }
  }

  /// Context-aware version of getDateRangeDescription
  static String getDateRangeDescriptionWithContext(DateTimeRange dateRange, BuildContext context) {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    return getDateRangeDescription(dateRange, locale);
  }

  /// Determine the appropriate data aggregation level based on date range
  static DataAggregationLevel determineAggregationLevel(DateTimeRange dateRange) {
    final days = dateRange.end.difference(dateRange.start).inDays;

    if (days > 730) { // More than 2 years
      return DataAggregationLevel.quarter;
    } else if (days > 365) { // More than 1 year
      return DataAggregationLevel.month;
    } else if (days > 60) { // More than 2 months
      return DataAggregationLevel.week;
    } else if (days > 14) { // More than 2 weeks
      return DataAggregationLevel.twoDays;
    } else {
      return DataAggregationLevel.day;
    }
  }

  /// Determine if the data should be displayed as monthly data
  ///
  /// This is used to decide whether to show only month names in charts
  static bool shouldShowMonthlyData(DateTimeRange dateRange) {
    // If the date range is more than 60 days, show monthly data
    final days = dateRange.end.difference(dateRange.start).inDays;
    return days > 60;
  }

  /// Format a date for tooltip display
  static String formatTooltipDate(DateTime date, AnalysisPeriod period, {bool isMonthlyData = false, Locale? locale}) {
    if (period == AnalysisPeriod.allTime || isMonthlyData) {
      // For "All Time" period, show month and year
      return LocaleDateUtils.getFullMonthYearFormat(locale).format(date);
    } else {
      // For other periods, show weekday, month, day and year
      return '${LocaleDateUtils.getWeekdayFormat(locale).format(date)}, ${LocaleDateUtils.formatDate(date, locale)}';
    }
  }

  /// Format a date for tooltip display with custom format support
  static Future<String> formatTooltipDateAsync(DateTime date, AnalysisPeriod period, {bool isMonthlyData = false, Locale? locale}) async {
    if (period == AnalysisPeriod.allTime || isMonthlyData) {
      // For "All Time" period, show month and year
      return LocaleDateUtils.getFullMonthYearFormat(locale).format(date);
    } else {
      // For other periods, show weekday, month, day and year
      final formattedDate = await LocaleDateUtils.formatDateAsync(date, locale);
      return '${LocaleDateUtils.getWeekdayFormat(locale).format(date)}, $formattedDate';
    }
  }

  /// Context-aware version of formatTooltipDate
  static String formatTooltipDateWithContext(DateTime date, AnalysisPeriod period, BuildContext context, {bool isMonthlyData = false}) {
    final locale = LocaleDateUtils.getCurrentLocale(context);

    // Try to use custom date formatting if available, with fallback to standard formatting
    if (period == AnalysisPeriod.allTime || isMonthlyData) {
      // For "All Time" period, show month and year
      return LocaleDateUtils.getFullMonthYearFormat(locale).format(date);
    } else {
      // For other periods, show weekday, month, day and year with better locale support
      try {
        // Use the synchronous version but with better locale awareness
        final weekday = LocaleDateUtils.getWeekdayFormat(locale).format(date);
        final dateStr = LocaleDateUtils.formatDate(date, locale);
        return '$weekday, $dateStr';
      } catch (e) {
        // Fallback to basic formatting
        return formatTooltipDate(date, period, isMonthlyData: isMonthlyData, locale: locale);
      }
    }
  }

  /// Context-aware version of formatTooltipDate with custom format support
  static Future<String> formatTooltipDateWithContextAsync(DateTime date, AnalysisPeriod period, BuildContext context, {bool isMonthlyData = false}) async {
    final locale = LocaleDateUtils.getCurrentLocale(context);
    return formatTooltipDateAsync(date, period, isMonthlyData: isMonthlyData, locale: locale);
  }

  /// Calculate optimal interval for x-axis labels based on available width and data points
  static int calculateOptimalLabelInterval(int dataPointCount, double availableWidth) {
    // Estimate minimum width needed per label (in logical pixels)
    const minWidthPerLabel = 40.0;

    // Calculate how many labels can fit in the available width
    final maxLabels = (availableWidth / minWidthPerLabel).floor();

    // Ensure we show at least 2 labels and at most the number of data points
    final targetLabelCount = maxLabels.clamp(2, dataPointCount);

    // Calculate interval (how many data points to skip between labels)
    int interval = (dataPointCount / targetLabelCount).ceil();

    // Ensure interval is at least 1
    return interval < 1 ? 1 : interval;
  }
}

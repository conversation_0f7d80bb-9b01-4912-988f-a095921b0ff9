import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/widgets/custom_snackbar.dart';
/// Shows a modern styled snackbar with improved UI
void _showModernSnackBar({
  required BuildContext context,
  required String message,
  bool isError = false,
  Duration duration = const Duration(seconds: 4),
  Widget? action,
  IconData? icon,
}) {
  // Use our custom snackbar overlay instead of the default SnackBar
  SnackbarOverlay.instance.show(
    context: context,
    message: message,
    isError: isError,
    duration: duration,
    action: action,
    icon: icon,
  );
}


void showErrorSnackBar(String message, BuildContext context) {
  _showModernSnackBar(
    context: context,
    message: message,
    isError: true,
    icon: Icons.warning_amber_rounded,
  );
}

void showSuccessSnackBar(String message, BuildContext context) {
  _showModernSnackBar(
    context: context,
    message: message,
    icon: Icons.check_circle_outline,
  );
}

void showInfoSnackBar(String message, BuildContext context) {
  _showModernSnackBar(
    context: context,
    message: message,
    icon: Icons.info_outline,
  );
}


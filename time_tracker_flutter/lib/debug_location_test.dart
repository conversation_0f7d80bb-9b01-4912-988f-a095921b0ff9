import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'services/location/location_utils.dart';

class DebugLocationTest {
  static void runTests() {
    _log('=== Running Location Debug Tests ===');

    _testLocationUtils();
    _testDistanceCalculation();
    _testCooldownLogic();

    _log('=== Debug Tests Complete ===');
  }

  static void _testLocationUtils() {
    _log('Testing LocationUtils...');

    final duration1 = Duration(hours: 1, minutes: 30, seconds: 45);
    final formatted1 = LocationUtils.formatDuration(duration1);
    _log('Duration 1h 30m 45s formatted as: $formatted1');

    final startTime = DateTime.now().subtract(Duration(minutes: 15, seconds: 30));
    final trackingDuration = LocationUtils.formatTrackingDuration(startTime);
    _log('Tracking duration for 15m 30s ago: $trackingDuration');

    final now = DateTime.now();
    final dateStr = LocationUtils.formatDate(now);
    final timeStr = LocationUtils.formatTime(now);
    _log('Current date: $dateStr, time: $timeStr');
  }

  static void _testDistanceCalculation() {
    _log('Testing distance calculation...');

    final location1 = LatLng(47.42825518777445, 11.885145528701255);
    final location2 = LatLng(47.42915518777445, 11.885145528701255);

    final distance = LocationUtils.calculateDistance(location1, location2);
    _log('Distance between test points: ${distance.toStringAsFixed(1)}m');
  }

  static void _testCooldownLogic() {
    _log('Testing cooldown logic...');

    const areaId = 'test-area-id';
    const cooldownMinutes = 5;
    final lastExitTimes = <String, DateTime>{};

    final testCases = [
      ('no previous exit', lastExitTimes),
      ('recent exit (2m ago)', {areaId: DateTime.now().subtract(Duration(minutes: 2))}),
      ('old exit (10m ago)', {areaId: DateTime.now().subtract(Duration(minutes: 10))}),
    ];

    for (final (description, exitTimes) in testCases) {
      final inCooldown = LocationUtils.isInCooldown(areaId, cooldownMinutes, exitTimes);
      _log('Cooldown with $description: $inCooldown');
    }
  }

  static void _log(String message) {
    debugPrint(message);
  }
}
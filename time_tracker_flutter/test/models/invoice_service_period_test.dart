import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';

void main() {
  group('Invoice Service Period', () {
    test('should create invoice with service period fields', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );

      expect(invoice.servicePeriodStart, equals(startDate));
      expect(invoice.servicePeriodEnd, equals(endDate));
    });

    test('should create invoice without service period fields', () {
      final invoice = Invoice(
        invoiceNumber: 'INV-002',
        clientId: 'client-1',
      );

      expect(invoice.servicePeriodStart, isNull);
      expect(invoice.servicePeriodEnd, isNull);
    });

    test('should validate service period correctly', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      // Valid service period
      final validInvoice = Invoice(
        invoiceNumber: 'INV-003',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );
      expect(validInvoice.isServicePeriodValid(), isTrue);

      // Invalid service period (end before start)
      final invalidInvoice = Invoice(
        invoiceNumber: 'INV-004',
        clientId: 'client-1',
        servicePeriodStart: endDate,
        servicePeriodEnd: startDate,
      );
      expect(invalidInvoice.isServicePeriodValid(), isFalse);

      // Same date should be valid
      final sameDateInvoice = Invoice(
        invoiceNumber: 'INV-005',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: startDate,
      );
      expect(sameDateInvoice.isServicePeriodValid(), isTrue);

      // No service period should be valid
      final noServicePeriodInvoice = Invoice(
        invoiceNumber: 'INV-006',
        clientId: 'client-1',
      );
      expect(noServicePeriodInvoice.isServicePeriodValid(), isTrue);

      // Only start date should be invalid
      final onlyStartInvoice = Invoice(
        invoiceNumber: 'INV-007',
        clientId: 'client-1',
        servicePeriodStart: startDate,
      );
      expect(onlyStartInvoice.isServicePeriodValid(), isFalse);

      // Only end date should be invalid
      final onlyEndInvoice = Invoice(
        invoiceNumber: 'INV-008',
        clientId: 'client-1',
        servicePeriodEnd: endDate,
      );
      expect(onlyEndInvoice.isServicePeriodValid(), isFalse);
    });

    test('should serialize and deserialize service period fields', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      final originalInvoice = Invoice(
        invoiceNumber: 'INV-009',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );

      // Test JSON serialization
      final json = originalInvoice.toJson();
      expect(json['servicePeriodStart'], equals(startDate.toIso8601String()));
      expect(json['servicePeriodEnd'], equals(endDate.toIso8601String()));

      // Test JSON deserialization
      final deserializedInvoice = Invoice.fromJson(json);
      expect(deserializedInvoice.servicePeriodStart, equals(startDate));
      expect(deserializedInvoice.servicePeriodEnd, equals(endDate));
    });

    test('should handle null service period in JSON serialization', () {
      final invoice = Invoice(
        invoiceNumber: 'INV-010',
        clientId: 'client-1',
      );

      final json = invoice.toJson();
      expect(json['servicePeriodStart'], isNull);
      expect(json['servicePeriodEnd'], isNull);

      final deserializedInvoice = Invoice.fromJson(json);
      expect(deserializedInvoice.servicePeriodStart, isNull);
      expect(deserializedInvoice.servicePeriodEnd, isNull);
    });

    test('should copy invoice with service period fields', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final newStartDate = DateTime(2024, 2, 1);
      final newEndDate = DateTime(2024, 2, 28);
      
      final originalInvoice = Invoice(
        invoiceNumber: 'INV-011',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );

      // Copy with new service period
      final copiedInvoice = originalInvoice.copyWith(
        servicePeriodStart: newStartDate,
        servicePeriodEnd: newEndDate,
      );

      expect(copiedInvoice.servicePeriodStart, equals(newStartDate));
      expect(copiedInvoice.servicePeriodEnd, equals(newEndDate));
      expect(copiedInvoice.invoiceNumber, equals(originalInvoice.invoiceNumber));
      expect(copiedInvoice.clientId, equals(originalInvoice.clientId));

      // Copy without changing service period
      final unchangedCopy = originalInvoice.copyWith(
        invoiceNumber: 'INV-012',
      );

      expect(unchangedCopy.servicePeriodStart, equals(startDate));
      expect(unchangedCopy.servicePeriodEnd, equals(endDate));
      expect(unchangedCopy.invoiceNumber, equals('INV-012'));
    });
  });
}
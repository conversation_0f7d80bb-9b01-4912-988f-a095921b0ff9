import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';

void main() {
  group('Address', () {
    test('should create valid address with required fields', () {
      final address = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      expect(address.street, '123 Main St');
      expect(address.city, 'New York');
      expect(address.postalCode, '10001');
      expect(address.country, 'USA');
      expect(address.street2, isNull);
      expect(address.state, isNull);
    });

    test('should create valid address with all fields', () {
      final address = Address(
        street: '123 Main St',
        street2: 'Apt 4B',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'USA',
      );

      expect(address.street2, 'Apt 4B');
      expect(address.state, 'NY');
    });

    test('should format address correctly', () {
      final address = Address(
        street: '123 Main St',
        street2: 'Apt 4B',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'USA',
      );

      expect(address.formattedAddress, '123 Main St, Apt 4B, New York, NY, 10001, USA');
    });

    test('should format address without optional fields', () {
      final address = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      expect(address.formattedAddress, '123 Main St, New York, 10001, USA');
    });

    test('should validate correctly', () {
      final validAddress = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );
      expect(validAddress.isValid(), isTrue);

      final invalidAddress1 = Address(
        street: '',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );
      expect(invalidAddress1.isValid(), isFalse);

      final invalidAddress2 = Address(
        street: '123 Main St',
        city: '',
        postalCode: '10001',
        country: 'USA',
      );
      expect(invalidAddress2.isValid(), isFalse);

      final invalidAddress3 = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '',
        country: 'USA',
      );
      expect(invalidAddress3.isValid(), isFalse);

      final invalidAddress4 = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: '',
      );
      expect(invalidAddress4.isValid(), isFalse);
    });

    test('should serialize to/from JSON correctly', () {
      final address = Address(
        street: '123 Main St',
        street2: 'Apt 4B',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'USA',
      );

      final json = address.toJson();
      final fromJson = Address.fromJson(json);

      expect(fromJson.street, address.street);
      expect(fromJson.street2, address.street2);
      expect(fromJson.city, address.city);
      expect(fromJson.state, address.state);
      expect(fromJson.postalCode, address.postalCode);
      expect(fromJson.country, address.country);
    });

    test('should handle copyWith correctly', () {
      final original = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final updated = original.copyWith(
        street2: 'Apt 4B',
        state: 'NY',
      );

      expect(updated.street, original.street);
      expect(updated.street2, 'Apt 4B');
      expect(updated.city, original.city);
      expect(updated.state, 'NY');
      expect(updated.postalCode, original.postalCode);
      expect(updated.country, original.country);
    });

    test('should handle equality correctly', () {
      final address1 = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final address2 = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final address3 = Address(
        street: '456 Oak Ave',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      expect(address1, equals(address2));
      expect(address1, isNot(equals(address3)));
      expect(address1.hashCode, equals(address2.hashCode));
    });
  });

  group('Client', () {
    test('should create client with required fields', () {
      final client = Client(name: 'John Doe');

      expect(client.id, isNotEmpty);
      expect(client.name, 'John Doe');
      expect(client.createdAt, isNotEmpty);
      expect(client.email, isNull);
      expect(client.phone, isNull);
      expect(client.address, isNull);
      expect(client.taxId, isNull);
      expect(client.notes, isNull);
      expect(client.updatedAt, isNull);
    });

    test('should create client with all fields', () {
      final address = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final client = Client(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        address: address,
        taxId: 'TAX123456',
        notes: 'Important client',
      );

      expect(client.name, 'John Doe');
      expect(client.email, '<EMAIL>');
      expect(client.phone, '******-0123');
      expect(client.address, address);
      expect(client.taxId, 'TAX123456');
      expect(client.notes, 'Important client');
    });

    test('should validate correctly', () {
      final validClient = Client(
        name: 'John Doe',
        email: '<EMAIL>',
      );
      expect(validClient.isValid(), isTrue);

      final invalidClient1 = Client(name: '');
      expect(invalidClient1.isValid(), isFalse);

      final invalidClient2 = Client(name: '   ');
      expect(invalidClient2.isValid(), isFalse);

      final invalidClient3 = Client(
        name: 'John Doe',
        email: 'invalid-email',
      );
      expect(invalidClient3.isValid(), isFalse);

      final invalidClient4 = Client(
        name: 'John Doe',
        email: 'john@',
      );
      expect(invalidClient4.isValid(), isFalse);

      final invalidClient5 = Client(
        name: 'John Doe',
        email: '@example.com',
      );
      expect(invalidClient5.isValid(), isFalse);
    });

    test('should validate email formats correctly', () {
      final validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      final invalidEmails = [
        'invalid-email',
        'test@',
        '@example.com',
        'test@.com',
        'test.example.com',
        'test@example',
      ];

      for (final email in validEmails) {
        final client = Client(name: 'Test', email: email);
        expect(client.isValid(), isTrue, reason: 'Email $email should be valid');
      }

      for (final email in invalidEmails) {
        final client = Client(name: 'Test', email: email);
        expect(client.isValid(), isFalse, reason: 'Email $email should be invalid');
      }
    });

    test('should handle invalid address', () {
      final invalidAddress = Address(
        street: '',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final client = Client(
        name: 'John Doe',
        address: invalidAddress,
      );

      expect(client.isValid(), isFalse);
    });

    test('should serialize to/from JSON correctly', () {
      final address = Address(
        street: '123 Main St',
        city: 'New York',
        postalCode: '10001',
        country: 'USA',
      );

      final client = Client(
        id: 'test-id',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        address: address,
        taxId: 'TAX123456',
        notes: 'Important client',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-02T00:00:00.000Z',
      );

      final json = client.toJson();
      final fromJson = Client.fromJson(json);

      expect(fromJson.id, client.id);
      expect(fromJson.name, client.name);
      expect(fromJson.email, client.email);
      expect(fromJson.phone, client.phone);
      expect(fromJson.address, client.address);
      expect(fromJson.taxId, client.taxId);
      expect(fromJson.notes, client.notes);
      expect(fromJson.createdAt, client.createdAt);
      expect(fromJson.updatedAt, client.updatedAt);
    });

    test('should handle copyWith correctly', () {
      final original = Client(name: 'John Doe');
      final updated = original.copyWith(
        name: 'Jane Doe',
        email: '<EMAIL>',
      );

      expect(updated.id, original.id);
      expect(updated.name, 'Jane Doe');
      expect(updated.email, '<EMAIL>');
      expect(updated.createdAt, original.createdAt);
      expect(updated.updatedAt, isNotNull);
      expect(updated.updatedAt, isNot(equals(original.updatedAt)));
    });

    test('should handle equality correctly', () {
      final client1 = Client(id: 'test-id', name: 'John Doe');
      final client2 = Client(id: 'test-id', name: 'Jane Doe');
      final client3 = Client(id: 'other-id', name: 'John Doe');

      expect(client1, equals(client2)); // Same ID
      expect(client1, isNot(equals(client3))); // Different ID
      expect(client1.hashCode, equals(client2.hashCode));
    });
  });

  group('InvoiceLineItem', () {
    test('should create line item with calculated amount', () {
      final item = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      expect(item.id, isNotEmpty);
      expect(item.description, 'Web Development');
      expect(item.quantity, 10.0);
      expect(item.rate, 50.0);
      expect(item.amount, 500.0);
      expect(item.type, InvoiceLineItemType.timeEntry);
      expect(item.timeEntryId, isNull);
    });

    test('should create line item with explicit amount', () {
      final item = InvoiceLineItem(
        description: 'Discount',
        quantity: 1.0,
        rate: 100.0,
        amount: -50.0,
        type: InvoiceLineItemType.discount,
      );

      expect(item.amount, -50.0);
    });

    test('should validate correctly', () {
      final validItem = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );
      expect(validItem.isValid(), isTrue);

      final invalidItem1 = InvoiceLineItem(
        description: '',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );
      expect(invalidItem1.isValid(), isFalse);

      final invalidItem2 = InvoiceLineItem(
        description: '   ',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );
      expect(invalidItem2.isValid(), isFalse);

      final invalidItem3 = InvoiceLineItem(
        description: 'Web Development',
        quantity: -1.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );
      expect(invalidItem3.isValid(), isFalse);

      final invalidItem4 = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: -50.0,
        type: InvoiceLineItemType.timeEntry,
      );
      expect(invalidItem4.isValid(), isFalse);
    });

    test('should recalculate amount correctly', () {
      final item = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      item.quantity = 15.0;
      item.rate = 60.0;
      item.recalculateAmount();

      expect(item.amount, 900.0);
    });

    test('should serialize to/from JSON correctly', () {
      final item = InvoiceLineItem(
        id: 'test-id',
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
        timeEntryId: 'entry-123',
      );

      final json = item.toJson();
      final fromJson = InvoiceLineItem.fromJson(json);

      expect(fromJson.id, item.id);
      expect(fromJson.description, item.description);
      expect(fromJson.quantity, item.quantity);
      expect(fromJson.rate, item.rate);
      expect(fromJson.amount, item.amount);
      expect(fromJson.type, item.type);
      expect(fromJson.timeEntryId, item.timeEntryId);
    });

    test('should handle copyWith correctly', () {
      final original = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      final updated = original.copyWith(
        description: 'Mobile Development',
        quantity: 15.0,
      );

      expect(updated.id, original.id);
      expect(updated.description, 'Mobile Development');
      expect(updated.quantity, 15.0);
      expect(updated.rate, original.rate);
      expect(updated.amount, 750.0); // Recalculated
      expect(updated.type, original.type);
    });

    test('should handle equality correctly', () {
      final item1 = InvoiceLineItem(
        id: 'test-id',
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      final item2 = InvoiceLineItem(
        id: 'test-id',
        description: 'Mobile Development',
        quantity: 15.0,
        rate: 60.0,
        type: InvoiceLineItemType.expense,
      );

      final item3 = InvoiceLineItem(
        id: 'other-id',
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      expect(item1, equals(item2)); // Same ID
      expect(item1, isNot(equals(item3))); // Different ID
      expect(item1.hashCode, equals(item2.hashCode));
    });

    test('should handle all line item types', () {
      final types = [
        InvoiceLineItemType.timeEntry,
        InvoiceLineItemType.expense,
        InvoiceLineItemType.discount,
        InvoiceLineItemType.adjustment,
      ];

      for (final type in types) {
        final item = InvoiceLineItem(
          description: 'Test Item',
          quantity: 1.0,
          rate: 100.0,
          type: type,
        );
        expect(item.type, type);
      }
    });
  });

  group('Invoice', () {
    test('should create invoice with required fields', () {
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
      );

      expect(invoice.id, isNotEmpty);
      expect(invoice.invoiceNumber, 'INV-001');
      expect(invoice.clientId, 'client-123');
      expect(invoice.timeEntryIds, isEmpty);
      expect(invoice.additionalItems, isEmpty);
      expect(invoice.currency, 'USD');
      expect(invoice.locale, 'en_US');
      expect(invoice.issueDate, isNotNull);
      expect(invoice.dueDate, isNull);
      expect(invoice.status, InvoiceStatus.draft);
      expect(invoice.subtotal, 0.0);
      expect(invoice.taxRate, 0.0);
      expect(invoice.taxAmount, 0.0);
      expect(invoice.total, 0.0);
      expect(invoice.notes, isNull);
      expect(invoice.createdAt, isNotEmpty);
      expect(invoice.updatedAt, isNull);
    });

    test('should create invoice with all fields', () {
      final issueDate = DateTime(2024, 1, 1);
      final dueDate = DateTime(2024, 1, 31);
      final timeEntryIds = ['entry-1', 'entry-2'];
      final additionalItems = [
        InvoiceLineItem(
          description: 'Web Development',
          quantity: 10.0,
          rate: 50.0,
          type: InvoiceLineItemType.timeEntry,
        ),
      ];

      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        timeEntryIds: timeEntryIds,
        additionalItems: additionalItems,
        currency: 'EUR',
        locale: 'de_DE',
        issueDate: issueDate,
        dueDate: dueDate,
        status: InvoiceStatus.sent,
        subtotal: 500.0,
        taxRate: 20.0,
        taxAmount: 100.0,
        total: 600.0,
        notes: 'Payment due in 30 days',
      );

      expect(invoice.timeEntryIds, timeEntryIds);
      expect(invoice.additionalItems, additionalItems);
      expect(invoice.currency, 'EUR');
      expect(invoice.locale, 'de_DE');
      expect(invoice.issueDate, issueDate);
      expect(invoice.dueDate, dueDate);
      expect(invoice.status, InvoiceStatus.sent);
      expect(invoice.subtotal, 500.0);
      expect(invoice.taxRate, 20.0);
      expect(invoice.taxAmount, 100.0);
      expect(invoice.total, 600.0);
      expect(invoice.notes, 'Payment due in 30 days');
    });

    test('should validate correctly', () {
      final validInvoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        subtotal: 500.0,
        taxRate: 20.0,
        taxAmount: 100.0,
        total: 600.0,
      );
      expect(validInvoice.isValid(), isTrue);

      final invalidInvoice1 = Invoice(
        invoiceNumber: '',
        clientId: 'client-123',
      );
      expect(invalidInvoice1.isValid(), isFalse);

      final invalidInvoice2 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: '',
      );
      expect(invalidInvoice2.isValid(), isFalse);

      final invalidInvoice3 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        currency: '',
      );
      expect(invalidInvoice3.isValid(), isFalse);

      final invalidInvoice4 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        taxRate: -5.0,
      );
      expect(invalidInvoice4.isValid(), isFalse);

      final invalidInvoice5 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        taxRate: 150.0,
      );
      expect(invalidInvoice5.isValid(), isFalse);

      final invalidInvoice6 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        subtotal: -100.0,
      );
      expect(invalidInvoice6.isValid(), isFalse);
    });

    test('should validate additional items', () {
      final validItem = InvoiceLineItem(
        description: 'Web Development',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      final invalidItem = InvoiceLineItem(
        description: '',
        quantity: 10.0,
        rate: 50.0,
        type: InvoiceLineItemType.timeEntry,
      );

      final validInvoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        additionalItems: [validItem],
      );
      expect(validInvoice.isValid(), isTrue);

      final invalidInvoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        additionalItems: [invalidItem],
      );
      expect(invalidInvoice.isValid(), isFalse);
    });

    test('should recalculate totals correctly', () {
      final items = [
        InvoiceLineItem(
          description: 'Web Development',
          quantity: 10.0,
          rate: 50.0,
          type: InvoiceLineItemType.timeEntry,
        ),
        InvoiceLineItem(
          description: 'Hosting',
          quantity: 1.0,
          rate: 100.0,
          type: InvoiceLineItemType.expense,
        ),
      ];

      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        additionalItems: items,
        taxRate: 20.0,
      );

      invoice.recalculateTotals();

      expect(invoice.subtotal, 600.0); // 500 + 100
      expect(invoice.taxAmount, 120.0); // 600 * 0.20
      expect(invoice.total, 720.0); // 600 + 120
      expect(invoice.updatedAt, isNotNull);
    });

    test('should detect overdue invoices correctly', () {
      final pastDue = DateTime.now().subtract(const Duration(days: 10));
      final futureDue = DateTime.now().add(const Duration(days: 10));

      final overdueInvoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        dueDate: pastDue,
        status: InvoiceStatus.sent,
      );
      expect(overdueInvoice.isOverdue, isTrue);
      expect(overdueInvoice.daysPastDue, greaterThan(0));

      final notOverdueInvoice = Invoice(
        invoiceNumber: 'INV-002',
        clientId: 'client-123',
        dueDate: futureDue,
        status: InvoiceStatus.sent,
      );
      expect(notOverdueInvoice.isOverdue, isFalse);
      expect(notOverdueInvoice.daysPastDue, 0);

      final paidInvoice = Invoice(
        invoiceNumber: 'INV-003',
        clientId: 'client-123',
        dueDate: pastDue,
        status: InvoiceStatus.paid,
      );
      expect(paidInvoice.isOverdue, isFalse);
      expect(paidInvoice.daysPastDue, 0);

      final noDueDateInvoice = Invoice(
        invoiceNumber: 'INV-004',
        clientId: 'client-123',
        status: InvoiceStatus.sent,
      );
      expect(noDueDateInvoice.isOverdue, isFalse);
      expect(noDueDateInvoice.daysPastDue, 0);
    });

    test('should serialize to/from JSON correctly', () {
      final issueDate = DateTime(2024, 1, 1);
      final dueDate = DateTime(2024, 1, 31);
      final additionalItems = [
        InvoiceLineItem(
          description: 'Web Development',
          quantity: 10.0,
          rate: 50.0,
          type: InvoiceLineItemType.timeEntry,
        ),
      ];

      final invoice = Invoice(
        id: 'test-id',
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        timeEntryIds: ['entry-1', 'entry-2'],
        additionalItems: additionalItems,
        currency: 'EUR',
        locale: 'de_DE',
        issueDate: issueDate,
        dueDate: dueDate,
        status: InvoiceStatus.sent,
        subtotal: 500.0,
        taxRate: 20.0,
        taxAmount: 100.0,
        total: 600.0,
        notes: 'Payment due in 30 days',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-02T00:00:00.000Z',
      );

      final json = invoice.toJson();
      final fromJson = Invoice.fromJson(json);

      expect(fromJson.id, invoice.id);
      expect(fromJson.invoiceNumber, invoice.invoiceNumber);
      expect(fromJson.clientId, invoice.clientId);
      expect(fromJson.timeEntryIds, invoice.timeEntryIds);
      expect(fromJson.additionalItems.length, invoice.additionalItems.length);
      expect(fromJson.currency, invoice.currency);
      expect(fromJson.locale, invoice.locale);
      expect(fromJson.issueDate, invoice.issueDate);
      expect(fromJson.dueDate, invoice.dueDate);
      expect(fromJson.status, invoice.status);
      expect(fromJson.subtotal, invoice.subtotal);
      expect(fromJson.taxRate, invoice.taxRate);
      expect(fromJson.taxAmount, invoice.taxAmount);
      expect(fromJson.total, invoice.total);
      expect(fromJson.notes, invoice.notes);
      expect(fromJson.createdAt, invoice.createdAt);
      expect(fromJson.updatedAt, invoice.updatedAt);
    });

    test('should handle copyWith correctly', () {
      final original = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
      );

      final updated = original.copyWith(
        invoiceNumber: 'INV-002',
        status: InvoiceStatus.sent,
        notes: 'Updated invoice',
      );

      expect(updated.id, original.id);
      expect(updated.invoiceNumber, 'INV-002');
      expect(updated.clientId, original.clientId);
      expect(updated.status, InvoiceStatus.sent);
      expect(updated.notes, 'Updated invoice');
      expect(updated.createdAt, original.createdAt);
      expect(updated.updatedAt, isNotNull);
    });

    test('should handle equality correctly', () {
      final invoice1 = Invoice(
        id: 'test-id',
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
      );

      final invoice2 = Invoice(
        id: 'test-id',
        invoiceNumber: 'INV-002',
        clientId: 'client-456',
      );

      final invoice3 = Invoice(
        id: 'other-id',
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
      );

      expect(invoice1, equals(invoice2)); // Same ID
      expect(invoice1, isNot(equals(invoice3))); // Different ID
      expect(invoice1.hashCode, equals(invoice2.hashCode));
    });

    test('should handle all invoice statuses', () {
      final statuses = [
        InvoiceStatus.draft,
        InvoiceStatus.sent,
        InvoiceStatus.paid,
        InvoiceStatus.overdue,
        InvoiceStatus.cancelled,
      ];

      for (final status in statuses) {
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client-123',
          status: status,
        );
        expect(invoice.status, status);
      }
    });

    test('should handle copyWith with lists correctly', () {
      final original = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-123',
        timeEntryIds: ['entry-1'],
        additionalItems: [
          InvoiceLineItem(
            description: 'Original Item',
            quantity: 1.0,
            rate: 100.0,
            type: InvoiceLineItemType.timeEntry,
          ),
        ],
      );

      final updated = original.copyWith(
        timeEntryIds: ['entry-1', 'entry-2'],
        additionalItems: [
          InvoiceLineItem(
            description: 'New Item',
            quantity: 2.0,
            rate: 200.0,
            type: InvoiceLineItemType.expense,
          ),
        ],
      );

      // Original should be unchanged
      expect(original.timeEntryIds, ['entry-1']);
      expect(original.additionalItems.length, 1);
      expect(original.additionalItems[0].description, 'Original Item');

      // Updated should have new values
      expect(updated.timeEntryIds, ['entry-1', 'entry-2']);
      expect(updated.additionalItems.length, 1);
      expect(updated.additionalItems[0].description, 'New Item');
    });
  });

  group('InvoiceStatus', () {
    test('should have all expected status values', () {
      expect(InvoiceStatus.values.length, 5);
      expect(InvoiceStatus.values, contains(InvoiceStatus.draft));
      expect(InvoiceStatus.values, contains(InvoiceStatus.sent));
      expect(InvoiceStatus.values, contains(InvoiceStatus.paid));
      expect(InvoiceStatus.values, contains(InvoiceStatus.overdue));
      expect(InvoiceStatus.values, contains(InvoiceStatus.cancelled));
    });
  });

  group('InvoiceLineItemType', () {
    test('should have all expected type values', () {
      expect(InvoiceLineItemType.values.length, 4);
      expect(InvoiceLineItemType.values, contains(InvoiceLineItemType.timeEntry));
      expect(InvoiceLineItemType.values, contains(InvoiceLineItemType.expense));
      expect(InvoiceLineItemType.values, contains(InvoiceLineItemType.discount));
      expect(InvoiceLineItemType.values, contains(InvoiceLineItemType.adjustment));
    });
  });
}
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/widgets/add_client_dialog.dart';

import 'add_client_dialog_test.mocks.dart';

@GenerateMocks([ClientService])
void main() {
  group('AddClientDialog Widget Tests', () {
    late MockClientService mockClientService;

    setUp(() {
      mockClientService = MockClientService();
      
      // Setup default mock responses
      when(mockClientService.isValidEmail(any)).thenReturn(true);
      when(mockClientService.isValidPhone(any)).thenReturn(true);
      when(mockClientService.isValidTaxId(any)).thenReturn(true);
      when(mockClientService.findPotentialDuplicates(any))
          .thenAnswer((_) async => []);
      when(mockClientService.saveClient(any)).thenAnswer((_) async {});
    });

    Widget createWidget({
      String? initialName,
      Client? clientToEdit,
    }) {
      return MaterialApp(
        home: AddClientDialog(
          initialName: initialName,
          clientToEdit: clientToEdit,
          clientService: mockClientService,
        ),
      );
    }

    testWidgets('displays add client dialog correctly', (tester) async {
      await tester.pumpWidget(createWidget());

      expect(find.text('Add Client'), findsOneWidget);
      expect(find.text('Client Name *'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Phone'), findsOneWidget);
      expect(find.text('Tax ID'), findsOneWidget);
      expect(find.text('Address'), findsOneWidget);
      expect(find.text('Notes'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Add'), findsOneWidget);
    });

    testWidgets('displays edit client dialog correctly', (tester) async {
      final client = Client(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
      );

      await tester.pumpWidget(createWidget(clientToEdit: client));

      expect(find.text('Edit Client'), findsOneWidget);
      expect(find.text('Update'), findsOneWidget);
      
      // Check that fields are pre-filled
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('+1234567890'), findsOneWidget);
    });

    testWidgets('pre-fills name with initial name', (tester) async {
      await tester.pumpWidget(createWidget(initialName: 'Test Client'));

      expect(find.text('Test Client'), findsOneWidget);
    });

    testWidgets('validates required client name', (tester) async {
      await tester.pumpWidget(createWidget());

      // Try to save without entering name
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a client name'), findsOneWidget);
    });

    testWidgets('validates email format', (tester) async {
      when(mockClientService.isValidEmail('invalid-email'))
          .thenReturn(false);

      await tester.pumpWidget(createWidget());

      // Enter invalid email
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Email'),
        'invalid-email',
      );
      
      // Try to save
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('validates phone format', (tester) async {
      when(mockClientService.isValidPhone('123'))
          .thenReturn(false);

      await tester.pumpWidget(createWidget());

      // Enter invalid phone
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Phone'),
        '123',
      );
      
      // Try to save
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid phone number'), findsOneWidget);
    });

    testWidgets('validates tax ID format', (tester) async {
      when(mockClientService.isValidTaxId('x'))
          .thenReturn(false);

      await tester.pumpWidget(createWidget());

      // Enter invalid tax ID
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Tax ID'),
        'x',
      );
      
      // Try to save
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid tax ID'), findsOneWidget);
    });

    testWidgets('shows address fields when toggle is enabled', (tester) async {
      await tester.pumpWidget(createWidget());

      // Initially address fields should be hidden
      expect(find.text('Street Address *'), findsNothing);

      // Toggle address fields
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Address fields should now be visible
      expect(find.text('Street Address *'), findsOneWidget);
      expect(find.text('Street Address 2'), findsOneWidget);
      expect(find.text('City *'), findsOneWidget);
      expect(find.text('State'), findsOneWidget);
      expect(find.text('Postal Code *'), findsOneWidget);
      expect(find.text('Country *'), findsOneWidget);
    });

    testWidgets('validates required address fields when address is enabled', (tester) async {
      await tester.pumpWidget(createWidget());

      // Enter client name
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Test Client',
      );

      // Enable address fields
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Try to save without filling required address fields
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a street address'), findsOneWidget);
      expect(find.text('Please enter a city'), findsOneWidget);
      expect(find.text('Please enter a postal code'), findsOneWidget);
      expect(find.text('Please enter a country'), findsOneWidget);
    });

    testWidgets('saves client successfully', (tester) async {
      Client? savedClient;
      when(mockClientService.saveClient(any)).thenAnswer((invocation) async {
        savedClient = invocation.positionalArguments[0] as Client;
      });

      await tester.pumpWidget(createWidget());

      // Fill in required fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Test Client',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Email'),
        '<EMAIL>',
      );

      // Save the client
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      // Verify client was saved
      verify(mockClientService.saveClient(any)).called(1);
      expect(savedClient?.name, equals('Test Client'));
      expect(savedClient?.email, equals('<EMAIL>'));
    });

    testWidgets('saves client with address', (tester) async {
      Client? savedClient;
      when(mockClientService.saveClient(any)).thenAnswer((invocation) async {
        savedClient = invocation.positionalArguments[0] as Client;
      });

      await tester.pumpWidget(createWidget());

      // Fill in required fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Test Client',
      );

      // Enable and fill address fields
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Street Address *'),
        '123 Main St',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'City *'),
        'Test City',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Postal Code *'),
        '12345',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Country *'),
        'Test Country',
      );

      // Save the client
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      // Verify client was saved with address
      verify(mockClientService.saveClient(any)).called(1);
      expect(savedClient?.address?.street, equals('123 Main St'));
      expect(savedClient?.address?.city, equals('Test City'));
      expect(savedClient?.address?.postalCode, equals('12345'));
      expect(savedClient?.address?.country, equals('Test Country'));
    });

    testWidgets('shows duplicate warning', (tester) async {
      final duplicateClient = Client(
        name: 'Existing Client',
        email: '<EMAIL>',
      );

      when(mockClientService.findPotentialDuplicates(any))
          .thenAnswer((_) async => [duplicateClient]);

      await tester.pumpWidget(createWidget());

      // Enter client name that matches existing client
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Existing Client',
      );

      // Trigger duplicate check by changing focus
      await tester.tap(find.widgetWithText(TextFormField, 'Email'));
      await tester.pumpAndSettle();

      // Should show duplicate warning
      expect(find.text('Similar clients found'), findsOneWidget);
      expect(find.text('• Existing Client (<EMAIL>)'), findsOneWidget);
    });

    testWidgets('shows duplicate confirmation dialog when saving', (tester) async {
      final duplicateClient = Client(
        name: 'Existing Client',
        email: '<EMAIL>',
      );

      when(mockClientService.findPotentialDuplicates(any))
          .thenAnswer((_) async => [duplicateClient]);

      await tester.pumpWidget(createWidget());

      // Enter client name
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Existing Client',
      );

      // Try to save
      await tester.tap(find.text('Add'));
      await tester.pumpAndSettle();

      // Should show duplicate confirmation dialog
      expect(find.text('Potential Duplicate'), findsOneWidget);
      expect(find.text('Similar clients already exist:'), findsOneWidget);
      expect(find.text('Continue'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('cancels dialog when cancel is tapped', (tester) async {
      await tester.pumpWidget(createWidget());

      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Dialog should be closed (no longer visible)
      expect(find.text('Add Client'), findsNothing);
    });

    testWidgets('shows loading state when saving', (tester) async {
      // Make save operation take some time
      when(mockClientService.saveClient(any)).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
      });

      await tester.pumpWidget(createWidget());

      // Fill in required fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Client Name *'),
        'Test Client',
      );

      // Start saving
      await tester.tap(find.text('Add'));
      await tester.pump(); // Don't use pumpAndSettle to catch loading state

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
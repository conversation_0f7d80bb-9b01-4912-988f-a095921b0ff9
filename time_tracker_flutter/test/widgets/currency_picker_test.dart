import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/widgets/currency_picker.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';

void main() {
  group('CurrencyPicker Widget Tests', () {
    testWidgets('displays popular currencies section by default', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPicker(
              showAsDialog: false,
              showFormattingPreview: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('Popular Currencies'), findsOneWidget);

      final popularCurrencies = CurrencyService.getPopularCurrencies();
      for (final currency in popularCurrencies) {
        expect(find.text(currency), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('hides popular currencies section when showPopularCurrencies is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPicker(
              showAsDialog: false,
              showPopularCurrencies: false,
              showFormattingPreview: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Popular Currencies'), findsNothing);
    });

    testWidgets('search functionality filters currencies correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPicker(
              showAsDialog: false,
              showFormattingPreview: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);

      await tester.enterText(searchField, 'USD');
      await tester.pump();

      expect(find.text('US Dollar'), findsOneWidget);
      expect(find.text('USD'), findsAtLeastNWidgets(1));
      expect(find.text('Euro'), findsNothing);
    });

    testWidgets('currency selection callback is triggered', (tester) async {
      String? selectedCurrency;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPicker(
              showAsDialog: false,
              showFormattingPreview: false,
              onCurrencySelected: (currency) {
                selectedCurrency = currency;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final usdCard = find.text('USD').first;
      await tester.tap(usdCard);
      await tester.pump();

      expect(selectedCurrency, equals('USD'));
    });

    testWidgets('widget renders correctly with selected currency', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPicker(
              showAsDialog: false,
              selectedCurrency: 'USD',
              showFormattingPreview: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byType(CurrencyPicker), findsOneWidget);
      expect(find.text('USD'), findsAtLeastNWidgets(1));
    });
  });

  group('CurrencyPickerButton Widget Tests', () {
    testWidgets('displays placeholder when no currency selected', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPickerButton(
              onCurrencySelected: (currency) {},
              placeholder: 'Choose Currency',
            ),
          ),
        ),
      );

      expect(find.text('Choose Currency'), findsOneWidget);
      expect(find.byIcon(Icons.currency_exchange), findsOneWidget);
    });

    testWidgets('displays selected currency information', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPickerButton(
              selectedCurrency: 'USD',
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      expect(find.text('US Dollar (USD)'), findsOneWidget);
    });

    testWidgets('opens currency picker dialog when tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPickerButton(
              showFormattingPreview: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      await tester.tap(find.byType(CurrencyPickerButton));
      await tester.pumpAndSettle();

      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Popular Currencies'), findsOneWidget);
    });

    testWidgets('button is disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CurrencyPickerButton(
              enabled: false,
              onCurrencySelected: (currency) {},
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CurrencyPickerButton);
      expect(buttonFinder, findsOneWidget);

      await tester.tap(buttonFinder);
      await tester.pumpAndSettle();

      expect(find.byType(AlertDialog), findsNothing);
    });
  });

  group('Currency Service Integration Tests', () {
    test('all popular currencies are supported', () {
      final popularCurrencies = CurrencyService.getPopularCurrencies();
      for (final currency in popularCurrencies) {
        expect(CurrencyService.isCurrencySupported(currency), isTrue,
            reason: 'Popular currency $currency should be supported');
      }
    });

    test('currency formatting examples work for all supported currencies', () {
      final supportedCurrencies = CurrencyService.getSupportedCurrencies();
      for (final currency in supportedCurrencies) {
        final example = CurrencyService.getCurrencyFormattingExample(currency);
        expect(example, isNotEmpty,
            reason: 'Currency $currency should have a formatting example');
        expect(example, contains(CurrencyService.getCurrencySymbol(currency)),
            reason: 'Formatting example for $currency should contain its symbol');
      }
    });

    test('currency info is available for all supported currencies', () {
      final supportedCurrencies = CurrencyService.getSupportedCurrencies();
      for (final currency in supportedCurrencies) {
        final info = CurrencyService.getCurrencyInfo(currency);
        expect(info, isNotNull, reason: 'Currency $currency should have info');
        expect(info!['name'], isNotEmpty, reason: 'Currency $currency should have a name');
        expect(info['symbol'], isNotEmpty, reason: 'Currency $currency should have a symbol');
        expect(info['code'], equals(currency), reason: 'Currency code should match');
      }
    });

    test('currencies are sorted by name correctly', () {
      final sortedCurrencies = CurrencyService.getCurrenciesSortedByName();
      expect(sortedCurrencies, isNotEmpty);

      for (int i = 1; i < sortedCurrencies.length; i++) {
        final prevName = sortedCurrencies[i - 1]['name'] as String;
        final currName = sortedCurrencies[i]['name'] as String;
        expect(prevName.compareTo(currName), lessThanOrEqualTo(0),
            reason: 'Currencies should be sorted alphabetically by name');
      }
    });
  });
}
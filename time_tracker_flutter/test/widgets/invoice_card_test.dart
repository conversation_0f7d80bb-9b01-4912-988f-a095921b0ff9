import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/widgets/invoice_card.dart';

void main() {
  group('InvoiceCard Widget Tests', () {
    late Invoice testInvoice;
    late Client testClient;

    setUp(() {
      testClient = Client(
        name: 'Test Client',
        email: '<EMAIL>',
      );

      testInvoice = Invoice(
        invoiceNumber: 'INV-**********',
        clientId: testClient.id,
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime(2025, 1, 15),
        dueDate: DateTime(2025, 2, 15),
        status: InvoiceStatus.sent,
        subtotal: 1000.0,
        taxRate: 10.0,
        taxAmount: 100.0,
        total: 1100.0,
      );
    });

    testWidgets('displays invoice information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: testInvoice,
              client: testClient,
            ),
          ),
        ),
      );

      // Verify invoice number is displayed
      expect(find.text('INV-**********'), findsOneWidget);
      
      // Verify client name is displayed
      expect(find.text('Test Client'), findsOneWidget);
      
      // Verify status chip is displayed
      expect(find.text('Sent'), findsOneWidget);
      
      // Verify total amount is displayed
      expect(find.textContaining('\$1,100.00'), findsOneWidget);
      
      // Verify dates are displayed
      expect(find.text('Issue Date'), findsOneWidget);
      expect(find.text('Due Date'), findsOneWidget);
    });

    testWidgets('displays overdue warning for overdue invoices', (WidgetTester tester) async {
      final overdueInvoice = testInvoice.copyWith(
        status: InvoiceStatus.overdue,
        dueDate: DateTime.now().subtract(const Duration(days: 5)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: overdueInvoice,
              client: testClient,
            ),
          ),
        ),
      );

      // Verify overdue warning is displayed
      expect(find.textContaining('days overdue'), findsOneWidget);
      expect(find.byIcon(Icons.warning_amber), findsOneWidget);
    });

    testWidgets('displays correct status chips for different statuses', (WidgetTester tester) async {
      final statuses = [
        (InvoiceStatus.draft, 'Draft', Icons.edit_note),
        (InvoiceStatus.sent, 'Sent', Icons.send),
        (InvoiceStatus.paid, 'Paid', Icons.check_circle),
        (InvoiceStatus.overdue, 'Overdue', Icons.warning),
        (InvoiceStatus.cancelled, 'Cancelled', Icons.cancel),
      ];

      for (final (status, text, icon) in statuses) {
        final invoice = testInvoice.copyWith(status: status);
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: InvoiceCard(
                invoice: invoice,
                client: testClient,
              ),
            ),
          ),
        );

        expect(find.text(text), findsOneWidget);
        expect(find.byIcon(icon), findsOneWidget);
        
        await tester.pumpAndSettle();
      }
    });

    testWidgets('calls onTap callback when card is tapped', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: testInvoice,
              client: testClient,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(InvoiceCard));
      expect(tapped, isTrue);
    });

    testWidgets('displays action buttons when callbacks are provided', (WidgetTester tester) async {
      bool editCalled = false;
      bool deleteCalled = false;
      bool statusChangeCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: testInvoice,
              client: testClient,
              onEdit: () => editCalled = true,
              onDelete: () => deleteCalled = true,
              onStatusChange: () => statusChangeCalled = true,
            ),
          ),
        ),
      );

      // Verify action buttons are displayed
      expect(find.text('Edit'), findsOneWidget);
      expect(find.text('Delete'), findsOneWidget);
      expect(find.text('Status'), findsOneWidget);

      // Test button callbacks
      await tester.tap(find.text('Edit'));
      expect(editCalled, isTrue);

      await tester.tap(find.text('Delete'));
      expect(deleteCalled, isTrue);

      await tester.tap(find.text('Status'));
      expect(statusChangeCalled, isTrue);
    });

    testWidgets('handles invoice without client', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: testInvoice,
              client: null, // No client provided
            ),
          ),
        ),
      );

      // Should still display invoice information
      expect(find.text('INV-**********'), findsOneWidget);
      expect(find.text('Sent'), findsOneWidget);
      
      // Client name should not be displayed
      expect(find.text('Test Client'), findsNothing);
    });

    testWidgets('handles invoice without due date', (WidgetTester tester) async {
      final invoiceWithoutDueDate = Invoice(
        invoiceNumber: testInvoice.invoiceNumber,
        clientId: testInvoice.clientId,
        currency: testInvoice.currency,
        locale: testInvoice.locale,
        issueDate: testInvoice.issueDate,
        dueDate: null, // Explicitly set to null
        status: testInvoice.status,
        subtotal: testInvoice.subtotal,
        taxRate: testInvoice.taxRate,
        taxAmount: testInvoice.taxAmount,
        total: testInvoice.total,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InvoiceCard(
              invoice: invoiceWithoutDueDate,
              client: testClient,
            ),
          ),
        ),
      );

      // Should display issue date but not due date
      expect(find.text('Issue Date'), findsOneWidget);
      expect(find.text('Due Date'), findsNothing);
    });
  });
}
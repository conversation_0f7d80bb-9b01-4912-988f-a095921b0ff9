import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/widgets/service_period_selector.dart';

void main() {
  group('ServicePeriodSelector Widget', () {
    testWidgets('should display service period checkbox and title', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Should display the checkbox and title
      expect(find.byType(Checkbox), findsOneWidget);
      expect(find.text('Service Period'), findsOneWidget);
      expect(find.text('From'), findsNothing); // Should not show date fields initially
      expect(find.text('To'), findsNothing);
    });

    testWidgets('should show date fields when enabled', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Tap the checkbox to enable service period
      await tester.tap(find.byType(Checkbox));
      await tester.pump();

      // Should now show date fields
      expect(find.text('From'), findsOneWidget);
      expect(find.text('To'), findsOneWidget);
      expect(find.text('Select date'), findsNWidgets(2)); // Both fields should show placeholder
      expect(enabled, isTrue);
    });

    testWidgets('should hide date fields when disabled', (WidgetTester tester) async {
      bool enabled = true;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              initialEnabled: true,
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Should show date fields initially
      expect(find.text('From'), findsOneWidget);
      expect(find.text('To'), findsOneWidget);

      // Tap the checkbox to disable service period
      await tester.tap(find.byType(Checkbox));
      await tester.pump();

      // Should hide date fields
      expect(find.text('From'), findsNothing);
      expect(find.text('To'), findsNothing);
      expect(enabled, isFalse);
      expect(startDate, isNull);
      expect(endDate, isNull);
    });

    testWidgets('should display initial dates when provided', (WidgetTester tester) async {
      final initialStart = DateTime(2024, 1, 1);
      final initialEnd = DateTime(2024, 1, 31);
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              initialEnabled: true,
              initialStartDate: initialStart,
              initialEndDate: initialEnd,
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Should display the formatted dates
      expect(find.textContaining('1/1/2024'), findsOneWidget);
      expect(find.textContaining('1/31/2024'), findsOneWidget);
    });

    testWidgets('should show validation error for invalid date range', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      final widget = ServicePeriodSelector(
        initialEnabled: true,
        initialStartDate: DateTime(2024, 1, 31), // End date before start date
        initialEndDate: DateTime(2024, 1, 1),
        onEnabledChanged: (value) => enabled = value,
        onStartDateChanged: (value) => startDate = value,
        onEndDateChanged: (value) => endDate = value,
        language: 'en_US',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: widget),
        ),
      );

      // Should show validation error
      expect(find.text('End date must be after or equal to start date'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should not show validation error when only one date is selected', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      final widget = ServicePeriodSelector(
        initialEnabled: true,
        initialStartDate: DateTime(2024, 1, 1),
        // No end date provided
        onEnabledChanged: (value) => enabled = value,
        onStartDateChanged: (value) => startDate = value,
        onEndDateChanged: (value) => endDate = value,
        language: 'en_US',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: widget),
        ),
      );

      // Should not show validation error when only one date is selected (user is still selecting)
      expect(find.text('Both start and end dates are required when service period is enabled'), findsNothing);
      expect(find.byIcon(Icons.error_outline), findsNothing);
    });

    testWidgets('should not show validation error when service period is disabled', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      final widget = ServicePeriodSelector(
        initialEnabled: false,
        initialStartDate: DateTime(2024, 1, 31), // Invalid range but disabled
        initialEndDate: DateTime(2024, 1, 1),
        onEnabledChanged: (value) => enabled = value,
        onStartDateChanged: (value) => startDate = value,
        onEndDateChanged: (value) => endDate = value,
        language: 'en_US',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: widget),
        ),
      );

      // Should not show validation error when disabled
      expect(find.text('End date must be after or equal to start date'), findsNothing);
      expect(find.byIcon(Icons.error_outline), findsNothing);
    });

    testWidgets('should have proper accessibility support', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Check that checkbox has semantic label
      final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
      expect(checkbox.semanticLabel, equals('Enable Service Period'));
    });

    testWidgets('should handle date picker interaction', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              initialEnabled: true,
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'en_US',
            ),
          ),
        ),
      );

      // Tap on the start date field
      await tester.tap(find.text('Select date').first);
      await tester.pumpAndSettle();

      // Should open date picker
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('should always display English UI regardless of language parameter', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServicePeriodSelector(
              onEnabledChanged: (value) => enabled = value,
              onStartDateChanged: (value) => startDate = value,
              onEndDateChanged: (value) => endDate = value,
              language: 'de_DE', // German language parameter
            ),
          ),
        ),
      );

      // Should display English UI even with German language parameter
      expect(find.text('Service Period'), findsOneWidget);
      
      // Enable service period to see date fields
      await tester.tap(find.byType(Checkbox));
      await tester.pump();
      
      expect(find.text('From'), findsOneWidget);
      expect(find.text('To'), findsOneWidget);
      expect(find.text('Select date'), findsNWidgets(2));
    });

    testWidgets('should show English validation error messages', (WidgetTester tester) async {
      bool enabled = false;
      DateTime? startDate;
      DateTime? endDate;

      final widget = ServicePeriodSelector(
        initialEnabled: true,
        initialStartDate: DateTime(2024, 1, 31), // End date before start date
        initialEndDate: DateTime(2024, 1, 1),
        onEnabledChanged: (value) => enabled = value,
        onStartDateChanged: (value) => startDate = value,
        onEndDateChanged: (value) => endDate = value,
        language: 'de_DE', // German language parameter
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: widget),
        ),
      );

      // Should show English validation error even with German language parameter
      expect(find.text('End date must be after or equal to start date'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });
  });
}
// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in time_tracker_flutter/test/widgets/add_client_dialog_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:time_tracker_flutter/models/invoice_models.dart' as _i2;
import 'package:time_tracker_flutter/services/client_service.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeClient_0 extends _i1.SmartFake implements _i2.Client {
  _FakeClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeClientValidationResult_1 extends _i1.SmartFake
    implements _i3.ClientValidationResult {
  _FakeClientValidationResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeClientStatistics_2 extends _i1.SmartFake
    implements _i3.ClientStatistics {
  _FakeClientStatistics_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ClientService].
///
/// See the documentation for Mockito's code generation for more information.
class MockClientService extends _i1.Mock implements _i3.ClientService {
  MockClientService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.Client>> getClients() => (super.noSuchMethod(
        Invocation.method(
          #getClients,
          [],
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);

  @override
  _i4.Future<_i2.Client?> getClient(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getClient,
          [id],
        ),
        returnValue: _i4.Future<_i2.Client?>.value(),
      ) as _i4.Future<_i2.Client?>);

  @override
  _i4.Future<List<_i2.Client>> searchClients(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchClients,
          [query],
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);

  @override
  _i4.Future<List<_i2.Client>> filterClients({
    String? nameContains,
    String? emailContains,
    String? phoneContains,
    String? countryEquals,
    bool? hasEmail,
    bool? hasPhone,
    bool? hasAddress,
    bool? hasTaxId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #filterClients,
          [],
          {
            #nameContains: nameContains,
            #emailContains: emailContains,
            #phoneContains: phoneContains,
            #countryEquals: countryEquals,
            #hasEmail: hasEmail,
            #hasPhone: hasPhone,
            #hasAddress: hasAddress,
            #hasTaxId: hasTaxId,
          },
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);

  @override
  _i4.Future<void> saveClient(_i2.Client? client) => (super.noSuchMethod(
        Invocation.method(
          #saveClient,
          [client],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.Client> createClient({
    required String? name,
    String? email,
    String? phone,
    _i2.Address? address,
    String? taxId,
    String? notes,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createClient,
          [],
          {
            #name: name,
            #email: email,
            #phone: phone,
            #address: address,
            #taxId: taxId,
            #notes: notes,
          },
        ),
        returnValue: _i4.Future<_i2.Client>.value(_FakeClient_0(
          this,
          Invocation.method(
            #createClient,
            [],
            {
              #name: name,
              #email: email,
              #phone: phone,
              #address: address,
              #taxId: taxId,
              #notes: notes,
            },
          ),
        )),
      ) as _i4.Future<_i2.Client>);

  @override
  _i4.Future<_i2.Client> updateClient(
    String? clientId, {
    String? name,
    String? email,
    String? phone,
    _i2.Address? address,
    String? taxId,
    String? notes,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateClient,
          [clientId],
          {
            #name: name,
            #email: email,
            #phone: phone,
            #address: address,
            #taxId: taxId,
            #notes: notes,
          },
        ),
        returnValue: _i4.Future<_i2.Client>.value(_FakeClient_0(
          this,
          Invocation.method(
            #updateClient,
            [clientId],
            {
              #name: name,
              #email: email,
              #phone: phone,
              #address: address,
              #taxId: taxId,
              #notes: notes,
            },
          ),
        )),
      ) as _i4.Future<_i2.Client>);

  @override
  _i4.Future<void> deleteClient(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteClient,
          [id],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> canDeleteClient(String? clientId) => (super.noSuchMethod(
        Invocation.method(
          #canDeleteClient,
          [clientId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<_i2.Client>> getClientsWithInvoices() => (super.noSuchMethod(
        Invocation.method(
          #getClientsWithInvoices,
          [],
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);

  @override
  _i4.Future<List<_i2.Client>> getClientsWithoutInvoices() =>
      (super.noSuchMethod(
        Invocation.method(
          #getClientsWithoutInvoices,
          [],
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);

  @override
  _i3.ClientValidationResult validateClient(_i2.Client? client) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateClient,
          [client],
        ),
        returnValue: _FakeClientValidationResult_1(
          this,
          Invocation.method(
            #validateClient,
            [client],
          ),
        ),
      ) as _i3.ClientValidationResult);

  @override
  bool isValidEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #isValidEmail,
          [email],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isValidPhone(String? phone) => (super.noSuchMethod(
        Invocation.method(
          #isValidPhone,
          [phone],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isValidTaxId(String? taxId) => (super.noSuchMethod(
        Invocation.method(
          #isValidTaxId,
          [taxId],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<List<_i3.ClientSelectionOption>> getClientSelectionOptions({
    String? searchQuery,
    bool? includeCreateNew = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getClientSelectionOptions,
          [],
          {
            #searchQuery: searchQuery,
            #includeCreateNew: includeCreateNew,
          },
        ),
        returnValue: _i4.Future<List<_i3.ClientSelectionOption>>.value(
            <_i3.ClientSelectionOption>[]),
      ) as _i4.Future<List<_i3.ClientSelectionOption>>);

  @override
  _i4.Future<_i3.ClientStatistics> getClientStatistics() => (super.noSuchMethod(
        Invocation.method(
          #getClientStatistics,
          [],
        ),
        returnValue:
            _i4.Future<_i3.ClientStatistics>.value(_FakeClientStatistics_2(
          this,
          Invocation.method(
            #getClientStatistics,
            [],
          ),
        )),
      ) as _i4.Future<_i3.ClientStatistics>);

  @override
  _i4.Future<List<_i2.Client>> findPotentialDuplicates(_i2.Client? client) =>
      (super.noSuchMethod(
        Invocation.method(
          #findPotentialDuplicates,
          [client],
        ),
        returnValue: _i4.Future<List<_i2.Client>>.value(<_i2.Client>[]),
      ) as _i4.Future<List<_i2.Client>>);
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/widgets/client_selector.dart';

import 'client_selector_test.mocks.dart';

@GenerateMocks([ClientService])
void main() {
  group('ClientSelector Widget Tests', () {
    late MockClientService mockClientService;
    late List<Client> testClients;
    late List<ClientSelectionOption> testOptions;

    setUp(() {
      mockClientService = MockClientService();
      
      testClients = [
        Client(
          id: '1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+1234567890',
        ),
        Client(
          id: '2',
          name: '<PERSON>',
          email: '<EMAIL>',
        ),
        Client(
          id: '3',
          name: '<PERSON>',
          phone: '+0987654321',
        ),
      ];

      testOptions = testClients.map((client) => ClientSelectionOption(
        client: client,
        displayText: client.name,
        isCreateNew: false,
      )).toList();
    });

    Widget createWidget({
      Client? selectedClient,
      Function(Client?)? onClientSelected,
      String? hintText,
      bool enabled = true,
      bool showCreateNew = true,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ClientSelector(
            selectedClient: selectedClient,
            onClientSelected: onClientSelected ?? (client) {},
            hintText: hintText,
            enabled: enabled,
            showCreateNew: showCreateNew,
            clientService: mockClientService,
          ),
        ),
      );
    }

    testWidgets('displays correctly with no selection', (tester) async {
      when(mockClientService.getClientSelectionOptions(includeCreateNew: false))
          .thenAnswer((_) async => testOptions);

      await tester.pumpWidget(createWidget());

      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.text('Search or select a client'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('displays selected client name', (tester) async {
      final selectedClient = testClients.first;
      
      await tester.pumpWidget(createWidget(selectedClient: selectedClient));

      expect(find.text(selectedClient.name), findsOneWidget);
      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('shows dropdown when tapped', (tester) async {
      when(mockClientService.getClientSelectionOptions(includeCreateNew: false))
          .thenAnswer((_) async => testOptions);

      await tester.pumpWidget(createWidget());
      
      // Tap the text field
      await tester.tap(find.byType(TextFormField));
      await tester.pumpAndSettle();

      // Should show dropdown with clients
      expect(find.byType(ListView), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsOneWidget);
    });

    testWidgets('filters clients when searching', (tester) async {
      when(mockClientService.getClientSelectionOptions(
        searchQuery: 'John',
        includeCreateNew: true,
      )).thenAnswer((_) async => [
        ClientSelectionOption(
          client: null,
          displayText: 'Create new client: "John"',
          isCreateNew: true,
          suggestedName: 'John',
        ),
        ClientSelectionOption(
          client: testClients.first,
          displayText: testClients.first.name,
          isCreateNew: false,
        ),
      ]);

      await tester.pumpWidget(createWidget());
      
      // Enter search text
      await tester.enterText(find.byType(TextFormField), 'John');
      await tester.pumpAndSettle();

      // Should show filtered results with create new option
      expect(find.text('Create new client: "John"'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);
    });

    testWidgets('calls onClientSelected when client is selected', (tester) async {
      Client? selectedClient;
      
      when(mockClientService.getClientSelectionOptions(includeCreateNew: false))
          .thenAnswer((_) async => testOptions);

      await tester.pumpWidget(createWidget(
        onClientSelected: (client) => selectedClient = client,
      ));
      
      // Tap the text field to show dropdown
      await tester.tap(find.byType(TextFormField));
      await tester.pumpAndSettle();

      // Tap on a client
      await tester.tap(find.text('John Doe'));
      await tester.pumpAndSettle();

      expect(selectedClient, equals(testClients.first));
    });

    testWidgets('clears selection when clear button is tapped', (tester) async {
      Client? selectedClient = testClients.first;
      
      await tester.pumpWidget(createWidget(
        selectedClient: testClients.first,
        onClientSelected: (client) => selectedClient = client,
      ));

      // Tap the clear button
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pumpAndSettle();

      expect(selectedClient, isNull);
    });

    testWidgets('shows add button when showCreateNew is true', (tester) async {
      await tester.pumpWidget(createWidget(showCreateNew: true));

      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('hides add button when showCreateNew is false', (tester) async {
      await tester.pumpWidget(createWidget(showCreateNew: false));

      expect(find.byIcon(Icons.add), findsNothing);
    });

    testWidgets('disables interaction when enabled is false', (tester) async {
      await tester.pumpWidget(createWidget(enabled: false));

      final textField = tester.widget<TextFormField>(find.byType(TextFormField));
      expect(textField.enabled, isFalse);
    });

    testWidgets('validates required selection', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ClientSelector(
                onClientSelected: (client) {},
              ),
            ),
          ),
        ),
      );

      final formState = Form.of(tester.element(find.byType(ClientSelector)));
      final isValid = formState?.validate() ?? false;

      expect(isValid, isFalse);
    });

    testWidgets('passes validation with selected client', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ClientSelector(
                selectedClient: testClients.first,
                onClientSelected: (client) {},
              ),
            ),
          ),
        ),
      );

      final formState = Form.of(tester.element(find.byType(ClientSelector)));
      final isValid = formState?.validate() ?? false;

      expect(isValid, isTrue);
    });

    testWidgets('shows loading indicator when searching', (tester) async {
      // Create a completer to control when the future completes
      when(mockClientService.getClientSelectionOptions(includeCreateNew: false))
          .thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return testOptions;
      });

      await tester.pumpWidget(createWidget());
      
      // Tap to trigger loading
      await tester.tap(find.byType(TextFormField));
      await tester.pump(); // Don't use pumpAndSettle to catch loading state

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays custom hint text', (tester) async {
      const customHint = 'Choose a client';
      
      await tester.pumpWidget(createWidget(hintText: customHint));

      expect(find.text(customHint), findsOneWidget);
    });

    testWidgets('shows client email in dropdown subtitle', (tester) async {
      when(mockClientService.getClientSelectionOptions(includeCreateNew: false))
          .thenAnswer((_) async => testOptions);

      await tester.pumpWidget(createWidget());
      
      // Tap the text field to show dropdown
      await tester.tap(find.byType(TextFormField));
      await tester.pumpAndSettle();

      // Should show client emails as subtitles
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });
  });
}
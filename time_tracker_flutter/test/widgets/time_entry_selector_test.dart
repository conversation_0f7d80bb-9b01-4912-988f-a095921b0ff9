import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/widgets/time_entry_selector.dart';

import 'time_entry_selector_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  group('TimeEntrySelector Widget Tests', () {
    late MockDatabaseService mockDatabaseService;
    late List<Project> testProjects;
    late List<TimeEntry> testTimeEntries;

    setUp(() {
      mockDatabaseService = MockDatabaseService();

      // Create test projects
      testProjects = [
        Project(
          id: 'project1',
          name: 'Project Alpha',
          colorHex: '#FF5722',
          iconDataCodePoint: Icons.work.codePoint,
        ),
        Project(
          id: 'project2',
          name: 'Project Beta',
          colorHex: '#2196F3',
          iconDataCodePoint: Icons.code.codePoint,
        ),
      ];

      // Create test time entries
      testTimeEntries = [
        TimeEntry(
          id: 'entry1',
          projectId: 'project1',
          date: '2024-01-15',
          start: '09:00',
          end: '12:00',
          inProgress: false,
        ),
        TimeEntry(
          id: 'entry2',
          projectId: 'project1',
          date: '2024-01-16',
          duration: '2:30',
          inProgress: false,
        ),
        TimeEntry(
          id: 'entry3',
          projectId: 'project2',
          date: '2024-01-17',
          start: '14:00',
          end: '17:30',
          inProgress: false,
        ),
        TimeEntry(
          id: 'entry4',
          projectId: 'project1',
          date: '2024-01-18',
          start: '10:00',
          end: '11:00',
          inProgress: true, // This should be filtered out
        ),
      ];

      // Setup mock responses
      when(mockDatabaseService.getProjects()).thenAnswer((_) async => testProjects);
      when(mockDatabaseService.getTimeEntries(
        projectId: anyNamed('projectId'),
        startDate: anyNamed('startDate'),
        endDate: anyNamed('endDate'),
      )).thenAnswer((_) async => testTimeEntries);
    });

    Widget createTestWidget({
      List<String> initialSelectedIds = const [],
      Function(List<TimeEntry>)? onSelectionChanged,
      String? projectFilter,
      bool showOnlyUnbilled = true,
      DateTimeRange? dateRange,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: TimeEntrySelector(
            initialSelectedIds: initialSelectedIds,
            onSelectionChanged: onSelectionChanged ?? (_) {},
            projectFilter: projectFilter,
            showOnlyUnbilled: showOnlyUnbilled,
            dateRange: dateRange,
            databaseService: mockDatabaseService,
          ),
        ),
      );
    }

    testWidgets('displays loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays time entries grouped by project', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show project groups
      expect(find.text('Project Alpha'), findsOneWidget);
      expect(find.text('Project Beta'), findsOneWidget);

      // Should show entry counts (excluding in-progress entries)
      expect(find.text('2 entries • 5.5h total'), findsOneWidget); // Project Alpha has 2 completed entries
      expect(find.text('1 entries • 3.5h total'), findsOneWidget); // Project Beta has 1 entry
    });

    testWidgets('filters out in-progress entries', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Project Alpha should be expanded by default and show 2 completed entries
      // The text should show "2 entries • 5.5h total" indicating 2 completed entries
      expect(find.text('2 entries • 5.5h total'), findsOneWidget);
      
      // Verify that the in-progress entry (entry4) is not included in the count
      // If it were included, we would see "3 entries" instead of "2 entries"
      expect(find.textContaining('3 entries'), findsNothing);
    });

    testWidgets('allows individual entry selection', (WidgetTester tester) async {
      List<TimeEntry> selectedEntries = [];

      await tester.pumpWidget(createTestWidget(
        onSelectionChanged: (entries) => selectedEntries = entries,
      ));
      await tester.pumpAndSettle();

      // Project Beta should be expanded by default, so its entry checkbox should be visible
      // Tap the entry checkbox for Project Beta (should be the second checkbox)
      final checkboxes = find.byType(Checkbox);
      await tester.tap(checkboxes.at(1)); // Skip project checkbox, tap entry checkbox
      await tester.pumpAndSettle();

      expect(selectedEntries.length, 1);
      expect(selectedEntries.first.id, 'entry3'); // entry3 is the Project Beta entry
    });

    testWidgets('allows project-level selection', (WidgetTester tester) async {
      List<TimeEntry> selectedEntries = [];

      await tester.pumpWidget(createTestWidget(
        onSelectionChanged: (entries) => selectedEntries = entries,
      ));
      await tester.pumpAndSettle();

      // Debug: Print checkbox layout
      final checkboxes = find.byType(Checkbox);
      print('Total checkboxes: ${checkboxes.evaluate().length}');
      
      // Project Alpha's checkbox should be at index 2 (0: Project Beta, 1: Project Beta entry, 2: Project Alpha)
      await tester.tap(checkboxes.at(2)); // Project Alpha's checkbox
      await tester.pumpAndSettle();

      print('Selected entries count: ${selectedEntries.length}');
      for (var entry in selectedEntries) {
        print('Selected entry: ${entry.id} (project: ${entry.projectId})');
      }

      // Should select all entries from Project Alpha (2 completed entries)
      expect(selectedEntries.length, 2);
      expect(selectedEntries.every((e) => e.projectId == 'project1'), true);
    });

    testWidgets('displays selection summary', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show summary card
      expect(find.text('Selection Summary'), findsOneWidget);
      expect(find.text('Selected Entries: 0'), findsOneWidget);
      expect(find.text('Total Hours: 0.00'), findsOneWidget);
    });

    testWidgets('updates summary when entries are selected', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Expand Project Beta and select its entry (3.5 hours)
      await tester.tap(find.text('Project Beta'));
      await tester.pumpAndSettle();

      final checkboxes = find.byType(Checkbox);
      await tester.tap(checkboxes.at(1)); // Project Beta's entry checkbox
      await tester.pumpAndSettle();

      expect(find.text('Selected Entries: 1'), findsOneWidget);
      expect(find.text('Total Hours: 3.50'), findsOneWidget);
      expect(find.textContaining('Estimated Total: \$175.00'), findsOneWidget);
    });

    testWidgets('shows expandable project sections', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Initially, project entries should be expanded by default
      expect(find.byIcon(Icons.expand_less), findsAtLeastNWidgets(1));

      // Tap to collapse
      await tester.tap(find.byIcon(Icons.expand_less).first);
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.expand_more), findsAtLeastNWidgets(1));
    });

    testWidgets('displays filter bar', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Filters'), findsOneWidget);
      expect(find.text('All Dates'), findsOneWidget);
      expect(find.text('Unbilled Only'), findsOneWidget);
    });

    testWidgets('handles project filter', (WidgetTester tester) async {
      when(mockDatabaseService.getTimeEntries(
        projectId: 'project1',
        startDate: anyNamed('startDate'),
        endDate: anyNamed('endDate'),
      )).thenAnswer((_) async => testTimeEntries.where((e) => e.projectId == 'project1').toList());

      await tester.pumpWidget(createTestWidget(projectFilter: 'project1'));
      await tester.pumpAndSettle();

      // Should only show Project Alpha
      expect(find.text('Project Alpha'), findsOneWidget);
      expect(find.text('Project Beta'), findsNothing);
    });

    testWidgets('handles date range filter', (WidgetTester tester) async {
      final dateRange = DateTimeRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 16),
      );

      await tester.pumpWidget(createTestWidget(dateRange: dateRange));
      await tester.pumpAndSettle();

      // Should show date range in filter
      expect(find.text('Jan 15, 2024 - Jan 16, 2024'), findsOneWidget);
    });

    testWidgets('shows empty state when no entries found', (WidgetTester tester) async {
      when(mockDatabaseService.getTimeEntries(
        projectId: anyNamed('projectId'),
        startDate: anyNamed('startDate'),
        endDate: anyNamed('endDate'),
      )).thenAnswer((_) async => []);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('No time entries found'), findsOneWidget);
      expect(find.text('Try adjusting your filters or add some time entries first.'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('displays project icons and colors', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show project icons
      expect(find.byIcon(Icons.work), findsOneWidget);
      expect(find.byIcon(Icons.code), findsOneWidget);
    });

    testWidgets('handles initial selection', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        initialSelectedIds: ['entry1', 'entry2'],
      ));
      await tester.pumpAndSettle();

      // Should show 2 selected entries in summary
      expect(find.text('Selected Entries: 2'), findsOneWidget);
      expect(find.text('Total Hours: 5.50'), findsOneWidget); // 3h + 2.5h
    });

    testWidgets('shows correct duration formats', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Project Alpha should be expanded by default, but let's check if we need to expand it
      // If it's collapsed, the expand_more icon will be visible
      if (find.byIcon(Icons.expand_more).evaluate().isNotEmpty) {
        // Find the expand button for Project Alpha and tap it
        final expandButtons = find.byIcon(Icons.expand_more);
        // Project Alpha is the second project, so its expand button should be the second one
        if (expandButtons.evaluate().length > 1) {
          await tester.tap(expandButtons.at(1));
        } else {
          await tester.tap(expandButtons.first);
        }
        await tester.pumpAndSettle();
      }

      // Should show time range for entry1 (Jan 15)
      expect(find.text('09:00 - 12:00'), findsOneWidget);

      // Should show duration for entry2 (Jan 16)
      expect(find.textContaining('Duration: 2:30'), findsOneWidget);
    });

    testWidgets('calculates project totals correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Project Alpha should show total hours for its 2 completed entries
      expect(find.textContaining('5.5h total'), findsOneWidget); // 3h + 2.5h

      // Project Beta should show total for its 1 entry
      expect(find.textContaining('3.5h total'), findsOneWidget); // 14:00-17:30 = 3.5h
    });

    testWidgets('handles tristate checkbox for partial project selection', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Expand Project Alpha
      await tester.tap(find.text('Project Alpha'));
      await tester.pumpAndSettle();

      // Select only one entry from Project Alpha
      final checkboxes = find.byType(Checkbox);
      await tester.tap(checkboxes.at(1)); // First entry
      await tester.pumpAndSettle();

      // Project checkbox should be in indeterminate state (tristate)
      final projectCheckbox = tester.widget<Checkbox>(checkboxes.first);
      expect(projectCheckbox.tristate, true);
    });
  });
}
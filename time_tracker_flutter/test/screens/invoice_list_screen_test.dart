import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/screens/invoice_list_screen.dart';

void main() {
  group('InvoiceListScreen Widget Tests', () {
    testWidgets('renders invoice list screen with basic UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      // Verify basic UI elements are present
      expect(find.text('Invoices'), findsOneWidget); // App bar title
      expect(find.byType(TextField), findsOneWidget); // Search field
      expect(find.byType(FloatingActionButton), findsOneWidget); // FAB
      expect(find.byIcon(Icons.filter_list), findsOneWidget); // Filter button
    });

    testWidgets('search field accepts input', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      // Find the search field and enter text
      final searchField = find.byType(TextField);
      await tester.enterText(searchField, 'test search');
      
      // Verify the text was entered
      expect(find.text('test search'), findsOneWidget);
    });

    testWidgets('filter button toggles filter panel', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Initially filters should be hidden
      expect(find.text('Filters'), findsNothing);

      // Tap filter button
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();

      // Filters should now be visible
      expect(find.text('Filters'), findsOneWidget);
      expect(find.text('Draft'), findsOneWidget);
      expect(find.text('Sent'), findsOneWidget);
      expect(find.text('Paid'), findsOneWidget);
      expect(find.text('Overdue'), findsOneWidget);
      expect(find.text('Cancelled'), findsOneWidget);
    });

    testWidgets('floating action button is present and tappable', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the floating action button
      final fab = find.byType(FloatingActionButton);
      expect(fab, findsOneWidget);
      
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // The FAB should be tappable (no exceptions thrown)
    });

    testWidgets('app bar contains correct title and actions', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      // Verify app bar elements
      expect(find.text('Invoices'), findsOneWidget);
      expect(find.byIcon(Icons.filter_list), findsOneWidget);
    });

    testWidgets('search field has correct hint text', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: InvoiceListScreen(),
        ),
      );

      // Verify search field hint text
      expect(find.text('Search invoices...'), findsOneWidget);
    });
  });
}
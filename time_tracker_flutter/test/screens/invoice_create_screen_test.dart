import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/invoice_create_screen.dart';

void main() {
  group('InvoiceCreateScreen', () {
    testWidgets('displays create invoice title for new invoice', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      expect(find.text('Create Invoice'), findsOneWidget);
    });

    testWidgets('displays edit invoice title for existing invoice', (tester) async {
      final testInvoice = Invoice(
        id: 'test-invoice',
        invoiceNumber: 'INV-001',
        clientId: 'client1',
        timeEntryIds: [],
        additionalItems: [],
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime.now(),
        status: InvoiceStatus.draft,
        subtotal: 100.0,
        taxRate: 0.0,
        taxAmount: 0.0,
        total: 100.0,
        createdAt: DateTime.now().toIso8601String(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: InvoiceCreateScreen(invoice: testInvoice),
        ),
      );

      expect(find.text('Edit Invoice'), findsOneWidget);
    });

    testWidgets('shows loading indicator initially', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Should show loading indicator while initializing
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows step indicator with progress bar', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Should show progress indicator in app bar
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('has proper app bar structure', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Should have app bar with title
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Create Invoice'), findsOneWidget);
    });

    testWidgets('initializes with provided client', (tester) async {
      final testClient = Client(
        id: 'client1',
        name: 'Test Client',
        email: '<EMAIL>',
        createdAt: DateTime.now().toIso8601String(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: InvoiceCreateScreen(initialClient: testClient),
        ),
      );

      // Widget should be created without errors
      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });

    testWidgets('initializes with provided time entries', (tester) async {
      final testTimeEntry = TimeEntry(
        id: 'entry1',
        projectId: 'project1',
        date: DateTime.now().toIso8601String().split('T')[0],
        start: '09:00',
        end: '11:00',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: InvoiceCreateScreen(
            initialTimeEntries: [testTimeEntry],
          ),
        ),
      );

      // Widget should be created without errors
      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });

    testWidgets('has form structure', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Should have a form widget
      expect(find.byType(Form), findsOneWidget);
    });

    testWidgets('has page view for steps', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Should have a PageView for step navigation
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('shows navigation buttons area', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Should have navigation buttons (Next button should be present)
      expect(find.text('Next'), findsOneWidget);
    });

    testWidgets('creates invoice with all required parameters', (tester) async {
      final testClient = Client(
        id: 'client1',
        name: 'Test Client',
        email: '<EMAIL>',
        createdAt: DateTime.now().toIso8601String(),
      );

      final testTimeEntry = TimeEntry(
        id: 'entry1',
        projectId: 'project1',
        date: DateTime.now().toIso8601String().split('T')[0],
        start: '09:00',
        end: '11:00',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: InvoiceCreateScreen(
            initialClient: testClient,
            initialTimeEntries: [testTimeEntry],
          ),
        ),
      );

      // Widget should initialize properly with all parameters
      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
      expect(find.text('Create Invoice'), findsOneWidget);
    });
  });

  group('InvoiceCreateScreen Widget Structure', () {
    testWidgets('has proper scaffold structure', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('disposes controllers properly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Navigate away to trigger dispose
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: Text('Different Screen')),
        ),
      );

      // Should not throw any errors during disposal
      expect(find.text('Different Screen'), findsOneWidget);
    });

    testWidgets('handles invoice editing mode', (tester) async {
      final testInvoice = Invoice(
        id: 'test-invoice',
        invoiceNumber: 'INV-001',
        clientId: 'client1',
        timeEntryIds: ['entry1'],
        additionalItems: [
          InvoiceLineItem(
            description: 'Test Item',
            quantity: 1.0,
            rate: 50.0,
            type: InvoiceLineItemType.expense,
          ),
        ],
        currency: 'EUR',
        locale: 'de_DE',
        issueDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: InvoiceStatus.draft,
        subtotal: 50.0,
        taxRate: 19.0,
        taxAmount: 9.5,
        total: 59.5,
        notes: 'Test notes',
        createdAt: DateTime.now().toIso8601String(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: InvoiceCreateScreen(invoice: testInvoice),
        ),
      );

      expect(find.text('Edit Invoice'), findsOneWidget);
      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });

    testWidgets('shows tab controller for preview mode', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(),
        ),
      );

      // Should have tab controller (used for preview functionality)
      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });
  });

  group('InvoiceCreateScreen Error Handling', () {
    testWidgets('handles null invoice gracefully', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(invoice: null),
        ),
      );

      expect(find.text('Create Invoice'), findsOneWidget);
    });

    testWidgets('handles empty time entries list', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(initialTimeEntries: []),
        ),
      );

      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });

    testWidgets('handles null client', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const InvoiceCreateScreen(initialClient: null),
        ),
      );

      expect(find.byType(InvoiceCreateScreen), findsOneWidget);
    });
  });
}
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/screens/invoice_detail_screen.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/services/invoice_service.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';

import 'invoice_detail_screen_test.mocks.dart';

void main() {
  group('InvoiceDetailScreen Basic Tests', () {
    late MockDatabaseService mockDatabaseService;
    late MockClientService mockClientService;
    late MockInvoiceService mockInvoiceService;
    late MockPDFService mockPdfService;

    late Invoice testInvoice;
    late Client testClient;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      mockClientService = MockClientService();
      mockInvoiceService = MockInvoiceService();
      mockPdfService = MockPDFService();

      // Create test data
      testClient = Client(
        id: 'client-1',
        name: 'Test Client',
        email: '<EMAIL>',
      );

      testInvoice = Invoice(
        id: 'invoice-1',
        invoiceNumber: 'INV-2024-001',
        clientId: testClient.id,
        additionalItems: [
          InvoiceLineItem(
            id: 'item-1',
            description: 'Web Development',
            quantity: 10.0,
            rate: 100.0,
            amount: 1000.0,
            type: InvoiceLineItemType.timeEntry,
          ),
        ],
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime(2024, 1, 15),
        status: InvoiceStatus.sent,
        subtotal: 1000.0,
        taxRate: 10.0,
        taxAmount: 100.0,
        total: 1100.0,
      );
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: InvoiceDetailScreen(
          invoiceId: testInvoice.id,
          databaseService: mockDatabaseService,
          clientService: mockClientService,
          invoiceService: mockInvoiceService,
          pdfService: mockPdfService,
        ),
      );
    }

    testWidgets('displays loading indicator initially', (WidgetTester tester) async {
      // Setup mocks to return data immediately but we check before pumping
      when(mockDatabaseService.getInvoice(testInvoice.id))
          .thenAnswer((_) async => testInvoice);
      when(mockClientService.getClient(testClient.id))
          .thenAnswer((_) async => testClient);

      await tester.pumpWidget(createTestWidget());

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Invoice Details'), findsOneWidget);
    });

    testWidgets('displays basic invoice information when loaded', (WidgetTester tester) async {
      // Setup successful mocks
      when(mockDatabaseService.getInvoice(testInvoice.id))
          .thenAnswer((_) async => testInvoice);
      when(mockClientService.getClient(testClient.id))
          .thenAnswer((_) async => testClient);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify basic elements are present
      expect(find.textContaining('Invoice'), findsWidgets);
      expect(find.textContaining('INV-2024-001'), findsWidgets);
      expect(find.text(testClient.name), findsOneWidget);
    });

    testWidgets('shows error when invoice not found', (WidgetTester tester) async {
      // Setup mock to return null
      when(mockDatabaseService.getInvoice(testInvoice.id))
          .thenAnswer((_) async => null);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Invoice not found'), findsOneWidget);
    });

    testWidgets('shows error when client not found', (WidgetTester tester) async {
      // Setup mocks
      when(mockDatabaseService.getInvoice(testInvoice.id))
          .thenAnswer((_) async => testInvoice);
      when(mockClientService.getClient(testClient.id))
          .thenAnswer((_) async => null);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Invoice not found'), findsOneWidget);
    });

    testWidgets('displays status chip', (WidgetTester tester) async {
      // Setup successful mocks
      when(mockDatabaseService.getInvoice(testInvoice.id))
          .thenAnswer((_) async => testInvoice);
      when(mockClientService.getClient(testClient.id))
          .thenAnswer((_) async => testClient);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify status chip is displayed
      expect(find.text('Sent'), findsOneWidget);
    });
  });
}
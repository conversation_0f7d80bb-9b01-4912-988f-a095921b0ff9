import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/screens/client_management_screen.dart';
import 'package:time_tracker_flutter/services/client_service.dart';

import 'client_management_screen_test.mocks.dart';

@GenerateMocks([ClientService])
void main() {
  group('ClientManagementScreen Widget Tests', () {
    late MockClientService mockClientService;
    late List<Client> testClients;
    late ClientStatistics testStatistics;

    setUp(() {
      mockClientService = MockClientService();
      
      testClients = [
        Client(
          id: '1',
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: Address(
            street: '123 Main St',
            city: 'Test City',
            postalCode: '12345',
            country: 'Test Country',
          ),
        ),
        Client(
          id: '2',
          name: '<PERSON>',
          email: '<EMAIL>',
        ),
        Client(
          id: '3',
          name: '<PERSON>',
          phone: '+0987654321',
        ),
      ];

      testStatistics = const ClientStatistics(
        totalClients: 3,
        clientsWithInvoices: 1,
        clientsWithoutInvoices: 2,
        clientsWithEmail: 2,
        clientsWithPhone: 2,
        clientsWithAddress: 1,
        clientsWithTaxId: 0,
      );

      // Setup default mock responses
      when(mockClientService.getClients())
          .thenAnswer((_) async => testClients);
      when(mockClientService.getClientStatistics())
          .thenAnswer((_) async => testStatistics);
      when(mockClientService.canDeleteClient(any))
          .thenAnswer((_) async => true);
      when(mockClientService.deleteClient(any))
          .thenAnswer((_) async {});
    });

    Widget createWidget() {
      return MaterialApp(
        home: ClientManagementScreen(
          clientService: mockClientService,
        ),
      );
    }

    testWidgets('displays screen correctly', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('Client Management'), findsOneWidget);
      expect(find.byIcon(Icons.filter_list), findsOneWidget);
      expect(find.byIcon(Icons.add), findsAtLeastNWidgets(1));
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });

    testWidgets('displays client statistics', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('Client Statistics'), findsOneWidget);
      expect(find.text('3'), findsOneWidget); // Total clients
      expect(find.text('2'), findsAtLeastNWidgets(1)); // With email and phone
      expect(find.text('1'), findsOneWidget); // With address
    });

    testWidgets('displays client list', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('+1234567890'), findsOneWidget);
    });

    testWidgets('shows empty state when no clients', (tester) async {
      when(mockClientService.getClients())
          .thenAnswer((_) async => []);
      when(mockClientService.getClientStatistics())
          .thenAnswer((_) async => const ClientStatistics(
            totalClients: 0,
            clientsWithInvoices: 0,
            clientsWithoutInvoices: 0,
            clientsWithEmail: 0,
            clientsWithPhone: 0,
            clientsWithAddress: 0,
            clientsWithTaxId: 0,
          ));

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('No clients yet'), findsOneWidget);
      expect(find.text('Add your first client to get started'), findsOneWidget);
      expect(find.text('Add Client'), findsOneWidget);
    });

    testWidgets('filters clients by search query', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Enter search query
      await tester.enterText(find.byType(TextField), 'John');
      await tester.pumpAndSettle();

      // Should show filtered results
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsOneWidget);
      expect(find.text('Jane Smith'), findsNothing);
    });

    testWidgets('clears search when clear button is tapped', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Enter search query
      await tester.enterText(find.byType(TextField), 'John');
      await tester.pumpAndSettle();

      // Tap clear button
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pumpAndSettle();

      // All clients should be visible again
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsOneWidget);
    });

    testWidgets('shows filter menu when filter button is tapped', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap filter button
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();

      // Should show filter options
      expect(find.text('Filter Clients'), findsOneWidget);
      expect(find.text('All Clients'), findsOneWidget);
      expect(find.text('With Email'), findsOneWidget);
      expect(find.text('With Phone'), findsOneWidget);
      expect(find.text('With Address'), findsOneWidget);
    });

    testWidgets('applies filter when selected', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Open filter menu
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();

      // Select "With Email" filter
      await tester.tap(find.text('With Email'));
      await tester.pumpAndSettle();

      // Should show filter chip
      expect(find.text('With Email'), findsOneWidget);
      
      // Should show only clients with email
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsNothing);
    });

    testWidgets('removes filter when chip is deleted', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Apply a filter first
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();
      await tester.tap(find.text('With Email'));
      await tester.pumpAndSettle();

      // Delete the filter chip
      await tester.tap(find.byIcon(Icons.cancel));
      await tester.pumpAndSettle();

      // All clients should be visible again
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Bob Johnson'), findsOneWidget);
    });

    testWidgets('shows client popup menu', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap popup menu button for first client
      await tester.tap(find.byType(PopupMenuButton<String>).first);
      await tester.pumpAndSettle();

      expect(find.text('Edit'), findsOneWidget);
      expect(find.text('Delete'), findsOneWidget);
    });

    testWidgets('opens edit dialog when edit is selected', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap on a client card to edit
      await tester.tap(find.text('John Doe'));
      await tester.pumpAndSettle();

      expect(find.text('Edit Client'), findsOneWidget);
    });

    testWidgets('shows delete confirmation dialog', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Open popup menu and select delete
      await tester.tap(find.byType(PopupMenuButton<String>).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      expect(find.text('Delete Client'), findsOneWidget);
      expect(find.text('Are you sure you want to delete "John Doe"?'), findsOneWidget);
    });

    testWidgets('deletes client when confirmed', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Open popup menu and select delete
      await tester.tap(find.byType(PopupMenuButton<String>).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Confirm deletion
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      verify(mockClientService.deleteClient('1')).called(1);
    });

    testWidgets('prevents deletion of client with invoices', (tester) async {
      when(mockClientService.canDeleteClient('1'))
          .thenAnswer((_) async => false);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Try to delete client
      await tester.tap(find.byType(PopupMenuButton<String>).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text('Delete'));
      await tester.pumpAndSettle();

      // Should show error message instead of confirmation dialog
      expect(find.text('Delete Client'), findsNothing);
      verifyNever(mockClientService.deleteClient(any));
    });

    testWidgets('opens add client dialog when FAB is tapped', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap floating action button
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      expect(find.text('Add Client'), findsOneWidget);
    });

    testWidgets('opens add client dialog when app bar add button is tapped', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap app bar add button
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      expect(find.text('Add Client'), findsOneWidget);
    });

    testWidgets('shows loading state initially', (tester) async {
      // Make the service calls take time
      when(mockClientService.getClients()).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return testClients;
      });
      when(mockClientService.getClientStatistics()).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return testStatistics;
      });

      await tester.pumpWidget(createWidget());
      await tester.pump(); // Don't use pumpAndSettle to catch loading state

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays client avatars with initials', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Should show client initials in avatars
      expect(find.text('J'), findsAtLeastNWidgets(2)); // John and Jane
      expect(find.text('B'), findsOneWidget); // Bob
    });

    testWidgets('shows client contact information', (tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Should show email and phone icons with information
      expect(find.byIcon(Icons.email), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.phone), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.location_on), findsOneWidget);
      expect(find.text('Test City, Test Country'), findsOneWidget);
    });
  });
}
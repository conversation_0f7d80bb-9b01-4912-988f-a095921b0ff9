import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_controller.dart';

void main() {
  group('InvoiceCreateController Service Period', () {
    late InvoiceCreateController controller;

    setUp(() {
      controller = InvoiceCreateController(
        invoice: null,
        initialClient: null,
        initialTimeEntries: null,
        vsync: TestVSync(),
      );
    });

    tearDown(() {
      controller.dispose();
    });

    test('should initialize with service period disabled', () {
      expect(controller.servicePeriodEnabled, isFalse);
      expect(controller.servicePeriodStart, isNull);
      expect(controller.servicePeriodEnd, isNull);
    });

    test('should enable service period', () {
      controller.setServicePeriodEnabled(true);
      
      expect(controller.servicePeriodEnabled, isTrue);
    });

    test('should disable service period and clear dates', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      controller.setServicePeriodEnabled(true);
      controller.setServicePeriodStart(startDate);
      controller.setServicePeriodEnd(endDate);
      
      expect(controller.servicePeriodStart, equals(startDate));
      expect(controller.servicePeriodEnd, equals(endDate));
      
      controller.setServicePeriodEnabled(false);
      
      expect(controller.servicePeriodEnabled, isFalse);
      expect(controller.servicePeriodStart, isNull);
      expect(controller.servicePeriodEnd, isNull);
    });

    test('should set service period dates', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      controller.setServicePeriodStart(startDate);
      controller.setServicePeriodEnd(endDate);
      
      expect(controller.servicePeriodStart, equals(startDate));
      expect(controller.servicePeriodEnd, equals(endDate));
    });

    // Note: These tests would require database initialization which is complex for unit tests.
    // The service period functionality is tested through the Invoice model tests and widget tests.
  });
}

class TestVSync implements TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}
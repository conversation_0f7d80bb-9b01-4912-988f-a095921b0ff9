import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/invoice_service.dart';

// Manual mock for DatabaseService
class MockDatabaseService extends Mock implements DatabaseService {}

void main() {
  group('InvoiceService', () {
    late InvoiceService invoiceService;
    late MockDatabaseService mockDatabaseService;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      invoiceService = InvoiceService(databaseService: mockDatabaseService);
    });

    group('createInvoiceFromTimeEntries', () {
      test('should create invoice from time entries with duration', () async {
        // Arrange
        final project = Project(
          id: 'project1',
          name: 'Test Project',
        );

        final timeEntries = [
          TimeEntry(
            id: 'entry1',
            projectId: 'project1',
            date: '2024-01-15',
            duration: '02:30', // 2.5 hours
          ),
          TimeEntry(
            id: 'entry2',
            projectId: 'project1',
            date: '2024-01-16',
            duration: '01:30', // 1.5 hours
          ),
        ];

        when(mockDatabaseService.getProject('project1'))
            .thenAnswer((_) async => project);
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoice = await invoiceService.createInvoiceFromTimeEntries(
          timeEntries: timeEntries,
          clientId: 'client1',
          currency: 'USD',
          locale: 'en_US',
          taxRate: 10.0,
        );

        // Assert
        expect(invoice.clientId, equals('client1'));
        expect(invoice.currency, equals('USD'));
        expect(invoice.locale, equals('en_US'));
        expect(invoice.taxRate, equals(10.0));
        expect(invoice.timeEntryIds, hasLength(2));
        expect(invoice.additionalItems, hasLength(1));

        final lineItem = invoice.additionalItems.first;
        expect(lineItem.description, equals('Test Project - Time tracking'));
        expect(lineItem.quantity, equals(4.0)); // 2.5 + 1.5 hours
        expect(lineItem.rate, equals(50.0));
        expect(lineItem.amount, equals(200.0)); // 4 * 50
        expect(lineItem.type, equals(InvoiceLineItemType.timeEntry));

        expect(invoice.subtotal, equals(200.0));
        expect(invoice.taxAmount, equals(20.0)); // 10% of 200
        expect(invoice.total, equals(220.0)); // 200 + 20
      });

      test('should create invoice from time entries with start/end times', () async {
        // Arrange
        final project = Project(
          id: 'project1',
          name: 'Test Project',
        );

        final timeEntries = [
          TimeEntry(
            id: 'entry1',
            projectId: 'project1',
            date: '2024-01-15',
            start: '09:00',
            end: '12:00', // 3 hours
          ),
        ];

        when(mockDatabaseService.getProject('project1'))
            .thenAnswer((_) async => project);
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoice = await invoiceService.createInvoiceFromTimeEntries(
          timeEntries: timeEntries,
          clientId: 'client1',
          currency: 'EUR',
          locale: 'de_DE',
        );

        // Assert
        expect(invoice.currency, equals('EUR'));
        expect(invoice.locale, equals('de_DE'));
        expect(invoice.additionalItems, hasLength(1));

        final lineItem = invoice.additionalItems.first;
        expect(lineItem.quantity, equals(3.0)); // 3 hours
        expect(lineItem.rate, equals(50.0));
        expect(lineItem.amount, equals(150.0)); // 3 * 50

        expect(invoice.subtotal, equals(150.0));
        expect(invoice.taxAmount, equals(0.0)); // No tax
        expect(invoice.total, equals(150.0));
      });

      test('should use custom rates when provided', () async {
        // Arrange
        final project = Project(
          id: 'project1',
          name: 'Test Project',
        );

        final timeEntries = [
          TimeEntry(
            id: 'entry1',
            projectId: 'project1',
            date: '2024-01-15',
            duration: '02:00', // 2 hours
          ),
        ];

        when(mockDatabaseService.getProject('project1'))
            .thenAnswer((_) async => project);
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoice = await invoiceService.createInvoiceFromTimeEntries(
          timeEntries: timeEntries,
          clientId: 'client1',
          currency: 'USD',
          locale: 'en_US',
          customRates: {'project1': 100.0}, // Custom rate
        );

        // Assert
        final lineItem = invoice.additionalItems.first;
        expect(lineItem.rate, equals(100.0)); // Custom rate used
        expect(lineItem.amount, equals(200.0)); // 2 * 100
      });

      test('should handle multiple projects', () async {
        // Arrange
        final project1 = Project(
          id: 'project1',
          name: 'Project One',
        );
        final project2 = Project(
          id: 'project2',
          name: 'Project Two',
        );

        final timeEntries = [
          TimeEntry(
            id: 'entry1',
            projectId: 'project1',
            date: '2024-01-15',
            duration: '02:00', // 2 hours
          ),
          TimeEntry(
            id: 'entry2',
            projectId: 'project2',
            date: '2024-01-15',
            duration: '01:00', // 1 hour
          ),
        ];

        when(mockDatabaseService.getProject('project1'))
            .thenAnswer((_) async => project1);
        when(mockDatabaseService.getProject('project2'))
            .thenAnswer((_) async => project2);
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoice = await invoiceService.createInvoiceFromTimeEntries(
          timeEntries: timeEntries,
          clientId: 'client1',
          currency: 'USD',
          locale: 'en_US',
        );

        // Assert
        expect(invoice.additionalItems, hasLength(2));

        final lineItem1 = invoice.additionalItems
            .firstWhere((item) => item.description.contains('Project One'));
        expect(lineItem1.quantity, equals(2.0));
        expect(lineItem1.rate, equals(50.0));
        expect(lineItem1.amount, equals(100.0));

        final lineItem2 = invoice.additionalItems
            .firstWhere((item) => item.description.contains('Project Two'));
        expect(lineItem2.quantity, equals(1.0));
        expect(lineItem2.rate, equals(50.0));
        expect(lineItem2.amount, equals(50.0));

        expect(invoice.subtotal, equals(150.0)); // 100 + 50
      });

      test('should throw error when no time entries provided', () async {
        // Act & Assert
        expect(
          () => invoiceService.createInvoiceFromTimeEntries(
            timeEntries: [],
            clientId: 'client1',
            currency: 'USD',
            locale: 'en_US',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should throw error when project not found', () async {
        // Arrange
        final timeEntries = [
          TimeEntry(
            id: 'entry1',
            projectId: 'nonexistent',
            date: '2024-01-15',
            duration: '02:00',
          ),
        ];

        when(mockDatabaseService.getProject('nonexistent'))
            .thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => invoiceService.createInvoiceFromTimeEntries(
            timeEntries: timeEntries,
            clientId: 'client1',
            currency: 'USD',
            locale: 'en_US',
          ),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('generateInvoiceNumber', () {
      test('should generate invoice number with default format', () async {
        // Arrange
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoiceNumber = await invoiceService.generateInvoiceNumber();

        // Assert
        expect(invoiceNumber, matches(r'^INV-\d{6}-0001$'));
      });

      test('should increment sequence for existing invoices', () async {
        // Arrange
        final now = DateTime.now();
        final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}';

        final existingInvoices = [
          Invoice(
            invoiceNumber: 'INV-$dateStr-0001',
            clientId: 'client1',
          ),
          Invoice(
            invoiceNumber: 'INV-$dateStr-0002',
            clientId: 'client1',
          ),
        ];

        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => existingInvoices);

        // Act
        final invoiceNumber = await invoiceService.generateInvoiceNumber();

        // Assert
        expect(invoiceNumber, equals('INV-$dateStr-0003'));
      });

      test('should generate custom format invoice number', () async {
        // Arrange
        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => []);

        // Act
        final invoiceNumber = await invoiceService.generateInvoiceNumber(
          prefix: 'BILL',
          dateFormat: 'yyyy',
          sequenceLength: 3,
        );

        // Assert
        expect(invoiceNumber, matches(r'^BILL-\d{4}-001$'));
      });
    });

    group('calculateInvoiceTotals', () {
      test('should calculate totals correctly', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [
            InvoiceLineItem(
              description: 'Item 1',
              quantity: 2.0,
              rate: 50.0,
              type: InvoiceLineItemType.timeEntry,
            ),
            InvoiceLineItem(
              description: 'Item 2',
              quantity: 1.5,
              rate: 100.0,
              type: InvoiceLineItemType.expense,
            ),
          ],
          taxRate: 8.5,
        );

        // Act
        invoiceService.calculateInvoiceTotals(invoice);

        // Assert
        expect(invoice.subtotal, equals(250.0)); // (2*50) + (1.5*100)
        expect(invoice.taxAmount, equals(21.25)); // 250 * 0.085
        expect(invoice.total, equals(271.25)); // 250 + 21.25
      });

      test('should handle zero tax rate', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [
            InvoiceLineItem(
              description: 'Item 1',
              quantity: 3.0,
              rate: 25.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
          taxRate: 0.0,
        );

        // Act
        invoiceService.calculateInvoiceTotals(invoice);

        // Assert
        expect(invoice.subtotal, equals(75.0));
        expect(invoice.taxAmount, equals(0.0));
        expect(invoice.total, equals(75.0));
      });

      test('should round to two decimal places', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [
            InvoiceLineItem(
              description: 'Item 1',
              quantity: 1.0,
              rate: 33.333,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
          taxRate: 7.25,
        );

        // Act
        invoiceService.calculateInvoiceTotals(invoice);

        // Assert
        expect(invoice.subtotal, equals(33.33));
        expect(invoice.taxAmount, equals(2.42)); // 33.33 * 0.0725 = 2.41642, rounded to 2.42
        expect(invoice.total, equals(35.75)); // 33.33 + 2.42
      });
    });

    group('updateInvoiceStatus', () {
      test('should update invoice status', () async {
        // Arrange
        final invoice = Invoice(
          id: 'invoice1',
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          status: InvoiceStatus.draft,
        );

        when(mockDatabaseService.getInvoice('invoice1'))
            .thenAnswer((_) async => invoice);
        when(mockDatabaseService.saveInvoice(any))
            .thenAnswer((_) async {});

        // Act
        await invoiceService.updateInvoiceStatus('invoice1', InvoiceStatus.sent);

        // Assert
        verify(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) => inv.status == InvoiceStatus.sent))
        )).called(1);
      });

      test('should throw error when invoice not found', () async {
        // Arrange
        when(mockDatabaseService.getInvoice('nonexistent'))
            .thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => invoiceService.updateInvoiceStatus('nonexistent', InvoiceStatus.sent),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('getInvoicesByDateRange', () {
      test('should filter invoices by date range', () async {
        // Arrange
        final invoices = [
          Invoice(
            invoiceNumber: 'INV-001',
            clientId: 'client1',
            issueDate: DateTime(2024, 1, 15),
          ),
          Invoice(
            invoiceNumber: 'INV-002',
            clientId: 'client1',
            issueDate: DateTime(2024, 2, 15),
          ),
          Invoice(
            invoiceNumber: 'INV-003',
            clientId: 'client1',
            issueDate: DateTime(2024, 3, 15),
          ),
        ];

        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => invoices);

        // Act
        final result = await invoiceService.getInvoicesByDateRange(
          DateTime(2024, 1, 1),
          DateTime(2024, 2, 28),
        );

        // Assert
        expect(result, hasLength(2));
        expect(result.map((i) => i.invoiceNumber), containsAll(['INV-001', 'INV-002']));
      });
    });

    group('getInvoicesByClient', () {
      test('should filter invoices by client', () async {
        // Arrange
        final invoices = [
          Invoice(
            invoiceNumber: 'INV-001',
            clientId: 'client1',
          ),
          Invoice(
            invoiceNumber: 'INV-002',
            clientId: 'client2',
          ),
          Invoice(
            invoiceNumber: 'INV-003',
            clientId: 'client1',
          ),
        ];

        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => invoices);

        // Act
        final result = await invoiceService.getInvoicesByClient('client1');

        // Assert
        expect(result, hasLength(2));
        expect(result.map((i) => i.invoiceNumber), containsAll(['INV-001', 'INV-003']));
      });
    });

    group('getInvoicesByStatus', () {
      test('should filter invoices by status', () async {
        // Arrange
        final invoices = [
          Invoice(
            invoiceNumber: 'INV-001',
            clientId: 'client1',
            status: InvoiceStatus.draft,
          ),
          Invoice(
            invoiceNumber: 'INV-002',
            clientId: 'client1',
            status: InvoiceStatus.sent,
          ),
          Invoice(
            invoiceNumber: 'INV-003',
            clientId: 'client1',
            status: InvoiceStatus.draft,
          ),
        ];

        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => invoices);

        // Act
        final result = await invoiceService.getInvoicesByStatus(InvoiceStatus.draft);

        // Assert
        expect(result, hasLength(2));
        expect(result.map((i) => i.invoiceNumber), containsAll(['INV-001', 'INV-003']));
      });
    });

    group('line item management', () {
      test('should add line item to invoice', () async {
        // Arrange
        final invoice = Invoice(
          id: 'invoice1',
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [],
        );

        final lineItem = InvoiceLineItem(
          description: 'New item',
          quantity: 1.0,
          rate: 100.0,
          type: InvoiceLineItemType.expense,
        );

        when(mockDatabaseService.getInvoice('invoice1'))
            .thenAnswer((_) async => invoice);
        when(mockDatabaseService.saveInvoice(any))
            .thenAnswer((_) async {});

        // Act
        await invoiceService.addLineItemToInvoice('invoice1', lineItem);

        // Assert
        verify(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) =>
            inv.additionalItems.length == 1 &&
            inv.additionalItems.first.description == 'New item'
          ))
        )).called(1);
      });

      test('should remove line item from invoice', () async {
        // Arrange
        final lineItem = InvoiceLineItem(
          id: 'item1',
          description: 'Item to remove',
          quantity: 1.0,
          rate: 100.0,
          type: InvoiceLineItemType.expense,
        );

        final invoice = Invoice(
          id: 'invoice1',
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [lineItem],
        );

        when(mockDatabaseService.getInvoice('invoice1'))
            .thenAnswer((_) async => invoice);
        when(mockDatabaseService.saveInvoice(any))
            .thenAnswer((_) async {});

        // Act
        await invoiceService.removeLineItemFromInvoice('invoice1', 'item1');

        // Assert
        verify(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) => inv.additionalItems.isEmpty))
        )).called(1);
      });

      test('should update line item in invoice', () async {
        // Arrange
        final originalItem = InvoiceLineItem(
          id: 'item1',
          description: 'Original item',
          quantity: 1.0,
          rate: 100.0,
          type: InvoiceLineItemType.expense,
        );

        final updatedItem = InvoiceLineItem(
          id: 'item1',
          description: 'Updated item',
          quantity: 2.0,
          rate: 150.0,
          type: InvoiceLineItemType.expense,
        );

        final invoice = Invoice(
          id: 'invoice1',
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [originalItem],
        );

        when(mockDatabaseService.getInvoice('invoice1'))
            .thenAnswer((_) async => invoice);
        when(mockDatabaseService.saveInvoice(any))
            .thenAnswer((_) async {});

        // Act
        await invoiceService.updateLineItemInInvoice('invoice1', updatedItem);

        // Assert
        verify(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) =>
            inv.additionalItems.length == 1 &&
            inv.additionalItems.first.description == 'Updated item' &&
            inv.additionalItems.first.quantity == 2.0
          ))
        )).called(1);
      });
    });

    group('validateInvoice', () {
      test('should validate correct invoice', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [
            InvoiceLineItem(
              description: 'Valid item',
              quantity: 2.0,
              rate: 50.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
          taxRate: 10.0,
          subtotal: 100.0,
          taxAmount: 10.0,
          total: 110.0,
        );

        // Act
        final isValid = invoiceService.validateInvoice(invoice);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject invoice with invalid tax rate', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          taxRate: -5.0, // Invalid
        );

        // Act
        final isValid = invoiceService.validateInvoice(invoice);

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject invoice with incorrect totals', () {
        // Arrange
        final invoice = Invoice(
          invoiceNumber: 'INV-001',
          clientId: 'client1',
          additionalItems: [
            InvoiceLineItem(
              description: 'Item',
              quantity: 2.0,
              rate: 50.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
          taxRate: 10.0,
          subtotal: 100.0,
          taxAmount: 10.0,
          total: 200.0, // Incorrect total
        );

        // Act
        final isValid = invoiceService.validateInvoice(invoice);

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('updateOverdueInvoices', () {
      test('should mark overdue invoices', () async {
        // Arrange
        final pastDate = DateTime.now().subtract(const Duration(days: 30));
        final futureDate = DateTime.now().add(const Duration(days: 30));

        final invoices = [
          Invoice(
            id: 'invoice1',
            invoiceNumber: 'INV-001',
            clientId: 'client1',
            status: InvoiceStatus.sent,
            dueDate: pastDate, // Overdue
          ),
          Invoice(
            id: 'invoice2',
            invoiceNumber: 'INV-002',
            clientId: 'client1',
            status: InvoiceStatus.sent,
            dueDate: futureDate, // Not overdue
          ),
          Invoice(
            id: 'invoice3',
            invoiceNumber: 'INV-003',
            clientId: 'client1',
            status: InvoiceStatus.paid,
            dueDate: pastDate, // Paid, should not be marked overdue
          ),
        ];

        when(mockDatabaseService.getInvoices())
            .thenAnswer((_) async => invoices);
        when(mockDatabaseService.getInvoice('invoice1'))
            .thenAnswer((_) async => invoices[0]);
        when(mockDatabaseService.saveInvoice(any))
            .thenAnswer((_) async {});

        // Act
        await invoiceService.updateOverdueInvoices();

        // Assert
        verify(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) =>
            inv.id == 'invoice1' && inv.status == InvoiceStatus.overdue
          ))
        )).called(1);

        // Should not update invoice2 or invoice3
        verifyNever(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) => inv.id == 'invoice2'))
        ));
        verifyNever(mockDatabaseService.saveInvoice(
          argThat(predicate<Invoice>((inv) => inv.id == 'invoice3'))
        ));
      });
    });
  });
}
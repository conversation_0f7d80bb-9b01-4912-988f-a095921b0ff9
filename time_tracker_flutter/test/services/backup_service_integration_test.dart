import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/backup_data.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/models/location_area.dart';

void main() {
  group('Backup Data Integration Tests with Invoice Data', () {

    test('should serialize and deserialize backup data with invoice and client data', () {
      // Arrange - Create test data
      final client = Client(
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: Address(
          street: '123 Test St',
          city: 'Test City',
          postalCode: '12345',
          country: 'Test Country',
        ),
        taxId: 'TAX123',
        notes: 'Test client notes',
      );

      final project = Project(
        name: 'Test Project',
        colorHex: '#2196F3',
      );

      final timeEntry = TimeEntry(
        projectId: project.id,
        date: DateTime.now().toIso8601String().split('T')[0],
        start: DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        end: DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
      );

      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: client.id,
        timeEntryIds: [timeEntry.id],
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: InvoiceStatus.draft,
        subtotal: 50.0,
        taxRate: 10.0,
        taxAmount: 5.0,
        total: 55.0,
        notes: 'Test invoice notes',
        additionalItems: [
          InvoiceLineItem(
            description: 'Additional service',
            quantity: 1.0,
            rate: 25.0,
            type: InvoiceLineItemType.expense,
          ),
        ],
      );

      final locationArea = LocationArea(
        name: 'Test Area',
        projectId: project.id,
        centerLatitude: 40.7128,
        centerLongitude: -74.0060,
        radius: 100.0,
      );

      // Create backup data
      final backupData = BackupData(
        projects: [project],
        timeEntries: {project.id: [timeEntry]},
        locationAreas: [locationArea],
        invoices: [invoice],
        clients: [client],
        schemaVersion: 3,
      );

      // Act - Serialize to JSON and back
      final jsonData = backupData.toJson();
      final jsonString = jsonEncode(jsonData);
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredBackupData = BackupData.fromJson(decodedJson);

      // Assert - Verify all data is preserved
      expect(restoredBackupData.projects.length, equals(1));
      expect(restoredBackupData.clients.length, equals(1));
      expect(restoredBackupData.invoices.length, equals(1));
      expect(restoredBackupData.timeEntries.length, equals(1));
      expect(restoredBackupData.locationAreas.length, equals(1));

      // Verify client data
      final restoredClient = restoredBackupData.clients.first;
      expect(restoredClient.id, equals(client.id));
      expect(restoredClient.name, equals(client.name));
      expect(restoredClient.email, equals(client.email));
      expect(restoredClient.phone, equals(client.phone));
      expect(restoredClient.taxId, equals(client.taxId));
      expect(restoredClient.notes, equals(client.notes));
      expect(restoredClient.address?.street, equals(client.address?.street));
      expect(restoredClient.address?.city, equals(client.address?.city));

      // Verify invoice data
      final restoredInvoice = restoredBackupData.invoices.first;
      expect(restoredInvoice.id, equals(invoice.id));
      expect(restoredInvoice.invoiceNumber, equals(invoice.invoiceNumber));
      expect(restoredInvoice.clientId, equals(invoice.clientId));
      expect(restoredInvoice.timeEntryIds, equals(invoice.timeEntryIds));
      expect(restoredInvoice.currency, equals(invoice.currency));
      expect(restoredInvoice.locale, equals(invoice.locale));
      expect(restoredInvoice.status, equals(invoice.status));
      expect(restoredInvoice.subtotal, equals(invoice.subtotal));
      expect(restoredInvoice.taxRate, equals(invoice.taxRate));
      expect(restoredInvoice.taxAmount, equals(invoice.taxAmount));
      expect(restoredInvoice.total, equals(invoice.total));
      expect(restoredInvoice.notes, equals(invoice.notes));
      expect(restoredInvoice.additionalItems.length, equals(1));
      expect(restoredInvoice.additionalItems.first.description, equals('Additional service'));
      expect(restoredInvoice.additionalItems.first.type, equals(InvoiceLineItemType.expense));

      // Verify project data
      final restoredProject = restoredBackupData.projects.first;
      expect(restoredProject.id, equals(project.id));
      expect(restoredProject.name, equals(project.name));
      expect(restoredProject.colorHex, equals(project.colorHex));

      // Verify time entry data
      final restoredTimeEntries = restoredBackupData.timeEntries[project.id]!;
      expect(restoredTimeEntries.length, equals(1));
      final restoredTimeEntry = restoredTimeEntries.first;
      expect(restoredTimeEntry.id, equals(timeEntry.id));
      expect(restoredTimeEntry.projectId, equals(timeEntry.projectId));
      expect(restoredTimeEntry.date, equals(timeEntry.date));
      expect(restoredTimeEntry.start, equals(timeEntry.start));
      expect(restoredTimeEntry.end, equals(timeEntry.end));
    });

    test('should handle backup migration from older schema without invoice data', () {
      // Arrange - Create a backup JSON without invoice/client data (older schema)
      final oldBackupData = {
        'projects': [
          {
            'id': 'project1',
            'name': 'Old Project',
            'order': 0,
            'maxWeeksInOverview': 3,
            'createdAt': DateTime.now().toIso8601String(),
          }
        ],
        'timeEntries': {
          'project1': [
            {
              'id': 'entry1',
              'projectId': 'project1',
              'date': DateTime.now().toIso8601String().split('T')[0],
              'start': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
              'end': DateTime.now().toIso8601String(),
              'inProgress': false,
            }
          ]
        },
        'locationAreas': [],
        'version': 2, // Older schema version
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Act - Deserialize from old backup format
      final restoredBackupData = BackupData.fromJson(oldBackupData);

      // Assert - Verify data was migrated correctly
      expect(restoredBackupData.projects.length, equals(1));
      expect(restoredBackupData.projects.first.name, equals('Old Project'));
      expect(restoredBackupData.timeEntries.length, equals(1));
      expect(restoredBackupData.timeEntries['project1']!.length, equals(1));
      expect(restoredBackupData.timeEntries['project1']!.first.projectId, equals('project1'));

      // Invoice and client data should be empty (migrated gracefully)
      expect(restoredBackupData.clients, isEmpty);
      expect(restoredBackupData.invoices, isEmpty);
      expect(restoredBackupData.schemaVersion, equals(2));
    });

    test('should preserve invoice data integrity during serialization cycles', () {
      // Arrange - Create complex invoice data
      final client1 = Client(name: 'Client One', email: '<EMAIL>');
      final client2 = Client(name: 'Client Two', email: '<EMAIL>');

      final project = Project(name: 'Test Project', colorHex: '#2196F3');

      final invoice1 = Invoice(
        invoiceNumber: 'INV-001',
        clientId: client1.id,
        currency: 'EUR',
        locale: 'de_DE',
        status: InvoiceStatus.sent,
        subtotal: 100.0,
        taxRate: 19.0,
        taxAmount: 19.0,
        total: 119.0,
        additionalItems: [
          InvoiceLineItem(
            description: 'Consulting',
            quantity: 2.0,
            rate: 50.0,
            type: InvoiceLineItemType.timeEntry,
          ),
        ],
      );

      final invoice2 = Invoice(
        invoiceNumber: 'INV-002',
        clientId: client2.id,
        currency: 'GBP',
        locale: 'en_GB',
        status: InvoiceStatus.paid,
        subtotal: 200.0,
        taxRate: 20.0,
        taxAmount: 40.0,
        total: 240.0,
      );

      final backupData = BackupData(
        projects: [project],
        timeEntries: {},
        clients: [client1, client2],
        invoices: [invoice1, invoice2],
      );

      // Act - Multiple serialization/deserialization cycles
      BackupData currentData = backupData;
      for (int i = 0; i < 3; i++) {
        final jsonData = currentData.toJson();
        final jsonString = jsonEncode(jsonData);
        final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
        currentData = BackupData.fromJson(decodedJson);
      }

      // Assert - Verify data integrity after multiple cycles
      expect(currentData.clients.length, equals(2));
      expect(currentData.invoices.length, equals(2));

      // Find invoices by number
      final restoredInvoice1 = currentData.invoices.firstWhere((inv) => inv.invoiceNumber == 'INV-001');
      final restoredInvoice2 = currentData.invoices.firstWhere((inv) => inv.invoiceNumber == 'INV-002');

      // Verify complex data integrity
      expect(restoredInvoice1.currency, equals('EUR'));
      expect(restoredInvoice1.locale, equals('de_DE'));
      expect(restoredInvoice1.status, equals(InvoiceStatus.sent));
      expect(restoredInvoice1.additionalItems.length, equals(1));
      expect(restoredInvoice1.additionalItems.first.quantity, equals(2.0));

      expect(restoredInvoice2.currency, equals('GBP'));
      expect(restoredInvoice2.locale, equals('en_GB'));
      expect(restoredInvoice2.status, equals(InvoiceStatus.paid));
      expect(restoredInvoice2.total, equals(240.0));
    });

    test('should handle empty invoice and client data in backups', () {
      // Arrange - Create backup with only projects and time entries
      final project = Project(name: 'Solo Project', colorHex: '#2196F3');
      final timeEntry = TimeEntry(
        projectId: project.id,
        date: DateTime.now().toIso8601String().split('T')[0],
        start: DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        end: DateTime.now().toIso8601String(),
      );

      final backupData = BackupData(
        projects: [project],
        timeEntries: {project.id: [timeEntry]},
        // Explicitly empty invoice/client data
        clients: [],
        invoices: [],
      );

      // Act - Serialize and deserialize
      final jsonData = backupData.toJson();
      final jsonString = jsonEncode(jsonData);
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredData = BackupData.fromJson(decodedJson);

      // Assert - Should handle empty invoice/client data gracefully
      expect(restoredData.projects.length, equals(1));
      expect(restoredData.timeEntries.length, equals(1));
      expect(restoredData.clients, isEmpty);
      expect(restoredData.invoices, isEmpty);
    });

    test('should maintain referential integrity between invoices and clients', () {
      // Arrange - Create linked data
      final client = Client(name: 'Linked Client');
      final invoice1 = Invoice(invoiceNumber: 'INV-001', clientId: client.id);
      final invoice2 = Invoice(invoiceNumber: 'INV-002', clientId: client.id);

      final backupData = BackupData(
        projects: [],
        timeEntries: {},
        clients: [client],
        invoices: [invoice1, invoice2],
      );

      // Act - Serialize and deserialize
      final jsonData = backupData.toJson();
      final jsonString = jsonEncode(jsonData);
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredData = BackupData.fromJson(decodedJson);

      // Assert - Verify relationships are maintained
      expect(restoredData.clients.length, equals(1));
      expect(restoredData.invoices.length, equals(2));

      final restoredClient = restoredData.clients.first;
      final clientInvoices = restoredData.invoices.where((inv) => inv.clientId == restoredClient.id).toList();

      expect(clientInvoices.length, equals(2));
      expect(clientInvoices.every((inv) => inv.clientId == restoredClient.id), isTrue);
    });

    test('should handle complex invoice line items correctly', () {
      // Arrange - Create invoice with multiple line item types
      final client = Client(name: 'Test Client');
      final invoice = Invoice(
        invoiceNumber: 'INV-COMPLEX',
        clientId: client.id,
        additionalItems: [
          InvoiceLineItem(
            description: 'Time Entry Item',
            quantity: 8.0,
            rate: 75.0,
            type: InvoiceLineItemType.timeEntry,
            timeEntryId: 'time-entry-1',
          ),
          InvoiceLineItem(
            description: 'Expense Item',
            quantity: 1.0,
            rate: 50.0,
            type: InvoiceLineItemType.expense,
          ),
          InvoiceLineItem(
            description: 'Discount',
            quantity: 1.0,
            rate: -25.0,
            type: InvoiceLineItemType.discount,
          ),
          InvoiceLineItem(
            description: 'Adjustment',
            quantity: 1.0,
            rate: 10.0,
            type: InvoiceLineItemType.adjustment,
          ),
        ],
      );

      final backupData = BackupData(
        projects: [],
        timeEntries: {},
        clients: [client],
        invoices: [invoice],
      );

      // Act - Serialize and deserialize
      final jsonData = backupData.toJson();
      final jsonString = jsonEncode(jsonData);
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredData = BackupData.fromJson(decodedJson);

      // Assert - Verify line items are preserved correctly
      final restoredInvoice = restoredData.invoices.first;
      expect(restoredInvoice.additionalItems.length, equals(4));

      final timeEntryItem = restoredInvoice.additionalItems.firstWhere((item) => item.type == InvoiceLineItemType.timeEntry);
      expect(timeEntryItem.description, equals('Time Entry Item'));
      expect(timeEntryItem.quantity, equals(8.0));
      expect(timeEntryItem.rate, equals(75.0));
      expect(timeEntryItem.timeEntryId, equals('time-entry-1'));

      final expenseItem = restoredInvoice.additionalItems.firstWhere((item) => item.type == InvoiceLineItemType.expense);
      expect(expenseItem.description, equals('Expense Item'));
      expect(expenseItem.rate, equals(50.0));

      final discountItem = restoredInvoice.additionalItems.firstWhere((item) => item.type == InvoiceLineItemType.discount);
      expect(discountItem.description, equals('Discount'));
      expect(discountItem.rate, equals(-25.0));

      final adjustmentItem = restoredInvoice.additionalItems.firstWhere((item) => item.type == InvoiceLineItemType.adjustment);
      expect(adjustmentItem.description, equals('Adjustment'));
      expect(adjustmentItem.rate, equals(10.0));
    });

    test('should handle address data correctly in client backup', () {
      // Arrange - Create client with complex address
      final client = Client(
        name: 'Address Test Client',
        address: Address(
          street: '123 Main Street',
          street2: 'Suite 456',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
          country: 'United States',
        ),
      );

      final backupData = BackupData(
        projects: [],
        timeEntries: {},
        clients: [client],
        invoices: [],
      );

      // Act - Serialize and deserialize
      final jsonData = backupData.toJson();
      final jsonString = jsonEncode(jsonData);
      final decodedJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final restoredData = BackupData.fromJson(decodedJson);

      // Assert - Verify address data is preserved
      final restoredClient = restoredData.clients.first;
      expect(restoredClient.address, isNotNull);
      expect(restoredClient.address!.street, equals('123 Main Street'));
      expect(restoredClient.address!.street2, equals('Suite 456'));
      expect(restoredClient.address!.city, equals('New York'));
      expect(restoredClient.address!.state, equals('NY'));
      expect(restoredClient.address!.postalCode, equals('10001'));
      expect(restoredClient.address!.country, equals('United States'));
    });
  });
}
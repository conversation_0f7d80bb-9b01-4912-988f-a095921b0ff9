import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';

void main() {
  group('InvoiceLocalizationService', () {
    test('should support German language', () {
      expect(InvoiceLocalizationService.isLanguageSupported('de_DE'), isTrue);
      expect(InvoiceLocalizationService.getTranslation('invoice', 'de_DE'), equals('RECHNUNG'));
      expect(InvoiceLocalizationService.getTranslation('bill_to', 'de_DE'), equals('Rechnung an:'));
      expect(InvoiceLocalizationService.getTranslation('subtotal', 'de_DE'), equals('Zwischensumme'));
      expect(InvoiceLocalizationService.getTranslation('total', 'de_DE'), equals('Gesamtbetrag'));
    });

    test('should support English language', () {
      expect(InvoiceLocalizationService.isLanguageSupported('en_US'), isTrue);
      expect(InvoiceLocalizationService.getTranslation('invoice', 'en_US'), equals('INVOICE'));
      expect(InvoiceLocalizationService.getTranslation('bill_to', 'en_US'), equals('Bill To:'));
      expect(InvoiceLocalizationService.getTranslation('subtotal', 'en_US'), equals('Subtotal'));
      expect(InvoiceLocalizationService.getTranslation('total', 'en_US'), equals('Total'));
    });

    test('should support French language', () {
      expect(InvoiceLocalizationService.isLanguageSupported('fr_FR'), isTrue);
      expect(InvoiceLocalizationService.getTranslation('invoice', 'fr_FR'), equals('FACTURE'));
      expect(InvoiceLocalizationService.getTranslation('bill_to', 'fr_FR'), equals('Facturer à:'));
      expect(InvoiceLocalizationService.getTranslation('subtotal', 'fr_FR'), equals('Sous-total'));
      expect(InvoiceLocalizationService.getTranslation('total', 'fr_FR'), equals('Total'));
    });

    test('should support Spanish language', () {
      expect(InvoiceLocalizationService.isLanguageSupported('es_ES'), isTrue);
      expect(InvoiceLocalizationService.getTranslation('invoice', 'es_ES'), equals('FACTURA'));
      expect(InvoiceLocalizationService.getTranslation('bill_to', 'es_ES'), equals('Facturar a:'));
      expect(InvoiceLocalizationService.getTranslation('subtotal', 'es_ES'), equals('Subtotal'));
      expect(InvoiceLocalizationService.getTranslation('total', 'es_ES'), equals('Total'));
    });

    test('should fallback to English for unsupported languages', () {
      expect(InvoiceLocalizationService.getTranslation('invoice', 'unsupported'), equals('INVOICE'));
      expect(InvoiceLocalizationService.getTranslation('bill_to', 'unsupported'), equals('Bill To:'));
    });

    test('should return all supported languages', () {
      final languages = InvoiceLocalizationService.getSupportedLanguages();
      expect(languages, contains('en_US'));
      expect(languages, contains('de_DE'));
      expect(languages, contains('fr_FR'));
      expect(languages, contains('es_ES'));
    });

    test('should return language display names', () {
      final displayNames = InvoiceLocalizationService.getLanguageDisplayNames();
      expect(displayNames['en_US'], equals('English (US)'));
      expect(displayNames['de_DE'], equals('Deutsch (Deutschland)'));
      expect(displayNames['fr_FR'], equals('Français (France)'));
      expect(displayNames['es_ES'], equals('Español (España)'));
    });

    test('should validate complete translations', () {
      expect(InvoiceLocalizationService.hasCompleteTranslations('en_US'), isTrue);
      expect(InvoiceLocalizationService.hasCompleteTranslations('de_DE'), isTrue);
      expect(InvoiceLocalizationService.hasCompleteTranslations('fr_FR'), isTrue);
      expect(InvoiceLocalizationService.hasCompleteTranslations('es_ES'), isTrue);
      expect(InvoiceLocalizationService.hasCompleteTranslations('unsupported'), isFalse);
    });

    test('should format currency for different languages', () {
      // Test German formatting (uses comma as decimal separator)
      final germanAmount = InvoiceLocalizationService.formatCurrencyForLanguage(1234.56, 'EUR', 'de_DE');
      expect(germanAmount, contains('€'));
      
      // Test English formatting
      final englishAmount = InvoiceLocalizationService.formatCurrencyForLanguage(1234.56, 'USD', 'en_US');
      expect(englishAmount, contains('\$'));
      
      // Test French formatting
      final frenchAmount = InvoiceLocalizationService.formatCurrencyForLanguage(1234.56, 'EUR', 'fr_FR');
      expect(frenchAmount, contains('€'));
    });

    test('should get invoice headers for different languages', () {
      final germanHeaders = InvoiceLocalizationService.getInvoiceHeaders('de_DE');
      expect(germanHeaders['description'], equals('Beschreibung'));
      expect(germanHeaders['hours'], equals('Stunden'));
      expect(germanHeaders['rate'], equals('Satz'));
      expect(germanHeaders['amount'], equals('Betrag'));

      final englishHeaders = InvoiceLocalizationService.getInvoiceHeaders('en_US');
      expect(englishHeaders['description'], equals('Description'));
      expect(englishHeaders['hours'], equals('Hours'));
      expect(englishHeaders['rate'], equals('Rate'));
      expect(englishHeaders['amount'], equals('Amount'));
    });

    test('should get summary labels for different languages', () {
      final germanLabels = InvoiceLocalizationService.getSummaryLabels('de_DE');
      expect(germanLabels['subtotal'], equals('Zwischensumme'));
      expect(germanLabels['tax'], equals('Steuer'));
      expect(germanLabels['total'], equals('Gesamtbetrag'));

      final englishLabels = InvoiceLocalizationService.getSummaryLabels('en_US');
      expect(englishLabels['subtotal'], equals('Subtotal'));
      expect(englishLabels['tax'], equals('Tax'));
      expect(englishLabels['total'], equals('Total'));
    });

    test('should get invoice phrases for different languages', () {
      final germanPhrases = InvoiceLocalizationService.getInvoicePhrases('de_DE');
      expect(germanPhrases['thank_you'], equals('Vielen Dank für Ihr Vertrauen!'));
      expect(germanPhrases['please_pay'], equals('Bitte zahlen Sie innerhalb der angegebenen Frist.'));
      expect(germanPhrases['questions'], equals('Bei Fragen kontaktieren Sie uns bitte.'));

      final englishPhrases = InvoiceLocalizationService.getInvoicePhrases('en_US');
      expect(englishPhrases['thank_you'], equals('Thank you for your business!'));
      expect(englishPhrases['please_pay'], equals('Please pay within the specified terms.'));
      expect(englishPhrases['questions'], equals('If you have any questions, please contact us.'));
    });

    test('should convert between locale and language strings', () {
      final locale = InvoiceLocalizationService.getLocaleFromLanguage('de_DE');
      expect(locale.languageCode, equals('de'));
      expect(locale.countryCode, equals('DE'));

      final language = InvoiceLocalizationService.getLanguageFromLocale(locale);
      expect(language, equals('de_DE'));
    });

    test('should get language info', () {
      final germanInfo = InvoiceLocalizationService.getLanguageInfo('de_DE');
      expect(germanInfo['code'], equals('de_DE'));
      expect(germanInfo['displayName'], equals('Deutsch (Deutschland)'));
      expect(germanInfo['hasCompleteTranslations'], isTrue);
      expect(germanInfo['locale'].languageCode, equals('de'));
      expect(germanInfo['locale'].countryCode, equals('DE'));
    });

    test('should get all language info', () {
      final allInfo = InvoiceLocalizationService.getAllLanguageInfo();
      expect(allInfo.length, equals(4)); // en_US, de_DE, fr_FR, es_ES
      
      final germanInfo = allInfo.firstWhere((info) => info['code'] == 'de_DE');
      expect(germanInfo['displayName'], equals('Deutsch (Deutschland)'));
      expect(germanInfo['hasCompleteTranslations'], isTrue);
    });
  });
} 
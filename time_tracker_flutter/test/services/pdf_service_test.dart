import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';

import 'pdf_service_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  group('PDFService', () {
    late PDFService pdfService;
    late MockDatabaseService mockDatabaseService;
    late Invoice testInvoice;
    late Client testClient;
    late BusinessInfo testBusinessInfo;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      pdfService = PDFService(databaseService: mockDatabaseService);

      // Create test data
      testClient = Client(
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: Address(
          street: '123 Test St',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'Test Country',
        ),
        taxId: 'TAX123456',
      );

      testInvoice = Invoice(
        invoiceNumber: 'INV-2024-001',
        clientId: testClient.id,
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime(2024, 1, 15),
        dueDate: DateTime(2024, 2, 15),
        status: InvoiceStatus.sent,
        subtotal: 1000.0,
        taxRate: 10.0,
        taxAmount: 100.0,
        total: 1100.0,
        notes: 'Test invoice notes',
        additionalItems: [
          InvoiceLineItem(
            description: 'Web Development Services',
            quantity: 10.0,
            rate: 100.0,
            type: InvoiceLineItemType.timeEntry,
          ),
        ],
      );

      testBusinessInfo = BusinessInfo(
        name: 'Test Business',
        email: '<EMAIL>',
        phone: '+1987654321',
        address: Address(
          street: '456 Business Ave',
          city: 'Business City',
          state: 'BC',
          postalCode: '67890',
          country: 'Business Country',
        ),
        website: 'https://testbusiness.com',
        taxId: 'BUS123456',
      );
    });

    group('generateInvoicePDF', () {
      testWidgets('should generate PDF with professional template', (tester) async {
        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.professional,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
        // PDF should start with PDF header
        expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46])); // %PDF
      });

      testWidgets('should generate PDF with minimal template', (tester) async {
        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.minimal,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
        expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46])); // %PDF
      });

      testWidgets('should generate PDF with detailed template', (tester) async {
        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.detailed,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
        expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46])); // %PDF
      });

      testWidgets('should handle different currencies', (tester) async {
        // Arrange
        final eurInvoice = testInvoice.copyWith(currency: 'EUR');

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: eurInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle different locales', (tester) async {
        // Arrange
        const locale = Locale('de', 'DE');

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          locale: locale,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle invoice without due date', (tester) async {
        // Arrange
        final invoiceNoDueDate = testInvoice.copyWith(dueDate: null);

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: invoiceNoDueDate,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle invoice without notes', (tester) async {
        // Arrange
        final invoiceNoNotes = testInvoice.copyWith(notes: null);

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: invoiceNoNotes,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle client without address', (tester) async {
        // Arrange
        final clientNoAddress = testClient.copyWith(address: null);

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: clientNoAddress,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle business info without logo', (tester) async {
        // Arrange
        const businessInfoNoLogo = BusinessInfo(
          name: 'Test Business',
          email: '<EMAIL>',
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: businessInfoNoLogo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle invoice with zero tax', (tester) async {
        // Arrange
        final invoiceNoTax = testInvoice.copyWith(
          taxRate: 0.0,
          taxAmount: 0.0,
          total: testInvoice.subtotal,
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: invoiceNoTax,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle invoice with multiple line items', (tester) async {
        // Arrange
        final multiItemInvoice = testInvoice.copyWith(
          additionalItems: [
            InvoiceLineItem(
              description: 'Web Development',
              quantity: 10.0,
              rate: 100.0,
              type: InvoiceLineItemType.timeEntry,
            ),
            InvoiceLineItem(
              description: 'Consulting',
              quantity: 5.0,
              rate: 150.0,
              type: InvoiceLineItemType.timeEntry,
            ),
            InvoiceLineItem(
              description: 'Travel Expenses',
              quantity: 1.0,
              rate: 200.0,
              type: InvoiceLineItemType.expense,
            ),
          ],
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: multiItemInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle different invoice statuses', (tester) async {
        for (final status in InvoiceStatus.values) {
          // Arrange
          final statusInvoice = testInvoice.copyWith(status: status);

          // Act
          final pdfData = await pdfService.generateInvoicePDF(
            invoice: statusInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
          );

          // Assert
          expect(pdfData, isA<Uint8List>());
          expect(pdfData.isNotEmpty, isTrue);
        }
      });

      testWidgets('should include time entries when provided', (tester) async {
        // Arrange
        final timeEntries = <TimeEntry>[
          TimeEntry(
            id: 'entry1',
            projectId: 'project1',
            date: '2024-01-10',
            start: '09:00',
            end: '17:00',
          ),
          TimeEntry(
            id: 'entry2',
            projectId: 'project1',
            date: '2024-01-11',
            start: '09:00',
            end: '17:00',
          ),
        ];

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.detailed,
          timeEntries: timeEntries,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should throw PDFGenerationException on error', (tester) async {
        // Arrange - Create invalid invoice data that might cause PDF generation to fail
        final invalidInvoice = Invoice(
          invoiceNumber: '', // Empty invoice number might cause issues
          clientId: '',
          additionalItems: [],
        );

        // Act & Assert
        expect(
          () => pdfService.generateInvoicePDF(
            invoice: invalidInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
          ),
          throwsA(isA<PDFGenerationException>()),
        );
      });
    });

    group('generateAndSavePDF', () {
      testWidgets('should generate and save PDF file', (tester) async {
        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(filePath, isA<String>());
        expect(filePath.isNotEmpty, isTrue);
        expect(filePath.endsWith('.pdf'), isTrue);
      });

      testWidgets('should use custom filename when provided', (tester) async {
        // Arrange
        const customFileName = 'custom_invoice.pdf';

        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          customFileName: customFileName,
        );

        // Assert
        expect(filePath, contains(customFileName));
      });

      testWidgets('should sanitize invoice number for filename', (tester) async {
        // Arrange
        final invoiceWithSpecialChars = testInvoice.copyWith(
          invoiceNumber: 'INV/2024-001#@!',
        );

        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: invoiceWithSpecialChars,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(filePath, isA<String>());
        expect(filePath.endsWith('.pdf'), isTrue);
        // Should not contain special characters in filename
        expect(filePath, isNot(contains('/')));
        expect(filePath, isNot(contains('#')));
        expect(filePath, isNot(contains('@')));
        expect(filePath, isNot(contains('!')));
      });
    });

    group('shareInvoicePDF', () {
      testWidgets('should generate and share PDF', (tester) async {
        // Note: This test is limited because we can't easily mock the Share.shareXFiles call
        // In a real implementation, you might want to inject a sharing service for better testability

        // Act & Assert - Should not throw an exception
        expect(
          () => pdfService.shareInvoicePDF(
            invoice: testInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
          ),
          returnsNormally,
        );
      });
    });

    group('BusinessInfo', () {
      test('should create BusinessInfo with required fields only', () {
        // Arrange & Act
        const businessInfo = BusinessInfo(name: 'Test Business');

        // Assert
        expect(businessInfo.name, equals('Test Business'));
        expect(businessInfo.email, isNull);
        expect(businessInfo.phone, isNull);
        expect(businessInfo.address, isNull);
        expect(businessInfo.website, isNull);
        expect(businessInfo.taxId, isNull);
        expect(businessInfo.logoData, isNull);
      });

      test('should create BusinessInfo with all fields', () {
        // Arrange
        final logoData = Uint8List.fromList([1, 2, 3, 4]);
        final address = Address(
          street: '123 Test St',
          city: 'Test City',
          postalCode: '12345',
          country: 'Test Country',
        );

        // Act
        final businessInfo = BusinessInfo(
          name: 'Test Business',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: address,
          website: 'https://testbusiness.com',
          taxId: 'TAX123',
          logoData: logoData,
        );

        // Assert
        expect(businessInfo.name, equals('Test Business'));
        expect(businessInfo.email, equals('<EMAIL>'));
        expect(businessInfo.phone, equals('+1234567890'));
        expect(businessInfo.address, equals(address));
        expect(businessInfo.website, equals('https://testbusiness.com'));
        expect(businessInfo.taxId, equals('TAX123'));
        expect(businessInfo.logoData, equals(logoData));
      });
    });

    group('PDFGenerationException', () {
      test('should create exception with message', () {
        // Arrange & Act
        const exception = PDFGenerationException('Test error message');

        // Assert
        expect(exception.message, equals('Test error message'));
        expect(exception.toString(), equals('PDFGenerationException: Test error message'));
      });
    });

    group('InvoiceTemplate', () {
      test('should have all expected template values', () {
        // Assert
        expect(InvoiceTemplate.values, hasLength(3));
        expect(InvoiceTemplate.values, contains(InvoiceTemplate.professional));
        expect(InvoiceTemplate.values, contains(InvoiceTemplate.minimal));
        expect(InvoiceTemplate.values, contains(InvoiceTemplate.detailed));
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('should handle very large amounts', (tester) async {
        // Arrange
        final largeAmountInvoice = testInvoice.copyWith(
          subtotal: 999999999.99,
          total: 999999999.99,
          additionalItems: [
            InvoiceLineItem(
              description: 'Large amount item',
              quantity: 1.0,
              rate: 999999999.99,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: largeAmountInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle very small amounts', (tester) async {
        // Arrange
        final smallAmountInvoice = testInvoice.copyWith(
          subtotal: 0.01,
          total: 0.01,
          additionalItems: [
            InvoiceLineItem(
              description: 'Small amount item',
              quantity: 1.0,
              rate: 0.01,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: smallAmountInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle long descriptions', (tester) async {
        // Arrange
        final longDescription = 'A' * 1000; // Very long description
        final longDescInvoice = testInvoice.copyWith(
          additionalItems: [
            InvoiceLineItem(
              description: longDescription,
              quantity: 1.0,
              rate: 100.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: longDescInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle special characters in text', (tester) async {
        // Arrange
        final specialCharClient = testClient.copyWith(
          name: 'Test Client with Special Characters & Symbols',
        );
        final specialCharInvoice = testInvoice.copyWith(
          notes: 'Special notes with symbols: @#\$%^&*()',
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: specialCharInvoice,
          client: specialCharClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle empty line items list', (tester) async {
        // Arrange
        final emptyItemsInvoice = testInvoice.copyWith(
          additionalItems: [],
          subtotal: 0.0,
          taxAmount: 0.0,
          total: 0.0,
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: emptyItemsInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });
    });
  });
}
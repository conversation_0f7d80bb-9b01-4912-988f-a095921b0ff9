import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

import 'client_service_test_mocks.mocks.dart';

void main() {
  group('ClientService', () {
    late ClientService clientService;
    late MockDatabaseService mockDatabaseService;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      clientService = ClientService(databaseService: mockDatabaseService);
    });

    group('getClients', () {
      test('should return list of clients from database', () async {
        // Arrange
        final clients = [
          Client(name: 'Client A'),
          Client(name: 'Client B'),
        ];
        when(mockDatabaseService.getClients()).thenAnswer((_) async => clients);

        // Act
        final result = await clientService.getClients();

        // Assert
        expect(result, equals(clients));
        verify(mockDatabaseService.getClients()).called(1);
      });

      test('should rethrow database errors', () async {
        // Arrange
        when(mockDatabaseService.getClients()).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(() => clientService.getClients(), throwsException);
      });
    });

    group('getClient', () {
      test('should return client by ID', () async {
        // Arrange
        final client = Client(name: 'Test Client');
        when(mockDatabaseService.getClient('123')).thenAnswer((_) async => client);

        // Act
        final result = await clientService.getClient('123');

        // Assert
        expect(result, equals(client));
        verify(mockDatabaseService.getClient('123')).called(1);
      });

      test('should return null for non-existent client', () async {
        // Arrange
        when(mockDatabaseService.getClient('999')).thenAnswer((_) async => null);

        // Act
        final result = await clientService.getClient('999');

        // Assert
        expect(result, isNull);
      });

      test('should throw ArgumentError for empty ID', () async {
        // Act & Assert
        expect(() => clientService.getClient(''), throwsArgumentError);
        expect(() => clientService.getClient('   '), throwsArgumentError);
      });
    });

    group('searchClients', () {
      test('should return search results from database', () async {
        // Arrange
        final clients = [Client(name: 'John Doe')];
        when(mockDatabaseService.searchClients('John')).thenAnswer((_) async => clients);

        // Act
        final result = await clientService.searchClients('John');

        // Assert
        expect(result, equals(clients));
        verify(mockDatabaseService.searchClients('John')).called(1);
      });

      test('should handle empty search query', () async {
        // Arrange
        final clients = [Client(name: 'All Clients')];
        when(mockDatabaseService.searchClients('')).thenAnswer((_) async => clients);

        // Act
        final result = await clientService.searchClients('');

        // Assert
        expect(result, equals(clients));
      });
    });

    group('filterClients', () {
      late List<Client> testClients;

      setUp(() {
        testClients = [
          Client(
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+*********0',
            address: Address(
              street: '123 Main St',
              city: 'New York',
              postalCode: '10001',
              country: 'USA',
            ),
            taxId: 'TAX123',
          ),
          Client(
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: null,
            address: null,
            taxId: null,
          ),
          Client(
            name: 'Bob Johnson',
            email: null,
            phone: '+9876543210',
            address: Address(
              street: '456 Oak Ave',
              city: 'London',
              postalCode: 'SW1A 1AA',
              country: 'UK',
            ),
            taxId: 'UK789',
          ),
        ];
        when(mockDatabaseService.getClients()).thenAnswer((_) async => testClients);
      });

      test('should filter by name contains', () async {
        // Act
        final result = await clientService.filterClients(nameContains: 'John');

        // Assert
        expect(result.length, equals(2)); // John Doe and Bob Johnson
        expect(result.any((c) => c.name == 'John Doe'), isTrue);
        expect(result.any((c) => c.name == 'Bob Johnson'), isTrue);
      });

      test('should filter by email contains', () async {
        // Act
        final result = await clientService.filterClients(emailContains: 'example');

        // Assert
        expect(result.length, equals(1));
        expect(result.first.name, equals('John Doe'));
      });

      test('should filter by phone contains', () async {
        // Act
        final result = await clientService.filterClients(phoneContains: '123');

        // Assert
        expect(result.length, equals(1));
        expect(result.first.name, equals('John Doe'));
      });

      test('should filter by country equals', () async {
        // Act
        final result = await clientService.filterClients(countryEquals: 'UK');

        // Assert
        expect(result.length, equals(1));
        expect(result.first.name, equals('Bob Johnson'));
      });

      test('should filter by hasEmail', () async {
        // Act
        final withEmail = await clientService.filterClients(hasEmail: true);
        final withoutEmail = await clientService.filterClients(hasEmail: false);

        // Assert
        expect(withEmail.length, equals(2)); // John and Jane
        expect(withoutEmail.length, equals(1)); // Bob
      });

      test('should filter by hasPhone', () async {
        // Act
        final withPhone = await clientService.filterClients(hasPhone: true);
        final withoutPhone = await clientService.filterClients(hasPhone: false);

        // Assert
        expect(withPhone.length, equals(2)); // John and Bob
        expect(withoutPhone.length, equals(1)); // Jane
      });

      test('should filter by hasAddress', () async {
        // Act
        final withAddress = await clientService.filterClients(hasAddress: true);
        final withoutAddress = await clientService.filterClients(hasAddress: false);

        // Assert
        expect(withAddress.length, equals(2)); // John and Bob
        expect(withoutAddress.length, equals(1)); // Jane
      });

      test('should filter by hasTaxId', () async {
        // Act
        final withTaxId = await clientService.filterClients(hasTaxId: true);
        final withoutTaxId = await clientService.filterClients(hasTaxId: false);

        // Assert
        expect(withTaxId.length, equals(2)); // John and Bob
        expect(withoutTaxId.length, equals(1)); // Jane
      });

      test('should apply multiple filters', () async {
        // Act
        final result = await clientService.filterClients(
          hasEmail: true,
          hasAddress: false,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.name, equals('Jane Smith'));
      });
    });

    group('saveClient', () {
      test('should save valid client', () async {
        // Arrange
        final client = Client(name: 'Valid Client', email: '<EMAIL>');
        when(mockDatabaseService.saveClient(client)).thenAnswer((_) async {});

        // Act
        await clientService.saveClient(client);

        // Assert
        verify(mockDatabaseService.saveClient(client)).called(1);
      });

      test('should throw ArgumentError for invalid client', () async {
        // Arrange
        final client = Client(name: ''); // Invalid: empty name

        // Act & Assert
        expect(() => clientService.saveClient(client), throwsArgumentError);
        verifyNever(mockDatabaseService.saveClient(any));
      });
    });

    group('createClient', () {
      test('should create and save new client', () async {
        // Arrange
        when(mockDatabaseService.saveClient(any)).thenAnswer((_) async {});

        // Act
        final result = await clientService.createClient(
          name: 'New Client',
          email: '<EMAIL>',
        );

        // Assert
        expect(result.name, equals('New Client'));
        expect(result.email, equals('<EMAIL>'));
        verify(mockDatabaseService.saveClient(any)).called(1);
      });

      test('should throw error for invalid client data', () async {
        // Act & Assert
        expect(
          () => clientService.createClient(name: ''), // Empty name
          throwsArgumentError,
        );
      });
    });

    group('updateClient', () {
      test('should update existing client', () async {
        // Arrange
        final existingClient = Client(name: 'Old Name');
        when(mockDatabaseService.getClient('123')).thenAnswer((_) async => existingClient);
        when(mockDatabaseService.saveClient(any)).thenAnswer((_) async {});

        // Act
        final result = await clientService.updateClient('123', name: 'New Name');

        // Assert
        expect(result.name, equals('New Name'));
        expect(result.id, equals(existingClient.id));
        verify(mockDatabaseService.saveClient(any)).called(1);
      });

      test('should throw ArgumentError for non-existent client', () async {
        // Arrange
        when(mockDatabaseService.getClient('999')).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => clientService.updateClient('999', name: 'New Name'),
          throwsArgumentError,
        );
      });
    });

    group('deleteClient', () {
      test('should delete existing client', () async {
        // Arrange
        final client = Client(name: 'Test Client');
        when(mockDatabaseService.getClient('123')).thenAnswer((_) async => client);
        when(mockDatabaseService.deleteClient('123')).thenAnswer((_) async {});

        // Act
        await clientService.deleteClient('123');

        // Assert
        verify(mockDatabaseService.deleteClient('123')).called(1);
      });

      test('should throw ArgumentError for empty ID', () async {
        // Act & Assert
        expect(() => clientService.deleteClient(''), throwsArgumentError);
      });

      test('should throw ArgumentError for non-existent client', () async {
        // Arrange
        when(mockDatabaseService.getClient('999')).thenAnswer((_) async => null);

        // Act & Assert
        expect(() => clientService.deleteClient('999'), throwsArgumentError);
      });
    });

    group('canDeleteClient', () {
      test('should return true when client has no invoices', () async {
        // Arrange
        when(mockDatabaseService.getInvoicesByClient('123')).thenAnswer((_) async => []);

        // Act
        final result = await clientService.canDeleteClient('123');

        // Assert
        expect(result, isTrue);
      });

      test('should return false when client has invoices', () async {
        // Arrange
        final invoices = [
          Invoice(invoiceNumber: 'INV-001', clientId: '123'),
        ];
        when(mockDatabaseService.getInvoicesByClient('123')).thenAnswer((_) async => invoices);

        // Act
        final result = await clientService.canDeleteClient('123');

        // Assert
        expect(result, isFalse);
      });

      test('should return false on database error', () async {
        // Arrange
        when(mockDatabaseService.getInvoicesByClient('123')).thenThrow(Exception('DB Error'));

        // Act
        final result = await clientService.canDeleteClient('123');

        // Assert
        expect(result, isFalse);
      });
    });

    group('validation', () {
      group('validateClient', () {
        test('should validate correct client', () {
          // Arrange
          final client = Client(
            name: 'Valid Client',
            email: '<EMAIL>',
            phone: '+*********0',
            taxId: 'TAX123',
          );

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isTrue);
          expect(result.errors, isEmpty);
        });

        test('should reject client with empty name', () {
          // Arrange
          final client = Client(name: '');

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isFalse);
          expect(result.errors, contains('Client name is required'));
        });

        test('should reject client with invalid email', () {
          // Arrange
          final client = Client(name: 'Test', email: 'invalid-email');

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isFalse);
          expect(result.errors, contains('Invalid email format'));
        });

        test('should reject client with invalid phone', () {
          // Arrange
          final client = Client(name: 'Test', phone: '123'); // Too short

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isFalse);
          expect(result.errors, contains('Invalid phone number format'));
        });

        test('should reject client with invalid tax ID', () {
          // Arrange
          final client = Client(name: 'Test', taxId: 'X'); // Too short

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isFalse);
          expect(result.errors, contains('Invalid tax ID format'));
        });

        test('should reject client with invalid address', () {
          // Arrange
          final client = Client(
            name: 'Test',
            address: Address(
              street: '', // Invalid: empty street
              city: 'City',
              postalCode: '12345',
              country: 'Country',
            ),
          );

          // Act
          final result = clientService.validateClient(client);

          // Assert
          expect(result.isValid, isFalse);
          expect(result.errors, contains('Invalid address: missing required fields'));
        });
      });

      group('isValidEmail', () {
        test('should validate correct email formats', () {
          expect(clientService.isValidEmail('<EMAIL>'), isTrue);
          expect(clientService.isValidEmail('<EMAIL>'), isTrue);
          expect(clientService.isValidEmail('<EMAIL>'), isTrue);
        });

        test('should reject invalid email formats', () {
          expect(clientService.isValidEmail(''), isFalse);
          expect(clientService.isValidEmail('invalid'), isFalse);
          expect(clientService.isValidEmail('test@'), isFalse);
          expect(clientService.isValidEmail('@example.com'), isFalse);
          expect(clientService.isValidEmail('test@.com'), isFalse);
        });
      });

      group('isValidPhone', () {
        test('should validate correct phone formats', () {
          expect(clientService.isValidPhone('+*********0'), isTrue);
          expect(clientService.isValidPhone('(*************'), isTrue);
          expect(clientService.isValidPhone('************'), isTrue);
          expect(clientService.isValidPhone('*********0'), isTrue);
          expect(clientService.isValidPhone('+44 20 7946 0958'), isTrue);
        });

        test('should reject invalid phone formats', () {
          expect(clientService.isValidPhone(''), isFalse);
          expect(clientService.isValidPhone('123'), isFalse); // Too short
          expect(clientService.isValidPhone('*********0*********0'), isFalse); // Too long
        });
      });

      group('isValidTaxId', () {
        test('should validate correct tax ID formats', () {
          expect(clientService.isValidTaxId('TAX123'), isTrue);
          expect(clientService.isValidTaxId('12-3456789'), isTrue);
          expect(clientService.isValidTaxId('ABC 123 DEF'), isTrue);
          expect(clientService.isValidTaxId('*********'), isTrue);
        });

        test('should reject invalid tax ID formats', () {
          expect(clientService.isValidTaxId(''), isFalse);
          expect(clientService.isValidTaxId('X'), isFalse); // Too short
          expect(clientService.isValidTaxId('X' * 25), isFalse); // Too long
          expect(clientService.isValidTaxId('TAX@123'), isFalse); // Invalid characters
        });
      });
    });

    group('getClientSelectionOptions', () {
      test('should return client options without search', () async {
        // Arrange
        final clients = [Client(name: 'Client A'), Client(name: 'Client B')];
        when(mockDatabaseService.getClients()).thenAnswer((_) async => clients);

        // Act
        final result = await clientService.getClientSelectionOptions();

        // Assert
        expect(result.length, equals(2));
        expect(result.every((option) => !option.isCreateNew), isTrue);
      });

      test('should return search results with create new option', () async {
        // Arrange
        final clients = [Client(name: 'John Doe')];
        when(mockDatabaseService.searchClients('John')).thenAnswer((_) async => clients);

        // Act
        final result = await clientService.getClientSelectionOptions(
          searchQuery: 'John',
          includeCreateNew: true,
        );

        // Assert
        expect(result.length, equals(2)); // 1 client + 1 create new
        expect(result.first.isCreateNew, isTrue);
        expect(result.first.suggestedName, equals('John'));
        expect(result.last.isCreateNew, isFalse);
      });
    });

    group('getClientStatistics', () {
      test('should return correct statistics', () async {
        // Arrange
        final clients = [
          Client(name: 'Client A', email: '<EMAIL>', phone: '123'),
          Client(name: 'Client B', email: '<EMAIL>'),
          Client(name: 'Client C', phone: '456'),
        ];
        when(mockDatabaseService.getClients()).thenAnswer((_) async => clients);
        when(mockDatabaseService.getInvoicesByClient(any)).thenAnswer((_) async => []);

        // Act
        final stats = await clientService.getClientStatistics();

        // Assert
        expect(stats.totalClients, equals(3));
        expect(stats.clientsWithEmail, equals(2));
        expect(stats.clientsWithPhone, equals(2));
        expect(stats.clientsWithoutInvoices, equals(3));
      });
    });

    group('findPotentialDuplicates', () {
      test('should find duplicates by name', () async {
        // Arrange
        final existingClients = [
          Client(name: 'John Doe'),
          Client(name: 'Jane Smith'),
        ];
        final newClient = Client(name: 'john doe'); // Case insensitive match
        when(mockDatabaseService.getClients()).thenAnswer((_) async => existingClients);

        // Act
        final duplicates = await clientService.findPotentialDuplicates(newClient);

        // Assert
        expect(duplicates.length, equals(1));
        expect(duplicates.first.name, equals('John Doe'));
      });

      test('should find duplicates by email', () async {
        // Arrange
        final existingClients = [
          Client(name: 'John Doe', email: '<EMAIL>'),
          Client(name: 'Jane Smith', email: '<EMAIL>'),
        ];
        final newClient = Client(name: 'Different Name', email: '<EMAIL>');
        when(mockDatabaseService.getClients()).thenAnswer((_) async => existingClients);

        // Act
        final duplicates = await clientService.findPotentialDuplicates(newClient);

        // Assert
        expect(duplicates.length, equals(1));
        expect(duplicates.first.email, equals('<EMAIL>'));
      });

      test('should find duplicates by phone', () async {
        // Arrange
        final existingClients = [
          Client(name: 'John Doe', phone: '******-567-8900'),
          Client(name: 'Jane Smith', phone: '******-654-3210'),
        ];
        final newClient = Client(name: 'Different Name', phone: '*********00'); // Normalized match
        when(mockDatabaseService.getClients()).thenAnswer((_) async => existingClients);

        // Act
        final duplicates = await clientService.findPotentialDuplicates(newClient);

        // Assert
        expect(duplicates.length, equals(1));
        expect(duplicates.first.phone, equals('******-567-8900'));
      });

      test('should not find self as duplicate when updating', () async {
        // Arrange
        final existingClient = Client(name: 'John Doe');
        final existingClients = [existingClient];
        final updatedClient = existingClient.copyWith(name: 'John Doe Updated');
        when(mockDatabaseService.getClients()).thenAnswer((_) async => existingClients);

        // Act
        final duplicates = await clientService.findPotentialDuplicates(updatedClient);

        // Assert
        expect(duplicates, isEmpty);
      });
    });
  });
}
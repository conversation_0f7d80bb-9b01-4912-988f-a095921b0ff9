import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';

void main() {
  group('InvoiceLocalizationService Service Period', () {
    test('should provide English service period translations', () {
      expect(InvoiceLocalizationService.getTranslation('service_period', 'en_US'), 
             equals('Service Period'));
      expect(InvoiceLocalizationService.getTranslation('enable_service_period', 'en_US'), 
             equals('Enable Service Period'));
      expect(InvoiceLocalizationService.getTranslation('from', 'en_US'), 
             equals('From'));
      expect(InvoiceLocalizationService.getTranslation('to', 'en_US'), 
             equals('To'));
      expect(InvoiceLocalizationService.getTranslation('both_dates_required', 'en_US'), 
             equals('Both start and end dates are required when service period is enabled'));
      expect(InvoiceLocalizationService.getTranslation('end_date_must_be_after_start', 'en_US'), 
             equals('End date must be after or equal to start date'));
      expect(InvoiceLocalizationService.getTranslation('select_date', 'en_US'), 
             equals('Select date'));
    });

    test('should provide German service period translations', () {
      expect(InvoiceLocalizationService.getTranslation('service_period', 'de_DE'), 
             equals('Leistungszeitraum'));
      expect(InvoiceLocalizationService.getTranslation('enable_service_period', 'de_DE'), 
             equals('Leistungszeitraum aktivieren'));
      expect(InvoiceLocalizationService.getTranslation('from', 'de_DE'), 
             equals('Von'));
      expect(InvoiceLocalizationService.getTranslation('to', 'de_DE'), 
             equals('Bis'));
      expect(InvoiceLocalizationService.getTranslation('both_dates_required', 'de_DE'), 
             equals('Beide Daten (Start und Ende) sind erforderlich, wenn der Leistungszeitraum aktiviert ist'));
      expect(InvoiceLocalizationService.getTranslation('end_date_must_be_after_start', 'de_DE'), 
             equals('Das Enddatum muss nach oder gleich dem Startdatum sein'));
      expect(InvoiceLocalizationService.getTranslation('select_date', 'de_DE'), 
             equals('Datum auswählen'));
    });

    test('should fallback to English for unsupported languages', () {
      expect(InvoiceLocalizationService.getTranslation('service_period', 'unsupported_lang'), 
             equals('Service Period'));
      expect(InvoiceLocalizationService.getTranslation('enable_service_period', 'unsupported_lang'), 
             equals('Enable Service Period'));
    });

    test('should return key if translation not found', () {
      expect(InvoiceLocalizationService.getTranslation('non_existent_key', 'en_US'), 
             equals('non_existent_key'));
      expect(InvoiceLocalizationService.getTranslation('non_existent_key', 'de_DE'), 
             equals('non_existent_key'));
    });

    test('should support language switching', () {
      // Test switching between languages for the same key
      expect(InvoiceLocalizationService.getTranslation('service_period', 'en_US'), 
             equals('Service Period'));
      expect(InvoiceLocalizationService.getTranslation('service_period', 'de_DE'), 
             equals('Leistungszeitraum'));
      
      // Test validation messages
      expect(InvoiceLocalizationService.getTranslation('end_date_must_be_after_start', 'en_US'), 
             equals('End date must be after or equal to start date'));
      expect(InvoiceLocalizationService.getTranslation('end_date_must_be_after_start', 'de_DE'), 
             equals('Das Enddatum muss nach oder gleich dem Startdatum sein'));
    });
  });
}
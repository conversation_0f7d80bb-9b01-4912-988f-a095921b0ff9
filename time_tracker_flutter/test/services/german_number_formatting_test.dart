import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';

void main() {
  group('German Number Formatting Tests', () {
    test('should format currency correctly for German locale', () {
      // Test German currency formatting
      final germanAmount = CurrencyService.formatCurrency(1234.56, 'EUR', const Locale('de', 'DE'));

      // German locale uses comma as decimal separator and period as thousand separator
      expect(germanAmount, contains('€'));
      expect(germanAmount, contains('1.234,56'));
    });

    test('should convert invoice locale string to Locale object', () {
      final locale = InvoiceLocalizationService.getLocaleFromLanguage('de_DE');
      expect(locale.languageCode, equals('de'));
      expect(locale.countryCode, equals('DE'));
    });

        test('should format currency with German locale in PDF service', () async {
      // Initialize locale data for testing
      await initializeDateFormatting('de_DE');

      // Create test invoice with German locale
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client1',
        currency: 'EUR',
        locale: 'de_DE',
        subtotal: 1234.56,
        taxAmount: 123.46,
        total: 1358.02,
      );

      final client = Client(
        id: 'client1',
        name: 'Test Client',
        email: '<EMAIL>',
      );

      final businessInfo = BusinessInfo(
        name: 'Test Business',
        email: '<EMAIL>',
      );

      final pdfService = PDFService();

      // Generate PDF with German locale
      final pdfData = await pdfService.generateInvoicePDF(
        invoice: invoice,
        client: client,
        businessInfo: businessInfo,
        locale: InvoiceLocalizationService.getLocaleFromLanguage('de_DE'),
      );

      // Verify PDF was generated successfully
      expect(pdfData, isA<List<int>>());
      expect(pdfData.isNotEmpty, isTrue);

      // Verify it's a valid PDF (starts with %PDF)
      expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46]));
    });

    test('should handle different German number formats', () {
      // Test various amounts with German formatting
      final testCases = [
        {'amount': 1234.56, 'expected': '1.234,56'},
        {'amount': 1000000.00, 'expected': '1.000.000,00'},
        {'amount': 0.50, 'expected': '0,50'},
        {'amount': 123.45, 'expected': '123,45'},
      ];

      for (final testCase in testCases) {
        final formatted = CurrencyService.formatCurrency(
          testCase['amount'] as double,
          'EUR',
          const Locale('de', 'DE'),
        );

        expect(formatted, contains(testCase['expected'] as String));
        expect(formatted, contains('€'));
      }
    });

    test('should format currency for different currencies with German locale', () {
      // Test different currencies with German locale
      final currencies = ['EUR', 'USD', 'GBP'];

      for (final currency in currencies) {
        final formatted = CurrencyService.formatCurrency(
          1234.56,
          currency,
          const Locale('de', 'DE'),
        );

        expect(formatted, isNotEmpty);
        expect(formatted, contains('1.234,56')); // German number format
      }
    });
  });
}
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/pdf/pdf_formatter.dart';

void main() {
  group('PDF Service Period Support', () {
    test('should format service period translation correctly', () {
      // Test English translation
      final englishTranslation = PDFFormatter.getTranslation('service_period', 'en_US');
      expect(englishTranslation, equals('Service Period'));

      // Test German translation
      final germanTranslation = PDFFormatter.getTranslation('service_period', 'de_DE');
      expect(germanTranslation, equals('Leistungszeitraum'));
    });

    test('should format service period dates correctly', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      final formattedStart = PDFFormatter.formatDate(startDate, const Locale('en', 'US'));
      final formattedEnd = PDFFormatter.formatDate(endDate, const Locale('en', 'US'));
      
      expect(formattedStart, isNotEmpty);
      expect(formattedEnd, isNotEmpty);
      
      // Verify dates are different
      expect(formattedStart, isNot(equals(formattedEnd)));
    });

    test('should handle invoice with service period', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );

      expect(invoice.servicePeriodStart, equals(startDate));
      expect(invoice.servicePeriodEnd, equals(endDate));
      expect(invoice.isServicePeriodValid(), isTrue);
    });

    test('should handle invoice without service period', () {
      final invoice = Invoice(
        invoiceNumber: 'INV-002',
        clientId: 'client-1',
      );

      expect(invoice.servicePeriodStart, isNull);
      expect(invoice.servicePeriodEnd, isNull);
      expect(invoice.isServicePeriodValid(), isTrue); // Should be valid when both are null
    });

    test('should validate service period correctly', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      
      // Valid service period
      final validInvoice = Invoice(
        invoiceNumber: 'INV-003',
        clientId: 'client-1',
        servicePeriodStart: startDate,
        servicePeriodEnd: endDate,
      );
      expect(validInvoice.isServicePeriodValid(), isTrue);

      // Invalid service period (end before start)
      final invalidInvoice = Invoice(
        invoiceNumber: 'INV-004',
        clientId: 'client-1',
        servicePeriodStart: endDate,
        servicePeriodEnd: startDate,
      );
      expect(invalidInvoice.isServicePeriodValid(), isFalse);
    });

    test('should format dates for PDF display', () {
      final date = DateTime(2024, 1, 15);
      
      // Test with null locale (should use default formatting)
      final defaultFormat = PDFFormatter.formatDate(date, null);
      expect(defaultFormat, isNotEmpty);
      expect(defaultFormat.contains('2024'), isTrue);
      
      // Test that date formatting produces consistent results
      final sameDate = DateTime(2024, 1, 15);
      final sameFormat = PDFFormatter.formatDate(sameDate, null);
      expect(sameFormat, equals(defaultFormat));
    });
  });
}
import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'dart:io';

void main() {
  group('DatabaseService Invoice and Client Tests', () {
    late DatabaseService databaseService;

    setUpAll(() async {
      // Initialize Hive for testing with a temporary directory
      final tempDir = Directory.systemTemp.createTempSync('hive_test');
      Hive.init(tempDir.path);
    });

    setUp(() async {
      // Clear any existing boxes
      await Hive.deleteFromDisk();
      
      // Initialize database service
      await DatabaseService.initializeService();
      databaseService = DatabaseService();
    });

    tearDown(() async {
      // Clean up after each test
      await Hive.deleteFromDisk();
    });

    test('should save and retrieve clients', () async {
      // Create a test client
      final client = Client(
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '+1234567890',
      );

      // Save the client
      await databaseService.saveClient(client);

      // Retrieve the client
      final retrievedClient = await databaseService.getClient(client.id);

      // Verify the client was saved correctly
      expect(retrievedClient, isNotNull);
      expect(retrievedClient!.name, equals('Test Client'));
      expect(retrievedClient.email, equals('<EMAIL>'));
      expect(retrievedClient.phone, equals('+1234567890'));
    });

    test('should save and retrieve invoices', () async {
      // Create a test client first
      final client = Client(name: 'Test Client');
      await databaseService.saveClient(client);

      // Create a test invoice
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: client.id,
        currency: 'USD',
        locale: 'en_US',
      );

      // Save the invoice
      await databaseService.saveInvoice(invoice);

      // Retrieve the invoice
      final retrievedInvoice = await databaseService.getInvoice(invoice.id);

      // Verify the invoice was saved correctly
      expect(retrievedInvoice, isNotNull);
      expect(retrievedInvoice!.invoiceNumber, equals('INV-001'));
      expect(retrievedInvoice.clientId, equals(client.id));
      expect(retrievedInvoice.currency, equals('USD'));
      expect(retrievedInvoice.status, equals(InvoiceStatus.draft));
    });

    test('should search clients by name', () async {
      // Create test clients
      final client1 = Client(name: 'John Doe', email: '<EMAIL>');
      final client2 = Client(name: 'Jane Smith', email: '<EMAIL>');
      final client3 = Client(name: 'Bob Johnson', email: '<EMAIL>');

      await databaseService.saveClient(client1);
      await databaseService.saveClient(client2);
      await databaseService.saveClient(client3);

      // Search for clients with "John" in the name
      final searchResults = await databaseService.searchClients('John');

      // Should find both John Doe and Bob Johnson
      expect(searchResults.length, equals(2));
      expect(searchResults.any((c) => c.name == 'John Doe'), isTrue);
      expect(searchResults.any((c) => c.name == 'Bob Johnson'), isTrue);
    });

    test('should get invoices by status', () async {
      // Create a test client
      final client = Client(name: 'Test Client');
      await databaseService.saveClient(client);

      // Create test invoices with different statuses
      final draftInvoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: client.id,
        status: InvoiceStatus.draft,
      );
      final sentInvoice = Invoice(
        invoiceNumber: 'INV-002',
        clientId: client.id,
        status: InvoiceStatus.sent,
      );
      final paidInvoice = Invoice(
        invoiceNumber: 'INV-003',
        clientId: client.id,
        status: InvoiceStatus.paid,
      );

      await databaseService.saveInvoice(draftInvoice);
      await databaseService.saveInvoice(sentInvoice);
      await databaseService.saveInvoice(paidInvoice);

      // Get draft invoices
      final draftInvoices = await databaseService.getInvoicesByStatus(InvoiceStatus.draft);
      expect(draftInvoices.length, equals(1));
      expect(draftInvoices.first.invoiceNumber, equals('INV-001'));

      // Get sent invoices
      final sentInvoices = await databaseService.getInvoicesByStatus(InvoiceStatus.sent);
      expect(sentInvoices.length, equals(1));
      expect(sentInvoices.first.invoiceNumber, equals('INV-002'));
    });

    test('should prevent deleting client with existing invoices', () async {
      // Create a test client
      final client = Client(name: 'Test Client');
      await databaseService.saveClient(client);

      // Create an invoice for the client
      final invoice = Invoice(
        invoiceNumber: 'INV-001',
        clientId: client.id,
      );
      await databaseService.saveInvoice(invoice);

      // Try to delete the client - should throw an exception
      expect(
        () => databaseService.deleteClient(client.id),
        throwsA(isA<Exception>()),
      );
    });
  });
}
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';
import 'package:path_provider/path_provider.dart';

import 'pdf_service_integration_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  group('PDFService Integration Tests', () {
    late PDFService pdfService;
    late MockDatabaseService mockDatabaseService;
    late Invoice testInvoice;
    late Client testClient;
    late BusinessInfo testBusinessInfo;
    late List<TimeEntry> testTimeEntries;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
      pdfService = PDFService(databaseService: mockDatabaseService);

      // Create comprehensive test data
      testClient = Client(
        name: 'Acme Corporation',
        email: '<EMAIL>',
        phone: '******-0123',
        address: Address(
          street: '123 Business Ave',
          city: 'Business City',
          state: 'BC',
          postalCode: '12345',
          country: 'United States',
        ),
        taxId: 'TAX-*********',
      );

      testTimeEntries = [
        TimeEntry(
          id: 'entry1',
          projectId: 'project1',
          date: '2024-01-10',
          start: '09:00',
          end: '12:00',
        ),
        TimeEntry(
          id: 'entry2',
          projectId: 'project1',
          date: '2024-01-10',
          start: '13:00',
          end: '17:00',
        ),
        TimeEntry(
          id: 'entry3',
          projectId: 'project2',
          date: '2024-01-11',
          start: '09:00',
          end: '17:00',
        ),
      ];

      testInvoice = Invoice(
        invoiceNumber: 'INV-2024-001',
        clientId: testClient.id,
        timeEntryIds: testTimeEntries.map((e) => e.id).toList(),
        currency: 'USD',
        locale: 'en_US',
        issueDate: DateTime(2024, 1, 15),
        dueDate: DateTime(2024, 2, 15),
        status: InvoiceStatus.sent,
        subtotal: 2400.0,
        taxRate: 8.5,
        taxAmount: 204.0,
        total: 2604.0,
        notes: 'Payment terms: Net 30 days. Late fees may apply.',
        additionalItems: [
          InvoiceLineItem(
            description: 'Web Development Services',
            quantity: 20.0,
            rate: 120.0,
            type: InvoiceLineItemType.timeEntry,
          ),
          InvoiceLineItem(
            description: 'Travel Expenses',
            quantity: 1.0,
            rate: 150.0,
            type: InvoiceLineItemType.expense,
          ),
        ],
      );

      testBusinessInfo = BusinessInfo(
        name: 'Tech Solutions LLC',
        email: '<EMAIL>',
        phone: '******-0987',
        address: Address(
          street: '456 Tech Park Dr',
          city: 'Innovation City',
          state: 'IC',
          postalCode: '67890',
          country: 'United States',
        ),
        website: 'https://techsolutions.com',
        taxId: 'BUS-*********',
      );
    });

    group('PDF Export and Sharing Integration', () {
      testWidgets('should generate and save PDF with progress tracking', (tester) async {
        // Arrange
        double? lastProgress;
        final progressValues = <double>[];

        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.professional,
          timeEntries: testTimeEntries,
          onProgress: (progress) {
            lastProgress = progress;
            progressValues.add(progress);
          },
        );

        // Assert
        expect(filePath, isA<String>());
        expect(filePath.isNotEmpty, isTrue);
        expect(filePath.endsWith('.pdf'), isTrue);
        expect(lastProgress, equals(1.0));
        expect(progressValues.isNotEmpty, isTrue);
        expect(progressValues.first, lessThan(1.0));
        expect(progressValues.last, equals(1.0));

        // Verify file exists and has content
        final file = File(filePath);
        expect(await file.exists(), isTrue);
        final fileSize = await file.length();
        expect(fileSize, greaterThan(1000)); // Should be at least 1KB
      });

      testWidgets('should generate PDF with progress for large invoices', (tester) async {
        // Arrange - Create large invoice with many line items
        final largeInvoice = testInvoice.copyWith(
          additionalItems: List.generate(60, (index) => 
            InvoiceLineItem(
              description: 'Service Item ${index + 1}',
              quantity: 1.0,
              rate: 100.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ),
        );

        final largeTimeEntries = List.generate(120, (index) =>
          TimeEntry(
            id: 'entry_$index',
            projectId: 'project1',
            date: '2024-01-${(index % 30) + 1}',
            start: '09:00',
            end: '17:00',
          ),
        );

        double? lastProgress;
        final progressValues = <double>[];

        // Act
        final pdfData = await pdfService.generateInvoicePDFWithProgress(
          invoice: largeInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          template: InvoiceTemplate.detailed,
          timeEntries: largeTimeEntries,
          onProgress: (progress) {
            lastProgress = progress;
            progressValues.add(progress);
          },
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
        expect(lastProgress, equals(1.0));
        expect(progressValues.length, greaterThan(1));
        
        // Verify PDF header
        expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46])); // %PDF
      });

      testWidgets('should handle custom directory for PDF saving', (tester) async {
        // Arrange
        final tempDir = await getTemporaryDirectory();
        final customDir = Directory('${tempDir.path}/custom_invoices');
        await customDir.create(recursive: true);

        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
          customDirectory: customDir.path,
          customFileName: 'custom_invoice_name.pdf',
        );

        // Assert
        expect(filePath, contains(customDir.path));
        expect(filePath, contains('custom_invoice_name.pdf'));
        
        final file = File(filePath);
        expect(await file.exists(), isTrue);
        
        // Cleanup
        await customDir.delete(recursive: true);
      });

      testWidgets('should export multiple invoices in batch', (tester) async {
        // Arrange
        final invoice2 = testInvoice.copyWith(
          invoiceNumber: 'INV-2024-002',
          total: 1500.0,
        );
        final invoice3 = testInvoice.copyWith(
          invoiceNumber: 'INV-2024-003',
          total: 3000.0,
        );

        final invoices = [testInvoice, invoice2, invoice3];
        final clients = {
          testClient.id: testClient,
        };
        final timeEntriesMap = {
          testInvoice.id: testTimeEntries,
          invoice2.id: testTimeEntries.take(2).toList(),
          invoice3.id: testTimeEntries,
        };

        double? lastProgress;
        final progressValues = <double>[];

        // Act
        final filePaths = await pdfService.exportInvoiceBatch(
          invoices: invoices,
          clients: clients,
          businessInfo: testBusinessInfo,
          timeEntriesMap: timeEntriesMap,
          onProgress: (progress) {
            lastProgress = progress;
            progressValues.add(progress);
          },
        );

        // Assert
        expect(filePaths, hasLength(3));
        expect(lastProgress, equals(1.0));
        expect(progressValues.length, greaterThanOrEqualTo(3));

        // Verify all files exist
        for (final filePath in filePaths) {
          final file = File(filePath);
          expect(await file.exists(), isTrue);
          expect(await file.length(), greaterThan(1000));
        }
      });

      testWidgets('should handle batch export with missing client', (tester) async {
        // Arrange
        final invoices = [testInvoice];
        final clients = <String, Client>{}; // Empty clients map

        // Act & Assert
        expect(
          () => pdfService.exportInvoiceBatch(
            invoices: invoices,
            clients: clients,
            businessInfo: testBusinessInfo,
          ),
          throwsA(isA<PDFGenerationException>()),
        );
      });
    });



    group('Error Handling and Edge Cases', () {
      testWidgets('should handle PDF generation errors gracefully', (tester) async {
        // Arrange - Create invalid invoice that might cause errors
        final invalidInvoice = Invoice(
          invoiceNumber: '',
          clientId: '',
          additionalItems: [],
        );

        // Act & Assert
        expect(
          () => pdfService.generateInvoicePDF(
            invoice: invalidInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
          ),
          throwsA(isA<PDFGenerationException>()),
        );
      });

      testWidgets('should handle file system errors during save', (tester) async {
        // Arrange - Try to save to invalid directory
        const invalidDirectory = '/invalid/path/that/does/not/exist';

        // Act & Assert
        expect(
          () => pdfService.generateAndSavePDF(
            invoice: testInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
            customDirectory: invalidDirectory,
          ),
          throwsA(isA<PDFGenerationException>()),
        );
      });

      testWidgets('should handle very large invoice data', (tester) async {
        // Arrange - Create invoice with extremely long descriptions
        final longDescription = 'A' * 10000; // 10KB description
        final largeInvoice = testInvoice.copyWith(
          notes: longDescription,
          additionalItems: [
            InvoiceLineItem(
              description: longDescription,
              quantity: 1.0,
              rate: 100.0,
              type: InvoiceLineItemType.timeEntry,
            ),
          ],
        );

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: largeInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(pdfData.isNotEmpty, isTrue);
      });

      testWidgets('should handle special characters in file names', (tester) async {
        // Arrange
        final specialCharInvoice = testInvoice.copyWith(
          invoiceNumber: 'INV/2024-001#@!',
        );

        // Act
        final filePath = await pdfService.generateAndSavePDF(
          invoice: specialCharInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        // Assert
        expect(filePath, isA<String>());
        expect(filePath.endsWith('.pdf'), isTrue);
        // Should not contain special characters in filename
        final fileName = filePath.split('/').last;
        expect(fileName, isNot(contains('/')));
        expect(fileName, isNot(contains('#')));
        expect(fileName, isNot(contains('@')));
        expect(fileName, isNot(contains('!')));
      });
    });

    group('Template Variations Integration', () {
      testWidgets('should generate all template types successfully', (tester) async {
        for (final template in InvoiceTemplate.values) {
          // Act
          final pdfData = await pdfService.generateInvoicePDF(
            invoice: testInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
            template: template,
            timeEntries: testTimeEntries,
          );

          // Assert
          expect(pdfData, isA<Uint8List>());
          expect(pdfData.isNotEmpty, isTrue);
          expect(pdfData.take(4), equals([0x25, 0x50, 0x44, 0x46])); // %PDF
        }
      });

      testWidgets('should handle different currencies and locales', (tester) async {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY'];
        final locales = [
          const Locale('en', 'US'),
          const Locale('de', 'DE'),
          const Locale('fr', 'FR'),
          const Locale('ja', 'JP'),
        ];

        for (int i = 0; i < currencies.length; i++) {
          // Arrange
          final currencyInvoice = testInvoice.copyWith(
            currency: currencies[i],
            locale: locales[i].toString(),
          );

          // Act
          final pdfData = await pdfService.generateInvoicePDF(
            invoice: currencyInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
            locale: locales[i],
          );

          // Assert
          expect(pdfData, isA<Uint8List>());
          expect(pdfData.isNotEmpty, isTrue);
        }
      });
    });

    group('Utility Functions', () {
      testWidgets('should handle PDF generation with different templates', (tester) async {
        // Act - Test that all templates work
        for (final template in InvoiceTemplate.values) {
          final pdfData = await pdfService.generateInvoicePDF(
            invoice: testInvoice,
            client: testClient,
            businessInfo: testBusinessInfo,
            template: template,
          );
          
          // Assert
          expect(pdfData, isA<Uint8List>());
          expect(pdfData.isNotEmpty, isTrue);
        }
      });
    });

    group('Performance Tests', () {
      testWidgets('should generate PDF within reasonable time', (tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        final pdfData = await pdfService.generateInvoicePDF(
          invoice: testInvoice,
          client: testClient,
          businessInfo: testBusinessInfo,
        );

        stopwatch.stop();

        // Assert
        expect(pdfData, isA<Uint8List>());
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete within 5 seconds
      });

      testWidgets('should handle concurrent PDF generation', (tester) async {
        // Arrange
        final futures = <Future<Uint8List>>[];

        // Act - Generate multiple PDFs concurrently
        for (int i = 0; i < 3; i++) {
          final invoice = testInvoice.copyWith(invoiceNumber: 'INV-2024-00$i');
          futures.add(
            pdfService.generateInvoicePDF(
              invoice: invoice,
              client: testClient,
              businessInfo: testBusinessInfo,
            ),
          );
        }

        final results = await Future.wait(futures);

        // Assert
        expect(results, hasLength(3));
        for (final result in results) {
          expect(result, isA<Uint8List>());
          expect(result.isNotEmpty, isTrue);
        }
      });
    });
  });
}
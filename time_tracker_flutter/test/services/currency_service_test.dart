import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';

void main() {
  group('CurrencyService', () {
    group('getSupportedCurrencies', () {
      test('should return list of supported currency codes', () {
        final currencies = CurrencyService.getSupportedCurrencies();
        
        expect(currencies, isA<List<String>>());
        expect(currencies, isNotEmpty);
        expect(currencies, contains('USD'));
        expect(currencies, contains('EUR'));
        expect(currencies, contains('GBP'));
        expect(currencies, contains('JPY'));
      });
    });

    group('getCurrencyInfo', () {
      test('should return currency info for valid currency code', () {
        final info = CurrencyService.getCurrencyInfo('USD');
        
        expect(info, isNotNull);
        expect(info!['name'], equals('US Dollar'));
        expect(info['symbol'], equals('\$'));
        expect(info['decimalDigits'], equals(2));
        expect(info['code'], equals('USD'));
      });

      test('should return null for invalid currency code', () {
        final info = CurrencyService.getCurrencyInfo('INVALID');
        expect(info, isNull);
      });

      test('should be case insensitive', () {
        final info = CurrencyService.getCurrencyInfo('usd');
        expect(info, isNotNull);
        expect(info!['code'], equals('USD'));
      });
    });

    group('getCurrencySymbol', () {
      test('should return correct symbols for supported currencies', () {
        expect(CurrencyService.getCurrencySymbol('USD'), equals('\$'));
        expect(CurrencyService.getCurrencySymbol('EUR'), equals('€'));
        expect(CurrencyService.getCurrencySymbol('GBP'), equals('£'));
        expect(CurrencyService.getCurrencySymbol('JPY'), equals('¥'));
        expect(CurrencyService.getCurrencySymbol('CAD'), equals('C\$'));
        expect(CurrencyService.getCurrencySymbol('AUD'), equals('A\$'));
        expect(CurrencyService.getCurrencySymbol('CHF'), equals('CHF'));
        expect(CurrencyService.getCurrencySymbol('CNY'), equals('¥'));
        expect(CurrencyService.getCurrencySymbol('INR'), equals('₹'));
        expect(CurrencyService.getCurrencySymbol('BRL'), equals('R\$'));
      });

      test('should return currency code for unsupported currency', () {
        expect(CurrencyService.getCurrencySymbol('INVALID'), equals('INVALID'));
      });
    });

    group('getCurrencyName', () {
      test('should return correct names for supported currencies', () {
        expect(CurrencyService.getCurrencyName('USD'), equals('US Dollar'));
        expect(CurrencyService.getCurrencyName('EUR'), equals('Euro'));
        expect(CurrencyService.getCurrencyName('GBP'), equals('British Pound'));
        expect(CurrencyService.getCurrencyName('JPY'), equals('Japanese Yen'));
      });

      test('should return currency code for unsupported currency', () {
        expect(CurrencyService.getCurrencyName('INVALID'), equals('INVALID'));
      });
    });

    group('getCurrencyDecimalDigits', () {
      test('should return correct decimal digits for currencies', () {
        expect(CurrencyService.getCurrencyDecimalDigits('USD'), equals(2));
        expect(CurrencyService.getCurrencyDecimalDigits('EUR'), equals(2));
        expect(CurrencyService.getCurrencyDecimalDigits('JPY'), equals(0));
        expect(CurrencyService.getCurrencyDecimalDigits('GBP'), equals(2));
      });

      test('should return 2 for unsupported currency', () {
        expect(CurrencyService.getCurrencyDecimalDigits('INVALID'), equals(2));
      });
    });

    group('isCurrencySupported', () {
      test('should return true for supported currencies', () {
        expect(CurrencyService.isCurrencySupported('USD'), isTrue);
        expect(CurrencyService.isCurrencySupported('EUR'), isTrue);
        expect(CurrencyService.isCurrencySupported('usd'), isTrue); // case insensitive
      });

      test('should return false for unsupported currencies', () {
        expect(CurrencyService.isCurrencySupported('INVALID'), isFalse);
        expect(CurrencyService.isCurrencySupported(''), isFalse);
      });
    });

    group('formatCurrency', () {
      test('should format USD correctly with default locale', () {
        final result = CurrencyService.formatCurrency(1234.56, 'USD');
        expect(result, contains('\$'));
        expect(result, contains('1,234.56'));
      });

      test('should format EUR correctly with German locale', () {
        final result = CurrencyService.formatCurrency(1234.56, 'EUR', const Locale('de', 'DE'));
        expect(result, contains('€'));
        // German locale uses comma as decimal separator and period as thousand separator
        expect(result, contains('1.234,56'));
      });

      test('should format JPY correctly (no decimal places)', () {
        final result = CurrencyService.formatCurrency(1234.56, 'JPY');
        expect(result, contains('¥'));
        expect(result, contains('1,235')); // Should round to nearest integer
      });

      test('should handle zero amounts', () {
        final result = CurrencyService.formatCurrency(0.0, 'USD');
        expect(result, contains('\$0.00'));
      });

      test('should handle negative amounts', () {
        final result = CurrencyService.formatCurrency(-123.45, 'USD');
        expect(result, contains('-'));
        expect(result, contains('123.45'));
      });

      test('should fallback to USD for unsupported currency', () {
        final result = CurrencyService.formatCurrency(100.0, 'INVALID');
        expect(result, contains('\$'));
        expect(result, contains('100.00'));
      });

      test('should handle formatting errors gracefully', () {
        // Test with extreme values that might cause formatting issues
        final result = CurrencyService.formatCurrency(double.maxFinite, 'USD');
        expect(result, isA<String>());
        expect(result, isNotEmpty);
      });
    });

    group('formatCurrencyAmount', () {
      test('should format amount without currency symbol', () {
        final result = CurrencyService.formatCurrencyAmount(1234.56, 'USD');
        expect(result, equals('1,234.56'));
        expect(result, isNot(contains('\$')));
      });

      test('should format with German locale (comma decimal)', () {
        final result = CurrencyService.formatCurrencyAmount(1234.56, 'EUR', const Locale('de', 'DE'));
        expect(result, contains('1.234,56'));
      });

      test('should format JPY without decimals', () {
        final result = CurrencyService.formatCurrencyAmount(1234.56, 'JPY');
        expect(result, equals('1,235'));
      });
    });

    group('validateCurrencyFormat', () {
      test('should return true for supported currencies with valid locale', () {
        expect(CurrencyService.validateCurrencyFormat('USD', const Locale('en', 'US')), isTrue);
        expect(CurrencyService.validateCurrencyFormat('EUR', const Locale('de', 'DE')), isTrue);
      });

      test('should return false for unsupported currencies', () {
        expect(CurrencyService.validateCurrencyFormat('INVALID', const Locale('en', 'US')), isFalse);
      });

      test('should handle null locale', () {
        expect(CurrencyService.validateCurrencyFormat('USD', null), isTrue);
      });
    });

    group('getPopularCurrencies', () {
      test('should return list of popular currencies', () {
        final popular = CurrencyService.getPopularCurrencies();
        
        expect(popular, isA<List<String>>());
        expect(popular, contains('USD'));
        expect(popular, contains('EUR'));
        expect(popular, contains('GBP'));
        expect(popular.length, lessThanOrEqualTo(CurrencyService.getSupportedCurrencies().length));
      });
    });

    group('getCurrenciesSortedByName', () {
      test('should return currencies sorted alphabetically by name', () {
        final currencies = CurrencyService.getCurrenciesSortedByName();
        
        expect(currencies, isA<List<Map<String, dynamic>>>());
        expect(currencies, isNotEmpty);
        
        // Check if sorted by name
        for (int i = 1; i < currencies.length; i++) {
          expect(
            currencies[i - 1]['name'].compareTo(currencies[i]['name']),
            lessThanOrEqualTo(0),
          );
        }
        
        // Check structure
        final firstCurrency = currencies.first;
        expect(firstCurrency, containsPair('code', isA<String>()));
        expect(firstCurrency, containsPair('name', isA<String>()));
        expect(firstCurrency, containsPair('symbol', isA<String>()));
        expect(firstCurrency, containsPair('decimalDigits', isA<int>()));
      });
    });

    group('parseCurrencyString', () {
      test('should parse USD currency string correctly', () {
        final result = CurrencyService.parseCurrencyString('\$1,234.56', 'USD');
        expect(result, equals(1234.56));
      });

      test('should parse EUR currency string with German locale', () {
        final result = CurrencyService.parseCurrencyString('€1.234,56', 'EUR', const Locale('de', 'DE'));
        expect(result, equals(1234.56));
      });

      test('should handle strings without currency symbol', () {
        final result = CurrencyService.parseCurrencyString('1,234.56', 'USD');
        expect(result, equals(1234.56));
      });

      test('should return null for invalid strings', () {
        final result = CurrencyService.parseCurrencyString('invalid', 'USD');
        expect(result, isNull);
      });

      test('should handle empty strings', () {
        final result = CurrencyService.parseCurrencyString('', 'USD');
        expect(result, isNull);
      });
    });

    group('formatCurrencyCompact', () {
      test('should format large amounts with K suffix', () {
        final result = CurrencyService.formatCurrencyCompact(1500.0, 'USD');
        expect(result, equals('\$1.5K'));
      });

      test('should format very large amounts with M suffix', () {
        final result = CurrencyService.formatCurrencyCompact(1500000.0, 'USD');
        expect(result, equals('\$1.5M'));
      });

      test('should format small amounts normally', () {
        final result = CurrencyService.formatCurrencyCompact(123.45, 'USD');
        expect(result, contains('\$123.45'));
      });

      test('should handle negative amounts', () {
        final result = CurrencyService.formatCurrencyCompact(-1500.0, 'USD');
        expect(result, equals('\$-1.5K'));
      });
    });

    group('getCurrencyFormattingExample', () {
      test('should return formatted example amount', () {
        final result = CurrencyService.getCurrencyFormattingExample('USD');
        expect(result, contains('\$'));
        expect(result, contains('1,234.56'));
      });

      test('should work with different currencies', () {
        final result = CurrencyService.getCurrencyFormattingExample('EUR');
        expect(result, contains('€'));
      });
    });

    group('locale helper methods', () {
      test('localeUsesCommaAsDecimal should identify comma decimal locales', () {
        expect(CurrencyService.localeUsesCommaAsDecimal(const Locale('de', 'DE')), isTrue);
        expect(CurrencyService.localeUsesCommaAsDecimal(const Locale('fr', 'FR')), isTrue);
        expect(CurrencyService.localeUsesCommaAsDecimal(const Locale('es', 'ES')), isTrue);
        expect(CurrencyService.localeUsesCommaAsDecimal(const Locale('en', 'US')), isFalse);
        expect(CurrencyService.localeUsesCommaAsDecimal(const Locale('en', 'GB')), isFalse);
        expect(CurrencyService.localeUsesCommaAsDecimal(null), isFalse);
      });

      test('getDecimalSeparator should return correct separator', () {
        expect(CurrencyService.getDecimalSeparator(const Locale('de', 'DE')), equals(','));
        expect(CurrencyService.getDecimalSeparator(const Locale('en', 'US')), equals('.'));
        expect(CurrencyService.getDecimalSeparator(null), equals('.'));
      });

      test('getThousandSeparator should return correct separator', () {
        expect(CurrencyService.getThousandSeparator(const Locale('de', 'DE')), equals('.'));
        expect(CurrencyService.getThousandSeparator(const Locale('en', 'US')), equals(','));
        expect(CurrencyService.getThousandSeparator(null), equals(','));
      });
    });

    group('edge cases and error handling', () {
      test('should handle very small amounts', () {
        final result = CurrencyService.formatCurrency(0.01, 'USD');
        expect(result, contains('\$0.01'));
      });

      test('should handle very large amounts', () {
        final result = CurrencyService.formatCurrency(999999999.99, 'USD');
        expect(result, isA<String>());
        expect(result, contains('\$'));
      });

      test('should handle case insensitive currency codes', () {
        final result1 = CurrencyService.formatCurrency(100.0, 'usd');
        final result2 = CurrencyService.formatCurrency(100.0, 'USD');
        expect(result1, equals(result2));
      });

      test('should handle null and empty currency codes gracefully', () {
        // These should fallback to USD
        final result1 = CurrencyService.formatCurrency(100.0, '');
        expect(result1, contains('\$'));
      });
    });
  });
}
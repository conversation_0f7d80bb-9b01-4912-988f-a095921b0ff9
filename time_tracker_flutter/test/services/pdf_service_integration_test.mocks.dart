// Mocks generated by Mockito 5.4.5 from annotations
// in time_tracker_flutter/test/services/pdf_service_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:time_tracker_flutter/models/backup_data.dart' as _i2;
import 'package:time_tracker_flutter/models/invoice_models.dart' as _i3;
import 'package:time_tracker_flutter/models/location_area.dart' as _i8;
import 'package:time_tracker_flutter/models/project.dart' as _i7;
import 'package:time_tracker_flutter/models/time_entry.dart' as _i9;
import 'package:time_tracker_flutter/services/cloud_backup_service.dart' as _i4;
import 'package:time_tracker_flutter/services/database_service.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBackupData_0 extends _i1.SmartFake implements _i2.BackupData {
  _FakeBackupData_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInvoiceSettings_1 extends _i1.SmartFake
    implements _i3.InvoiceSettings {
  _FakeInvoiceSettings_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCloudBackupService_2 extends _i1.SmartFake
    implements _i4.CloudBackupService {
  _FakeCloudBackupService_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [DatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDatabaseService extends _i1.Mock implements _i5.DatabaseService {
  MockDatabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  void addDataChangeListener(dynamic Function()? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #addDataChangeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeDataChangeListener(dynamic Function()? listener) =>
      super.noSuchMethod(
        Invocation.method(
          #removeDataChangeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyDataChange() => super.noSuchMethod(
        Invocation.method(
          #notifyDataChange,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<List<_i7.Project>> getProjects() => (super.noSuchMethod(
        Invocation.method(
          #getProjects,
          [],
        ),
        returnValue: _i6.Future<List<_i7.Project>>.value(<_i7.Project>[]),
      ) as _i6.Future<List<_i7.Project>>);

  @override
  _i6.Future<_i7.Project?> getProject(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getProject,
          [id],
        ),
        returnValue: _i6.Future<_i7.Project?>.value(),
      ) as _i6.Future<_i7.Project?>);

  @override
  _i6.Future<void> saveProject(_i7.Project? project) => (super.noSuchMethod(
        Invocation.method(
          #saveProject,
          [project],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteProject(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteProject,
          [id],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<List<_i8.LocationArea>> getLocationAreas({String? projectId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLocationAreas,
          [],
          {#projectId: projectId},
        ),
        returnValue:
            _i6.Future<List<_i8.LocationArea>>.value(<_i8.LocationArea>[]),
      ) as _i6.Future<List<_i8.LocationArea>>);

  @override
  _i6.Future<_i8.LocationArea?> getLocationArea(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLocationArea,
          [id],
        ),
        returnValue: _i6.Future<_i8.LocationArea?>.value(),
      ) as _i6.Future<_i8.LocationArea?>);

  @override
  _i6.Future<void> saveLocationArea(_i8.LocationArea? area) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveLocationArea,
          [area],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteLocationArea(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteLocationArea,
          [id],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<List<_i9.TimeEntry>> getTimeEntries({
    String? projectId,
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTimeEntries,
          [],
          {
            #projectId: projectId,
            #startDate: startDate,
            #endDate: endDate,
          },
        ),
        returnValue: _i6.Future<List<_i9.TimeEntry>>.value(<_i9.TimeEntry>[]),
      ) as _i6.Future<List<_i9.TimeEntry>>);

  @override
  _i6.Future<_i9.TimeEntry?> getTimeEntry(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getTimeEntry,
          [id],
        ),
        returnValue: _i6.Future<_i9.TimeEntry?>.value(),
      ) as _i6.Future<_i9.TimeEntry?>);

  @override
  _i6.Future<List<_i9.TimeEntry>> getActiveTimeEntries({String? projectId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveTimeEntries,
          [],
          {#projectId: projectId},
        ),
        returnValue: _i6.Future<List<_i9.TimeEntry>>.value(<_i9.TimeEntry>[]),
      ) as _i6.Future<List<_i9.TimeEntry>>);

  @override
  _i6.Future<_i9.TimeEntry?> getActiveTimeEntryForProject(String? projectId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveTimeEntryForProject,
          [projectId],
        ),
        returnValue: _i6.Future<_i9.TimeEntry?>.value(),
      ) as _i6.Future<_i9.TimeEntry?>);

  @override
  _i6.Future<void> saveTimeEntry(_i9.TimeEntry? entry) => (super.noSuchMethod(
        Invocation.method(
          #saveTimeEntry,
          [entry],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteTimeEntry(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteTimeEntry,
          [id],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteMultipleTimeEntries(List<String>? ids) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteMultipleTimeEntries,
          [ids],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<List<_i3.Invoice>> getInvoices({
    String? clientId,
    DateTime? startDate,
    DateTime? endDate,
    _i3.InvoiceStatus? status,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInvoices,
          [],
          {
            #clientId: clientId,
            #startDate: startDate,
            #endDate: endDate,
            #status: status,
          },
        ),
        returnValue: _i6.Future<List<_i3.Invoice>>.value(<_i3.Invoice>[]),
      ) as _i6.Future<List<_i3.Invoice>>);

  @override
  _i6.Future<_i3.Invoice?> getInvoice(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getInvoice,
          [id],
        ),
        returnValue: _i6.Future<_i3.Invoice?>.value(),
      ) as _i6.Future<_i3.Invoice?>);

  @override
  _i6.Future<_i3.Invoice?> getInvoiceByNumber(String? invoiceNumber) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInvoiceByNumber,
          [invoiceNumber],
        ),
        returnValue: _i6.Future<_i3.Invoice?>.value(),
      ) as _i6.Future<_i3.Invoice?>);

  @override
  _i6.Future<List<_i3.Invoice>> getInvoicesByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInvoicesByDateRange,
          [
            startDate,
            endDate,
          ],
        ),
        returnValue: _i6.Future<List<_i3.Invoice>>.value(<_i3.Invoice>[]),
      ) as _i6.Future<List<_i3.Invoice>>);

  @override
  _i6.Future<List<_i3.Invoice>> getInvoicesByClient(String? clientId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInvoicesByClient,
          [clientId],
        ),
        returnValue: _i6.Future<List<_i3.Invoice>>.value(<_i3.Invoice>[]),
      ) as _i6.Future<List<_i3.Invoice>>);

  @override
  _i6.Future<List<_i3.Invoice>> getInvoicesByStatus(
          _i3.InvoiceStatus? status) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInvoicesByStatus,
          [status],
        ),
        returnValue: _i6.Future<List<_i3.Invoice>>.value(<_i3.Invoice>[]),
      ) as _i6.Future<List<_i3.Invoice>>);

  @override
  _i6.Future<List<_i3.Invoice>> getOverdueInvoices() => (super.noSuchMethod(
        Invocation.method(
          #getOverdueInvoices,
          [],
        ),
        returnValue: _i6.Future<List<_i3.Invoice>>.value(<_i3.Invoice>[]),
      ) as _i6.Future<List<_i3.Invoice>>);

  @override
  _i6.Future<void> saveInvoice(_i3.Invoice? invoice) => (super.noSuchMethod(
        Invocation.method(
          #saveInvoice,
          [invoice],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteInvoice(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteInvoice,
          [id],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> updateInvoiceStatus(
    String? invoiceId,
    _i3.InvoiceStatus? status,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateInvoiceStatus,
          [
            invoiceId,
            status,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Set<String>> getBilledTimeEntryIds() => (super.noSuchMethod(
        Invocation.method(
          #getBilledTimeEntryIds,
          [],
        ),
        returnValue: _i6.Future<Set<String>>.value(<String>{}),
      ) as _i6.Future<Set<String>>);

  @override
  _i6.Future<List<_i3.Client>> getClients() => (super.noSuchMethod(
        Invocation.method(
          #getClients,
          [],
        ),
        returnValue: _i6.Future<List<_i3.Client>>.value(<_i3.Client>[]),
      ) as _i6.Future<List<_i3.Client>>);

  @override
  _i6.Future<_i3.Client?> getClient(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getClient,
          [id],
        ),
        returnValue: _i6.Future<_i3.Client?>.value(),
      ) as _i6.Future<_i3.Client?>);

  @override
  _i6.Future<List<_i3.Client>> searchClients(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchClients,
          [query],
        ),
        returnValue: _i6.Future<List<_i3.Client>>.value(<_i3.Client>[]),
      ) as _i6.Future<List<_i3.Client>>);

  @override
  _i6.Future<void> saveClient(_i3.Client? client) => (super.noSuchMethod(
        Invocation.method(
          #saveClient,
          [client],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> deleteClient(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteClient,
          [id],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i2.BackupData> exportDatabase() => (super.noSuchMethod(
        Invocation.method(
          #exportDatabase,
          [],
        ),
        returnValue: _i6.Future<_i2.BackupData>.value(_FakeBackupData_0(
          this,
          Invocation.method(
            #exportDatabase,
            [],
          ),
        )),
      ) as _i6.Future<_i2.BackupData>);

  @override
  _i6.Future<void> importDatabase(_i2.BackupData? backupData) =>
      (super.noSuchMethod(
        Invocation.method(
          #importDatabase,
          [backupData],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> saveBackupToFile(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #saveBackupToFile,
          [filePath],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> restoreFromFile(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #restoreFromFile,
          [filePath],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setAutoBackup(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setAutoBackup,
          [enabled],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> isAutoBackupEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isAutoBackupEnabled,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> setCloudAutoBackup(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setCloudAutoBackup,
          [enabled],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> isCloudAutoBackupEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isCloudAutoBackupEnabled,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> setCustomDateFormat(String? format) => (super.noSuchMethod(
        Invocation.method(
          #setCustomDateFormat,
          [format],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<String?> getCustomDateFormat() => (super.noSuchMethod(
        Invocation.method(
          #getCustomDateFormat,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<void> setUseCustomDateFormat(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setUseCustomDateFormat,
          [enabled],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> isCustomDateFormatEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isCustomDateFormatEnabled,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> setCustomLocale(String? localeString) => (super.noSuchMethod(
        Invocation.method(
          #setCustomLocale,
          [localeString],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<String?> getCustomLocale() => (super.noSuchMethod(
        Invocation.method(
          #getCustomLocale,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<void> setUseCustomLocale(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setUseCustomLocale,
          [enabled],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> isCustomLocaleEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isCustomLocaleEnabled,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> setLastRestoreDate(DateTime? date) => (super.noSuchMethod(
        Invocation.method(
          #setLastRestoreDate,
          [date],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<DateTime?> getLastRestoreDate() => (super.noSuchMethod(
        Invocation.method(
          #getLastRestoreDate,
          [],
        ),
        returnValue: _i6.Future<DateTime?>.value(),
      ) as _i6.Future<DateTime?>);

  @override
  _i6.Future<_i3.InvoiceSettings> getInvoiceSettings() => (super.noSuchMethod(
        Invocation.method(
          #getInvoiceSettings,
          [],
        ),
        returnValue:
            _i6.Future<_i3.InvoiceSettings>.value(_FakeInvoiceSettings_1(
          this,
          Invocation.method(
            #getInvoiceSettings,
            [],
          ),
        )),
      ) as _i6.Future<_i3.InvoiceSettings>);

  @override
  _i6.Future<void> saveInvoiceSettings(_i3.InvoiceSettings? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveInvoiceSettings,
          [settings],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> updateInvoiceSettings({
    String? defaultCurrency,
    String? defaultLocale,
    _i3.InvoiceTemplate? defaultTemplate,
    _i3.BusinessInfo? businessInfo,
    String? invoiceNumberPrefix,
    String? invoiceNumberDateFormat,
    int? invoiceNumberSequenceLength,
    double? defaultTaxRate,
    int? defaultDueDays,
    String? defaultNotes,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateInvoiceSettings,
          [],
          {
            #defaultCurrency: defaultCurrency,
            #defaultLocale: defaultLocale,
            #defaultTemplate: defaultTemplate,
            #businessInfo: businessInfo,
            #invoiceNumberPrefix: invoiceNumberPrefix,
            #invoiceNumberDateFormat: invoiceNumberDateFormat,
            #invoiceNumberSequenceLength: invoiceNumberSequenceLength,
            #defaultTaxRate: defaultTaxRate,
            #defaultDueDays: defaultDueDays,
            #defaultNotes: defaultNotes,
          },
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i3.BusinessInfo?> getBusinessInfo() => (super.noSuchMethod(
        Invocation.method(
          #getBusinessInfo,
          [],
        ),
        returnValue: _i6.Future<_i3.BusinessInfo?>.value(),
      ) as _i6.Future<_i3.BusinessInfo?>);

  @override
  _i6.Future<void> saveBusinessInfo(_i3.BusinessInfo? businessInfo) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveBusinessInfo,
          [businessInfo],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<String> backupToCloud() => (super.noSuchMethod(
        Invocation.method(
          #backupToCloud,
          [],
        ),
        returnValue: _i6.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #backupToCloud,
            [],
          ),
        )),
      ) as _i6.Future<String>);

  @override
  _i6.Future<void> restoreFromCloud(String? filename) => (super.noSuchMethod(
        Invocation.method(
          #restoreFromCloud,
          [filename],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> restoreFromMostRecentValidBackup(List<String>? filenames) =>
      (super.noSuchMethod(
        Invocation.method(
          #restoreFromMostRecentValidBackup,
          [filenames],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<List<String>> listCloudBackups({bool? forceRefresh = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #listCloudBackups,
          [],
          {#forceRefresh: forceRefresh},
        ),
        returnValue: _i6.Future<List<String>>.value(<String>[]),
      ) as _i6.Future<List<String>>);

  @override
  _i6.Future<List<String>> deleteCloudBackup(String? filename) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteCloudBackup,
          [filename],
        ),
        returnValue: _i6.Future<List<String>>.value(<String>[]),
      ) as _i6.Future<List<String>>);

  @override
  _i6.Future<String> generateRecoveryCode() => (super.noSuchMethod(
        Invocation.method(
          #generateRecoveryCode,
          [],
        ),
        returnValue: _i6.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #generateRecoveryCode,
            [],
          ),
        )),
      ) as _i6.Future<String>);

  @override
  _i6.Future<bool> hasGeneratedRecoveryCode() => (super.noSuchMethod(
        Invocation.method(
          #hasGeneratedRecoveryCode,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i4.CloudBackupService getCloudBackupService() => (super.noSuchMethod(
        Invocation.method(
          #getCloudBackupService,
          [],
        ),
        returnValue: _FakeCloudBackupService_2(
          this,
          Invocation.method(
            #getCloudBackupService,
            [],
          ),
        ),
      ) as _i4.CloudBackupService);

  @override
  _i6.Future<void> markRecoveryCodeAsSaved() => (super.noSuchMethod(
        Invocation.method(
          #markRecoveryCodeAsSaved,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  String getRecoveryCode() => (super.noSuchMethod(
        Invocation.method(
          #getRecoveryCode,
          [],
        ),
        returnValue: _i10.dummyValue<String>(
          this,
          Invocation.method(
            #getRecoveryCode,
            [],
          ),
        ),
      ) as String);

  @override
  _i6.Future<void> resetRecoveryKey() => (super.noSuchMethod(
        Invocation.method(
          #resetRecoveryKey,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<DateTime?> extractDateFromCloudBackupFilename(String? filename) =>
      (super.noSuchMethod(
        Invocation.method(
          #extractDateFromCloudBackupFilename,
          [filename],
        ),
        returnValue: _i6.Future<DateTime?>.value(),
      ) as _i6.Future<DateTime?>);

  @override
  _i6.Future<bool> restoreFromRecoveryKey(String? recoveryKey) =>
      (super.noSuchMethod(
        Invocation.method(
          #restoreFromRecoveryKey,
          [recoveryKey],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> applyTransferData(String? transferData) =>
      (super.noSuchMethod(
        Invocation.method(
          #applyTransferData,
          [transferData],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>> getTransferData() => (super.noSuchMethod(
        Invocation.method(
          #getTransferData,
          [],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<void> clearAllData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> autoBackupToCloudIfEnabled() => (super.noSuchMethod(
        Invocation.method(
          #autoBackupToCloudIfEnabled,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> migrateTimeEntries() => (super.noSuchMethod(
        Invocation.method(
          #migrateTimeEntries,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

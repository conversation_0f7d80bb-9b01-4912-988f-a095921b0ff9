# Custom Date Format Override Feature

## Overview
The Time Tracker Flutter app now supports custom date format overrides, allowing users to specify their preferred date format instead of relying solely on system locale settings.

## How to Use

### 1. Enable Custom Date Format
1. Open the Settings screen
2. Navigate to the "Date Format" section
3. Toggle "Override System Date Format" to enable custom formatting
4. Tap "Custom Date Format" to select your preferred format

### 2. Available Format Options
The date format picker provides several preset options:
- **US Format**: MM/dd/yyyy (03/15/2024)
- **European Format**: dd/MM/yyyy (15/03/2024)
- **ISO Format**: yyyy-MM-dd (2024-03-15)
- **Long Format**: MMMM d, yyyy (March 15, 2024)
- **Medium Format**: MMM d, yyyy (Mar 15, 2024)
- **Short Format**: M/d/yy (3/15/24)
- **Day First**: d MMM yyyy (15 Mar 2024)
- **Weekday Format**: EEE<PERSON>, MMMM d, yyyy (Friday, March 15, 2024)
- **Compact Format**: yyyyMMdd (20240315)
- **Dot Separated**: dd.MM.yyyy (15.03.2024)

### 3. Custom Format Patterns
You can also create your own custom format using these patterns:
- `y` - Year (2024)
- `yy` - Year (24)
- `M` - Month (3)
- `MM` - Month (03)
- `MMM` - Month (Mar)
- `MMMM` - Month (March)
- `d` - Day (5)
- `dd` - Day (05)
- `E` - Weekday (Fri)
- `EEEE` - Weekday (Friday)

## Implementation Details

### Database Storage
Custom date format preferences are stored in the Hive settings box with these keys:
- `useCustomDateFormat` (bool) - Whether custom formatting is enabled
- `customDateFormat` (String) - The custom format pattern

### API Methods
The `DatabaseService` provides these methods:
```dart
// Enable/disable custom date format
await databaseService.setUseCustomDateFormat(true);
bool isEnabled = await databaseService.isCustomDateFormatEnabled();

// Set/get custom format pattern
await databaseService.setCustomDateFormat('dd/MM/yyyy');
String? format = await databaseService.getCustomDateFormat();
```

### Usage in Code
To use custom date formatting in your widgets:

```dart
// For async contexts (automatically uses custom format when enabled)
String formattedDate = await LocaleDateUtils.formatDateAsync(
  DateTime.now(), 
  locale
);

// With context (recommended)
String formattedDate = await formatDateWithContextAsync(
  DateTime.now(), 
  context
);

// For date ranges
String dateRange = await DateFormatUtils.formatDateRangeWithContextAsync(
  startDate, 
  endDate, 
  period, 
  context
);

// For tooltips
String tooltipDate = await DateFormatUtils.formatTooltipDateWithContextAsync(
  date, 
  period, 
  context
);
```

### Synchronous vs Asynchronous Methods
- **Synchronous methods** (e.g., `formatDate()`) - Use system locale only
- **Asynchronous methods** (e.g., `formatDateAsync()`) - Automatically check for custom format preferences

### Automatic Custom Format Integration
The async methods automatically:
1. Check if custom date format is enabled
2. Use the custom format if available and valid
3. Fall back to system locale format if custom format is disabled or invalid

This means you can simply replace synchronous calls with async ones to get custom format support without any additional logic.

### Fallback Behavior
- If custom formatting is disabled, the app uses system locale formatting
- If a custom format is invalid, the app falls back to the default locale format
- The preview in settings shows "Invalid format pattern" for malformed patterns

## Benefits
1. **User Control**: Users can choose their preferred date format regardless of system locale
2. **Consistency**: Ensures consistent date display across the entire app
3. **Flexibility**: Supports both preset formats and completely custom patterns
4. **Safety**: Graceful fallback to system locale if custom format fails

## Technical Notes
- Custom formats are validated in real-time with a preview
- The format picker includes helpful pattern documentation
- All existing date formatting continues to work unchanged when custom formatting is disabled
- The feature integrates seamlessly with the existing locale-aware date formatting system 
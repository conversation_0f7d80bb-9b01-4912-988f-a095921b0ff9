# Time Tracker Flutter

A comprehensive time tracking application built with Flutter, featuring location-based tracking and cloud backup capabilities.

## Features

### Core Features
- **Project Management**: Create, edit, and manage projects
- **Time Tracking**: Manual time entry with start/stop functionality
- **Location-Based Tracking**: Automatic time tracking when entering/leaving designated areas
- **Data Export**: Export time entries to CSV format
- **Backup & Restore**: Local and cloud backup capabilities

### Cloud Backup Features
- **End-to-End Encryption**: All backups are encrypted using AES-GCM encryption
- **WebCRYPTO Compatibility**: Supports older WebCRYPTO backups for seamless migration
- **Recovery Codes**: Secure recovery codes for cross-device data access
- **QR Code Transfer**: Easy device-to-device data transfer via QR codes
- **Version Compatibility**: Backward compatibility with older backup formats

### Location Tracking
- **Geofenced Areas**: Define circular areas for automatic time tracking
- **Background Monitoring**: Continues tracking in the background
- **Cooldown Periods**: Configurable cooldown to prevent rapid start/stop cycles
- **Multiple Projects**: Support for multiple location-based tracking areas

## Cloud Backup Compatibility

The application now supports enhanced backup compatibility:

### Schema Versions
- **Version 1**: Original backup format (projects + time entries)
- **Version 2**: Extended format (projects + time entries + location areas)

### Encryption Support
- **WebCRYPTO Format**: Compatible with older WebCRYPTO-encrypted backups
- **Legacy JSON Format**: Support for CBC-encrypted backups
- **Modern AES-GCM**: New backups use AES-GCM encryption for better security

### Migration Features
- Automatic detection of backup format versions
- Seamless restoration from older backup formats
- Preservation of all data during format upgrades
- Warning messages for older schema versions

## Installation

1. Ensure Flutter is installed on your system
2. Clone this repository
3. Run `flutter pub get` to install dependencies
4. Build for your target platform:
   - Linux: `flutter build linux`
   - Android: `flutter build apk`
   - iOS: `flutter build ios`

## Dependencies

Key dependencies include:
- `hive_flutter`: Local database storage
- `supabase_flutter`: Cloud storage backend
- `crypto` & `encrypt`: Encryption capabilities
- `geolocator` & `flutter_map`: Location services
- `flutter_foreground_task`: Background location tracking

## Usage

### Basic Time Tracking
1. Create a new project
2. Start tracking time manually or set up location areas
3. View and edit time entries
4. Export data as needed

### Location-Based Tracking
1. Enable location permissions
2. Create location areas linked to projects
3. Enable tracking for specific areas
4. Time tracking starts/stops automatically when entering/leaving areas

### Cloud Backup
1. Generate a recovery code for your device
2. Save the recovery code securely
3. Backups are automatically encrypted and uploaded
4. Use recovery codes or QR transfer to restore on new devices

## Security Features

- **AES-256 Encryption**: All cloud backups are encrypted
- **Key Derivation**: Secure key derivation from user credentials
- **Recovery Codes**: Human-readable recovery codes for data access
- **Hash Verification**: Integrity verification for all backup operations

## Architecture

The application uses a clean architecture pattern with:
- **Models**: Data structures for projects, time entries, and location areas
- **Services**: Business logic and data access layers
- **Controllers**: State management using Riverpod
- **Screens**: UI components and user interactions

## License

This project is licensed under the MIT License - see the LICENSE file for details.

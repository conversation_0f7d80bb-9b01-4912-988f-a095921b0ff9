<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="256" height="256" viewBox="0 0 256 256" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer circle with gradient -->
  <defs>
    <linearGradient id="outerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6200EA" />
      <stop offset="100%" stop-color="#03DAC5" />
    </linearGradient>
  </defs>

  <circle cx="128" cy="128" r="120" fill="url(#outerGradient)" />

  <!-- Inner circle with subtle gradient -->
  <circle cx="128" cy="128" r="100" fill="#FFFFFF" />

  <!-- Clock face markings -->
  <circle cx="128" cy="128" r="90" fill="none" stroke="#E0E0E0" stroke-width="2" />

  <!-- Center point -->
  <circle cx="128" cy="128" r="8" fill="#FF5252" />

  <!-- Hour markers -->
  <line x1="128" y1="48" x2="128" y2="60" stroke="#6200EA" stroke-width="4" stroke-linecap="round" />
  <line x1="208" y1="128" x2="196" y2="128" stroke="#6200EA" stroke-width="4" stroke-linecap="round" />
  <line x1="128" y1="208" x2="128" y2="196" stroke="#6200EA" stroke-width="4" stroke-linecap="round" />
  <line x1="48" y1="128" x2="60" y2="128" stroke="#6200EA" stroke-width="4" stroke-linecap="round" />

  <!-- Diagonal hour markers -->
  <line x1="168" y1="88" x2="160" y2="96" stroke="#6200EA" stroke-width="3" stroke-linecap="round" />
  <line x1="168" y1="168" x2="160" y2="160" stroke="#6200EA" stroke-width="3" stroke-linecap="round" />
  <line x1="88" y1="168" x2="96" y2="160" stroke="#6200EA" stroke-width="3" stroke-linecap="round" />
  <line x1="88" y1="88" x2="96" y2="96" stroke="#6200EA" stroke-width="3" stroke-linecap="round" />

  <!-- Hour hand -->
  <line x1="128" y1="128" x2="128" y2="78" stroke="#6200EA" stroke-width="6" stroke-linecap="round">
    <animate attributeName="transform" attributeType="XML" type="rotate" from="0 128 128" to="360 128 128" dur="12h" repeatCount="indefinite" />
  </line>

  <!-- Minute hand -->
  <line x1="128" y1="128" x2="170" y2="128" stroke="#03DAC5" stroke-width="4" stroke-linecap="round">
    <animate attributeName="transform" attributeType="XML" type="rotate" from="0 128 128" to="360 128 128" dur="60min" repeatCount="indefinite" />
  </line>

  <!-- Second hand -->
  <line x1="128" y1="128" x2="128" y2="65" stroke="#FF5252" stroke-width="2" stroke-linecap="round">
    <animate attributeName="transform" attributeType="XML" type="rotate" from="0 128 128" to="360 128 128" dur="60s" repeatCount="indefinite" />
  </line>
</svg>